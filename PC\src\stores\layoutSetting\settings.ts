// store/settings.ts
import { defineStore } from 'pinia';


interface SettingsState {
//   showSettings: boolean;
//   fixedHeader: boolean;
//   sidebarLogo: boolean;
  tagsViews: any;
}

export const useSettingsStore = defineStore('settings', {
  state: (): SettingsState => ({
    // showSettings: defaultSettings.showSettings,
    // fixedHeader: defaultSettings.fixedHeader,
    // sidebarLogo: defaultSettings.sidebarLogo,
    tagsViews:'',

  }),
  actions: {
    changeSetting(key: keyof SettingsState, value: any) {
      if (key in this.$state) {
        this.$state[key] = value;
      }
    },

  },
});
