<template>
    <div class=''>
        <div class="card-header demo-form-inline">
            <div class="card-label">派单回单统计</div>
            <el-input v-model="area" style="width: 200px" placeholder="请输入部门或分局" clearable>
                <template #prefix>
                    <el-icon class="el-input__icon">
                        <search />
                    </el-icon>
                </template>
            </el-input>

  
        </div>


        <div class="card-body">
            <div class="list">
                <div class="list-item list-item-total">
                    <div class="list-item-title">总派单量</div>
                    <div class="list-item-value">{{ formatNumber(statistics.total) }}</div>
                </div>
                <div class="list-item list-item-finished">
                    <div class="list-item-title">已回单数量</div>
                    <div class="list-item-value">{{ formatNumber(statistics.receiptCount) }}</div>
                </div>
                <div class="list-item list-item-unfinished">
                    <div class="list-item-title">未回单数量</div>
                    <div class="list-item-value">{{ formatNumber(statistics.unReceiptCount) }}</div>
                </div>
                <div class="list-item list-item-rate">
                    <div class="list-item-title">回单完成率</div>
                    <div class="list-item-value">{{ statistics.completionRate }}%</div>
                </div>
            </div>
            <div class="mt-35" style="display: flex;justify-content: space-between;">
                <el-radio-group v-model="receiptType" size="large" @change="loadReceiptStatisticsList">
                    <el-radio-button label="全部人员" value="" />
                    <el-radio-button label="已回单" value="2" />
                    <el-radio-button label="未回单" value="1" />
                </el-radio-group>


                <div class="btnGroup">
                    <el-button size="large" class="icon-xiazaishuju"
                        @click="exportReceiptStatisticsList">导出数据</el-button>
                    <el-button size="large" class="icon-shuaxin" @click="loadReceiptStatisticsList">刷新数据</el-button>
                </div>

            </div>

            <el-table :data="tableData" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)"
                class="customTable mt-20" style="min-width: 100%; min-height: 200px" stripe highlight-current-row
                :row-key="(row: any) => row.ticketNo">
                <el-table-column prop="staffName" label="人员姓名" align="center" width="120" />
                <el-table-column prop="ticketNo" label="单号" align="center" />
                <el-table-column prop="customerName" label="客户名称" align="center" />

                <el-table-column prop="departmentName" label="所属部门" align="center" />
                <el-table-column prop="createTime" label="派单时间" align="center" />
                <el-table-column prop="receiptStatus" label="回单状态" align="center">
                    <template #default="{ row }">
                        <span :class="[
                            'receipt-status',
                            row.status == '2' ? 'receipt-status-success' : 'receipt-status-danger'
                        ]">
                            {{ row.status == '2' ? '已回单' : '未回单' }}
                        </span>
                    </template>
                </el-table-column>
                <el-table-column prop="receiptTime" label="回单时间" align="center" />
            </el-table>

            <el-pagination class="customPagination" @size-change="handleSizeChange" prev-icon="DArrowLeft"
                next-icon="DArrowRight" @current-change="handleCurrentChange" :page-sizes="[10, 50, 100, 200]"
                background :page-size="pageSize" :current-page="pageNumber" :pager-count="5"
                layout="prev, pager, next,sizes,jumper,total" :total="total"
                style="margin: 40px 60px 20px 0px; justify-content: end">
            </el-pagination>

        </div>
    </div>
</template>

<script setup lang='ts'>
import { ref, reactive, onMounted, onUnmounted, computed, toRefs } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { getReceiptStatisticsCountAPI, getReceiptStatisticsListAPI, exportReceiptStatisticsListAPI } from '@/api/activity';

interface ReceiptStatistics {
    receiptCount: number;      // 已回单数量
    total: number;      // 总派单量
    unReceiptCount: number;    // 未回单数量
    completionRate: number;    // 回单完成率
}

type AreaOptionList = {
    value: string;
    label: string;
}

const router = useRouter();
const route = useRoute();

const statistics = reactive<ReceiptStatistics>({
    receiptCount: 0,
    total: 0,
    unReceiptCount: 0,
    completionRate: 0
});

const area = ref<string>('');

const receiptType = ref('')
const pageNumber = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

const tableData = ref([]);

function handleSizeChange(val: number) {
    pageSize.value = val;
    loadReceiptStatisticsCount()
    loadReceiptStatisticsList()
}
function handleCurrentChange(val: number) {
    pageNumber.value = val;
    loadReceiptStatisticsCount()
    loadReceiptStatisticsList()


}

function formatNumber(num: number): string {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

function loadReceiptStatisticsCount() {
    let params = {
        departId: area.value,
        collectionId: route.query.id
    }
    getReceiptStatisticsCountAPI(params).then((res: any) => {
        if (res.code == 200) {
            statistics.total = res.data.total ?? 0
            statistics.receiptCount = res.data.receiptCount ?? 0
            statistics.unReceiptCount = res.data.unReceiptCount ?? 0
            statistics.completionRate = statistics.total == 0 ? 0 : Number(((statistics.receiptCount / statistics.total) * 100).toFixed(2))
        }
    })
}

function loadReceiptStatisticsList() {
    let params = {
        area: area.value,
        status: receiptType.value,
        collectionId: route.query.id,
        pageNumber: pageNumber.value,
        pageSize: pageSize.value
    }
    loading.value = true
    getReceiptStatisticsListAPI(params).then((res: any) => {
        if (res.code == 200) {
            tableData.value = res.data.records
        }
    }).finally(() => {
        loading.value = false
    })
}

function exportReceiptStatisticsList() {
    let params = {
        area: area.value,
        status: receiptType.value,
        collectionId: route.query.id,
    }
    exportReceiptStatisticsListAPI(params).then(async (res: any) => {

        const text = await res.text();

        try {
            const json = JSON.parse(text);
            // 如果能解析成 JSON，说明是错误信息
            if (json.code !== 200) {
                ElMessage.error(json.msg || '下载失败');
                return;
            }
        } catch (e) {
            // 直接使用响应数据创建Blob
            const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
            // 创建下载链接
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.download = '派单回单统计.xls';
            // 触发下载
            document.body.appendChild(link);
            link.click();
            // 清理
            document.body.removeChild(link);
            window.URL.revokeObjectURL(link.href);
            ElMessage.success('下载成功');
        }

    })
}


const loadmore = () => {
    console.log(12123);
}

// 生命周期钩子
onMounted(() => {
    loadReceiptStatisticsList()
    loadReceiptStatisticsCount()
});

onUnmounted(() => {

});

</script>

<style lang='scss' scoped>
.card-body {
    .list {
        display: flex;
        gap: 25px;
    }

    .list-item {
        flex: 1;
        border-radius: 12px;
        padding: 24px 16px;

    }

    .list-item-total {
        background: #eaf3fe;

        .list-item-value {
            color: #2176ff;
        }
    }

    .list-item-finished {
        background: #eafaf3;

        .list-item-value {
            color: #1dbb4c;
        }
    }


    .list-item-unfinished {
        background: #fef3f3;

        .list-item-value {
            color: #f23d3d;
        }
    }


    .list-item-rate {
        background: #f5f3fe;

        .list-item-value {
            color: #7c3aed;
        }
    }


    .list-item-title {
        color: #3f4254;
        font-size: 18px;
        margin-bottom: 8px;
    }

    .list-item-value {
        font-size: 28px;
        font-weight: bold;
    }
}

:deep(.el-radio-group) {
    .el-radio-button__inner {
        font-weight: 700;
    }
}

.receipt-status {
    display: inline-block;
    padding: 2px 10px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
}

.receipt-status-success {
    background: #eafaf3;
    color: #1dbb4c;
}

.receipt-status-danger {
    background: #fef3f3;
    color: #f23d3d;
}
</style>