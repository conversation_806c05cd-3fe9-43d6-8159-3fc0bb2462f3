import { DirectiveBinding, VNode } from 'vue';


const imgPreview = {
    install: ((app: any) => {
        app.directive('imgPreview', (el: any, binding: any) => {
            el.onclick = (e: Event) => {
                e.stopPropagation()
                let [list, index] = binding.value
                showImagePreview({
                    images: list,
                    startPosition: index,
                    closeable: true,
                });
            }
        });
    })
}


// const imgPreview = {
//     install: (app: any) => {
//         app.directive('imgPreview', {
//             // 使用 mounted 生命周期钩子代替 onMounted
//             mounted(el: HTMLElement, binding: DirectiveBinding, vnode: VNode) {
//                 // 绑定点击事件处理图片预览
//                 el.onclick = () => {
//                     let [list, index] = binding.value;
//                     list = list.map((item: any) => item.url);
//                     showImagePreview({
//                         images: list,
//                         startPosition: index,
//                         closeable: true,
//                     });
//                 };

//                 // 这里的 log 用于调试，可以移除
//                 console.log(el, binding, vnode);
//             },
//         });
//     },
// };

export default imgPreview;
