<template>
    <div class="card">
        <div class="card-header">
            <div>
                <img src="@/assets/orderType.svg" alt="" srcset="">
                <div>{{ route.query.typeName }}过程专项

                    <van-popover placement="bottom">
                        <div class="popoverBody">
                            <template v-if="curSelectType == 0">
                                <li>每日18:00前展示T-3数据，18:00后更新T-2数据</li>
                                <li> 日完成=当日加约率</li>
                                <li>累计完成=截止到选中的日期的总加约率</li>
                                <li>累计完成率=累计完成/6%</li>
                            </template>
                            <template v-else-if="curSelectType == 1">
                                <li> 数据均与派单详情一致</li>
                            </template>
                            <template v-else-if="curSelectType == 6">
                                <li>每日18:00前展示T-3数据，18:00后更新T-2数据</li>
                                <li>日完成=当日加装量</li>
                                <li>累计完成=截止到选中的日期的总加装量</li>
                                <li>累计完成率=总加装量/指标</li>
                            </template>
                        </div>
                        <template #reference>
                            <van-icon class="ml-3" name="question" color="#1A76FF" />
                        </template>
                    </van-popover>

                </div>
            </div>
            <cusTabs class="myTabs" :tabList="tabList" v-model:curSelectTab="curSelectType"
                @changeSelectTab="changeSelectTab">
            </cusTabs>
        </div>
        <template v-if="route.query.typeName == '提价值'">

            <div class="mt-8   card-body van-clearfix">
                <van-row gutter="5" class="table-header pt-10" align="center" >
                    <van-col class="row-item" :span="item.span" v-for="item in rowList">{{ item.name }}</van-col>
                </van-row>
                <van-row gutter="5" class="table-body" align="center" v-for="col, index in tableData" :key="index">
                    <van-col class="col-item" :span="item.span" v-for="item in rowList">{{ col[item.props] }}</van-col>
                </van-row>


            </div>
        </template>
        <template v-else>
            <div class="mt-8 card-body" v-for="item in tableData">

                <div class="card-body-header">{{ item.areaNameL2 || item.kangJiaName }}</div>

                <myCharts class="myEcharts" :options="getEchartsOption(item)"></myCharts>

            </div>
        </template>


        <van-empty :description="curSelectType == 1 ? '当前无框架派单' : '暂无信息'" class="mt-8"
            style="background-color: #fff;border-radius: 15px;" v-if="tableData.length == 0" />


    </div>
</template>

<script setup lang="ts">
import myCharts from "@/components/charts/myCharts.vue";
import cusTabs from "@/components/cusTabs/index.vue";
import { useRoute } from "vue-router";
import { getzqDetailAPI, getqyDetailAPI, getSendOrderDetailAPI } from "@/api/home";
interface Data {
    areaNameL2: string
    dayZb: number; //日指标
    dayDone: number;  // 日完成
    ljDone: number; //累计完成
    ljDoneRate: number ; //累计完成率
    [key: string]: any
}

const rowList = [
    {
        span: 6,
        name: '分局名称',
        props: 'areaNameL2',
        default: ''
    },

    {
        span: 3,
        name: '日指标',
        props: 'dayZb',
        default: ''

    },
    {
        span: 3,
        name: '日完成',
        props: 'dayDone',
        default: 0

    },


    {
        span: 4,
        name: '累计完成',
        props: 'ljDone',
        default: 0

    },
    {
        span: 5,
        name: '累计完成率',
        props: 'ljDoneRate',
        default: ''

    },
    {
        span: 3,
        name: '排名',
        props: 'rank',
        default: 0

    },

]






const route = useRoute()

const curSelectType = ref(Number(route.query.type) == 1 ? 0 : 6)
const curSelectAPI = ref(Number(route.query.type) == 1 ? getzqDetailAPI : getqyDetailAPI)


const tabList = computed(() => {
    if (Number(route.query.type) == 1) {
        return [
            { text: '筑墙', value: 0, API: getzqDetailAPI },
            { text: '防狼携转', value: 1, API: getSendOrderDetailAPI },
            // { text: '集约拆挽', value: 2 },
            // { text: '欠停赢回', value: 3 },

        ]
    } else {
        return [
            // { text: '千兆智慧家', value: 4 },
            // { text: '单进融', value: 5 },
            { text: '权益', value: 6, API: getqyDetailAPI },
        ]
    }
})


const tableData = ref<Data[]>([])


function changeSelectTab(params: any) {
    curSelectAPI.value = params.API
    tableData.value = []
    loadList()
}


function getEchartsOption(row: Data) {
    let data = []

    if (curSelectType.value == 1) {
        data = [
            { value: 0, name: '接触量', props: 'touchNum' },
            { value: 0, name: '接触率', props: 'touchRate' },
            { value: 0, name: '外呼量', props: 'outCallNum' },
            { value: 0, name: '外呼率', props: 'outCallRate' },
            { value: 0, name: '成功数', props: 'successResultNum' },
        ]
    } else {
        data = [
            { value: 0, name: '日指标', props: 'dayZb' },
            { value: 0, name: '日完成', props: 'dayDone' },
            { value: 0, name: '累计完成', props: 'ljDone' },
            { value: 0, name: '累计完成率', props: 'ljDoneRate' },
        ]
    }

    // 设置比例因子，将接触率和转化率的数值缩放
    let scaleFactor = 4; // 将接触率和转化率数据放大，使其适应 x 轴
    let mycolor = ['#1A76FF', '#27C272']
    let option = {
        grid: {
            left: "23%",
            top: "0%",
            right: '20%',
            bottom: "0%",
        },
        tooltip: {
            trigger: "item",
            formatter: function (params: any) {
                // 反向缩放，展示实际值
                let originalValue = 0
                if (curSelectType.value == 0) {
                    originalValue = params.value / scaleFactor
                } else if (curSelectType.value == 1) {
                    originalValue = params.name.includes('率') ? params.value / scaleFactor : params.value;
                } else {
                    originalValue = params.name.includes('率') ? params.value / scaleFactor : params.value;
                }
                // else if (curSelectType.value == 6) {
                //     originalValue = params.name == '累计完成率' ? params.value / scaleFactor : params.value;
                // } 


                // let originalValue = params.name.includes('率') ? params.value / scaleFactor : params.value;
                // return `<span style="color:${params.color}">${params.marker}${params.name}: ${originalValue}${params.name.includes('率') ? '%' : ''}</span>`;

                if (curSelectType.value == 0) {
                    // 筑墙打坝
                    return `<span style="color:${params.color}">${params.marker}${params.name}: ${originalValue}%</span>`;
                } else if (curSelectType.value == 1) {
                    return `<span style="color:${params.color}">${params.marker}${params.name}: ${originalValue}${params.name.includes('率') ? '%' : ''}</span>`
                } else {
                    return `<span style="color:${params.color}">${params.marker}${params.name}: ${originalValue}${params.name.includes('率') ? '%' : ''}</span>`
                }

                // else if (curSelectType.value == 6) {
                //     //权益
                //     return `<span style="color:${params.color}">${params.marker}${params.name}: ${originalValue}${params.name == '累计完成率' ? '%' : ''}</span>`
                // }
            }
        },
        xAxis:
        {
            show: false,
            type: "value",
            max: 400, // 设置接触量和转化量的最大值
        },


        yAxis: [
            {
                type: "category",
                data: data.map(item => item.name),
                axisLine: { show: false },
                axisTick: { show: false },
                axisLabel: {
                    color: "#3D3D3D ",
                    fontSize: 13,
                    show: true,
                    fontWeight: 500,
                    margin: 10,

                },
                inverse: true,
            },


            {
                type: 'category',
                inverse: true,
                axisTick: 'none',
                axisLine: 'none',
                show: true,
                axisLabel: {
                    align: 'left', // Align the labels to the left
                    formatter: function (params: any, index: any) {
                        if (curSelectType.value == 0) {
                            // 筑墙打坝
                            return `{color${index % 2}|${params}%}`;
                        } else if (curSelectType.value == 1) {
                            //防狼携转
                            return `{color${index % 2}|${params}${index % 2 == 0 ? '' : '%'}}`;
                        } else {
                            return `{color${index % 2}|${params}${index % 2 == 0 ? '' : '%'}}`;
                        }
                        // else if (curSelectType.value == 6) {
                        //     //权益
                        //     return `{color${index % 2}|${params}${index == 3 ? '%' : ''}}`
                        // } 


                    },
                    rich: {
                        color0: { color: mycolor[0], fontSize: 14 }, // For first label
                        color1: { color: mycolor[1], fontSize: 14 }, // For second label

                    },
                },
                data: data.map(item => ({ value: row[item.props] ?? 0, name: item.name, })
                )
            }
        ],
        series: [
            {
                name: "",
                type: "bar",
                data: data.map(item => {
                    return item.name.includes('率') ? row[item.props] * scaleFactor : row[item.props]
                }),
                showBackground: true,
                backgroundStyle: { color: "#EDF5FF", borderRadius: [8, 8, 8, 8] },
                barCategoryGap: 10, //间距

                barWidth: 10, //宽度
                itemStyle: {
                    normal: {
                        borderRadius: [20, 20, 20, 20],
                        color: function (d: any) {
                            return mycolor[d.dataIndex % 2];
                        },
                    },
                },
                yAxisIndex: 0, //定位层级，类似z-index
            },
        ],
    };
    return option
}




function loadList() {
    let params = {
        startTime: route.query.startTime ? `${route.query.startTime} 00:00:00` : '',
        endTime: route.query.endTime ? `${route.query.endTime} 23:59:59` : '',
        frameCode: 'flxz'
    }
    curSelectAPI.value(params).then((res: any) => {
        if (res.code == 200) {
            if (curSelectType.value == 1) {
                for (const item of res.data || []) {
                    item.outCallRate = item.dispatchNum == 0 ? 0 : Math.round((item.outCallNum / item.dispatchNum) * 100)
                    item.touchRate = item.dispatchNum == 0 ? 0 : Math.round((item.touchNum / item.dispatchNum) * 100)
                    item.transformRate = item.dispatchNum == 0 ? 0 : Math.round((item.transformNum / item.dispatchNum) * 100)
                }
            } else if (curSelectType.value == 6) {
                res.data = res.data.sort((A: Data, B: Data) => B.ljDoneRate - A.ljDoneRate)
                const set = [...new Set(res.data.map((item: any) => item.ljDoneRate))]
                for (const item of res.data) {
                    const index = set.findIndex(el => el == item.ljDoneRate)
                    item.rank = index + 1
                }


            }

            tableData.value = res.data || []
        }
    })



}

onMounted(() => {
    loadList()

})

</script>

<style lang="scss" scoped>
.card {
    font-family: 'Source Han Sans, Source Han Sans';
    min-height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
    font-family: Source Han Sans, Source Han Sans;

    .card-header {
        min-height: 46px;
        padding: 3px 10px;
        background: #FBFCFF;
        border-radius: 10px 10px 10px 10px;
        box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
        // padding-top: 10px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        justify-content: space-between;

        &>div:first-child {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 16px;
            color: #3D3D3D;
            line-height: 18px;

            img {
                margin-right: 8px;
            }

        }

        .myTabs {
            max-width: 100%;
            align-self: flex-end;
            flex: 1 0 auto;
        }



    }

    .card-body {
        background: #FBFCFF;
        box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
        background: #FBFCFF;
        border-radius: 10px 10px 10px 10px;

        .card-body-header {
            padding: 10px 20px;


            border-bottom: 1px solid #EEF5FF;
        }

        .myEcharts {

            width: 100%;
            height: 156px;
            padding: 10px 20px;
        }


    }


}

.popoverBody {
    font-size: 13px;
    padding: 5px 10px;
}





.row-item {
    font-weight: 400;
    font-size: 12px;
    color: #2079FF;
    text-align: center;
}

.table-header {
    border-bottom: 1px solid #EDF5FF;
    padding-bottom: 10px;
}

.table-body {
    border-bottom: 1px solid #EDF5FF;
    padding: 10px 0px;
}

.col-item {
    font-weight: 400;
    font-size: 12px;
    color: #3D3D3D;
    text-align: center;
}
</style>