<template>
    <div class="container_ gridPackage">
        <div class="container__header">
            <div>
                <img src="@/assets/comparativeCalculationAudit.svg" alt="" srcset="">
                <span class="ml-6">网格礼包</span>
            </div>

        </div>
        <div class="container_body">
            <van-search v-model="adderssName.text" @click="popoverIsShow = true" readonly placeholder="请输入查询地址"
                class="addressSearch">

                <template #left-icon>
                    <img class="mt-5" src="@/assets/search.svg" alt="" srcset="">

                </template>
            </van-search>

            <div class="package-list">
                <div v-for="(item, index) in packageList" :key="index" class="package-item">
                    <div class="package-header">
                        <span class="package-id">礼包ID: {{ item.pkgId }}</span>
                        <!-- <span class="package-price">¥{{ item.price }}</span> -->
                    </div>
                    <div class="package-content">
                        <div class="package-info">
                            <div class="package-title">{{ item.pkgName }}</div>
                            <!-- <div class="package-desc">{{ item.desc }}</div> -->
                        </div>
                    </div>
                </div>
                <van-empty :description="message ? message : '暂无礼包信息'" v-if="packageList.length == 0" />
            </div>

        </div>


        <van-popup v-model:show="popoverIsShow" round position="bottom" teleport="body">
            <van-picker title="地址列表" visible-option-num="8" :columns="addressList" :columns-field-names="{
                text: 'addressName',
                value: 'addressCode'
            }" @confirm="confirmAddressCode" @cancel="popoverIsShow = false">
                <template #title>
                    <van-search class="mt-5 mb-5" v-model="searchAddressName" placeholder="请输入搜索关键词"
                        @update:model-value="searchAddress" />
                </template>
            </van-picker>
        </van-popup>

    </div>
</template>

<script setup lang="ts">
import { getAddressAPI } from "@/api/common";
import { getqueryPkgListAPI } from "@/api/home";
import { debounce } from "lodash-es";
interface PackageInfo {
    gridId: string;
    gridName: string;
    pkgGrice: number | null;
    pkgId: string;
    pkgName: string;
}

const popoverIsShow = ref(false)
const adderssName = reactive({
    value: '',
    text: ''
})
const searchAddressName = ref('')
const addressList = ref([])
const confirmAddressCode = ({ selectedOptions }: any) => {
    adderssName.value = selectedOptions[0].addressCode
    adderssName.text = selectedOptions[0].addressName
    searchAddressName.value = selectedOptions[0].addressName
    // loadPkgList()
    if (addressList.value.length != 1) {
        searchAddress()
    } else {
        popoverIsShow.value = false
        loadPkgList()
    }


}

const packageList = ref<PackageInfo[]>([])

const searchAddress = debounce(() => {
    getAddressAPI({ address: searchAddressName.value, partitionId: "3" }).then((res: any) => {
        if (res.code == 200) {

            addressList.value = (JSON.parse(res.data)?.data?.address || []).map((item: any) => ({
                addressName: item.address,
                addressCode: item.addressId
            }))
        }


    })
}, 300)


const message = ref('')
const loadPkgList = () => {
    getqueryPkgListAPI({ locationId: adderssName.value }).then((res: any) => {
        if (res.code == 200) {
            packageList.value = res.data
        } else {
            packageList.value = []
            message.value = res.msg
            showToast({
                message: res.msg,
                duration: 20000
            })
        }
    })
}
</script>

<style lang="scss" scoped>
.gridPackage {
    min-height: 100%;
    background: #fff;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px;
}


:deep(.van-search.addressSearch) {
    padding: 0;

    .van-search__action {
        line-height: 40px;
    }

    .van-search__field {
        height: 40px;
    }

    .van-search__content {
        background-color: #EDF5FF;
        border-radius: 5px;
    }

    .van-field__value {
        height: 36px;
        line-height: 36px;
        font-weight: 400;
        font-size: 14px;
        color: #72ABFF;
    }

    .van-field__control {
        font-weight: 400;
        font-size: 14px;
        color: #72ABFF;
    }

    .van-field__control::placeholder {
        color: #72ABFF;
    }
}

:deep(.van-picker) {
    .van-search {
        flex: 1 1 auto;
    }
}

.package-list {
    // padding: 16px;
    margin-top: 15px;

    .package-item {
        background: #FFFFFF;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        border: 1px solid #EDF5FF;

        .package-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .package-id {
                color: #999;
                font-size: 12px;
            }

            .package-price {
                color: #67B6FA;
                font-size: 18px;
                font-weight: bold;
            }
        }

        .package-content {
            .package-info {
                .package-title {
                    font-size: 16px;
                    font-weight: 500;
                    color: #333;
                    font-weight: bold;
                    margin-bottom: 8px;
                    margin-top: 8px;
                }

                .package-desc {
                    font-size: 14px;
                    color: #666;
                    line-height: 1.5;
                }
            }
        }
    }
}
</style>