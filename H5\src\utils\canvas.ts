// 图片加水印
/**
 *
 * @param {File | String} image 图片文件或图片url地址
 * @param {String} text 水印文字
 * @param {String} font 字体属性
 * @param {String} fillStyle 字体颜色
 * @param {Number} rotate 旋转角度
 */

const imageType = ["image/png", "image/jpeg", "image/jpg", "image/webp"]
export async function addWaterMark(
  image,
  text = `拍照时间 ： ${getCurDay().year}-${getCurDay().month}-${getCurDay().date}  ${getCurDay().hour}:${getCurDay().min}`,
  font = "20px microsoft yahei",
  fillStyle = "rgba(0, 0, 0)",
  rotate = 0
) {
  let img;
  if (typeof image === "string") {
    img = await urlToImg(image);
  } else {
    if (imageType.includes(image.type)) {
      img = await blobToImg(image);
    } else {
      return Promise.reject("请上传png,jpeg,jpg,webp文件");
    }
  }
  let canvas = imgToCanvas(img);
  let blob = await watermark(canvas, text, font, fillStyle, rotate);
  let newImg = await blobToImg(blob);
  let src = newImg.src;
  let newFile = base64ToFile(src, "image.png");
  let newUrl = window.URL.createObjectURL(newFile);

  return { newFile, src, newUrl };
}

// 图片地址转image
function urlToImg(url) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.addEventListener("load", () => resolve(img));
    img.crossOrigin = "Anonymous";
    img.src = url;
    console.log(img.width, img.height, "cccc");
    // img.width = document.body.clientWidth
    img.objectFit = "contain";
  });
}

// 图片文件转image
function blobToImg(blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.addEventListener("load", () => {
      const img = new Image();
      img.addEventListener("load", () => resolve(img));
      img.crossOrigin = "Anonymous";
      img.src = reader.result;
      // img.width = document.body.clientWidth
    });
    reader.readAsDataURL(blob);
  });
}

// 图片转canvas
function imgToCanvas(img) {
  const canvas = document.createElement("canvas");
  canvas.width = img.width;
  canvas.height = img.height;
  console.log(img.width, img.height, "bbbbbbbbbb");
  const context = canvas.getContext("2d");
  context.drawImage(img, 0, 0);
  return canvas;
}

function base64ToFile(dataurl, filename = "image.png") {
  if (!dataurl) {
    return "";
  }
  let arr = dataurl.split(","),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n);
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  return new File([u8arr], filename, { type: mime });
}

// canvas 添加水印
function watermark(canvas, text, font, fillStyle, rotate) {
  return new Promise((resolve, reject) => {
    let context = canvas.getContext("2d");
    console.log(canvas.width, "vvvvv");
    let fontWidth = 16
    if (canvas?.width) {
      fontWidth = Math.floor(canvas.width / 40) * 2;
      font = `${fontWidth}px microsoft yahei`;
    }

    context.font = font;
    context.fillStyle = fillStyle;
    context.rotate((rotate * Math.PI) / 180);
    context.textAlign = "right";
    context.textBaseline = "Middle";
    const textWidth = context.measureText(text).width;
    context.fillText(text, canvas.width - 10, canvas.height - 10);
    canvas.toBlob((blob) => resolve(blob));
  });
}

function getCurDay() {
  var datetime = new Date();
  var year = datetime.getFullYear();
  var month =
    datetime.getMonth() + 1 < 10
      ? "0" + (datetime.getMonth() + 1)
      : datetime.getMonth() + 1;
  var date =
    datetime.getDate() < 10 ? "0" + datetime.getDate() : datetime.getDate();
  var hour = datetime.getHours()
  var min = datetime.getMinutes()
  return {
    year,
    month,
    date,
    hour,
    min
  };
}
