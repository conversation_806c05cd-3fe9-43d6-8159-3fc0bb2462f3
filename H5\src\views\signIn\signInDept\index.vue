<template>
    <div>
        <div class="card-header">
            <van-popover v-if="tabItem == 2" :actions="roleList" @select="changRole" placement="bottom-start">
                <template #reference>
                    <div class="selectOption-item">
                        <span>{{ curSelectRole.text }}</span>
                        <van-icon name="play" class="play" color="#1A76FF" />
                    </div>
                </template>
            </van-popover>
            <!-- <van-popover v-else :actions="areaList" @select="changArea" placement="bottom-start">
                <template #reference>
                    <div class="selectOption-item">
                        <span>{{ curSelectArea.text }}</span>
                        <van-icon name="play" class="play" color="#1A76FF" />
                    </div>
                </template>
            </van-popover> -->


            <cusTabs :tabList="tabList" v-model:curSelectTab="tabItem" @changeSelectTab="changeSelectTab">
            </cusTabs>
        </div>
        <div class=' container_ mt-6'>
            <div class="container__header">
                <div>
                    <img src="@/assets/resultOptimization.svg" alt="" srcset="">
                    <span class="ml-6">{{ title }}</span>
                </div>
            </div>


            <div class="container_body ">
                <div v-for="(item, index) in progressData" :key="index" class="item">

                    <div style="display: flex;align-items: center;justify-content: space-between;">
                        <div class="label">{{ item.name || item.roleName }}</div>
                        <div class="go-button" @click="GO(item)">
                            <span>GO</span>
                            <van-icon name="arrow" color="#fff" />
                        </div>
                    </div>
                    <div class="numbers">
                        <span>
                            <div>{{ item.totalCount }}</div>
                            <div>总人数</div>
                        </span>
                        <span>
                            <div>{{ item.signCount }}</div>
                            <div>出征数</div>
                        </span>
                        <span>
                            <div :class="['rate', item.rateClass]">{{ item.signRate }}%</div>
                            <div>出征率</div>
                        </span>
                    </div>


                </div>

            </div>




        </div>
    </div>
</template>

<script setup lang="ts">
import cusTabs from "@/components/cusTabs/index.vue";
import { getSignCountAreaAPI, getSignCountRoleAPI } from "@/api/morning";
import { getlistDeptOrSubOptions } from "@/hooks_modules/getlistDeptOrSubOptions";
import { getlistStaffRole } from "@/hooks_modules/getlistStaffRole";
import { useUserInfo } from "@/stores/userInfo";
import { useRoute, useRouter } from "vue-router";
import { mapSort } from "@/utils/mapSort";
const { areaList } = getlistDeptOrSubOptions()
const { roleList } = getlistStaffRole()
const { userInfo } = useUserInfo()

interface SelectType {
    text: string,
    value: number | string | undefined
}
interface Progress {
    name?: string;
    roleName?: string
    areaId: string
    signCount: number;
    totalCount: number;
    signRate: number;
    rateClass: string;
    [key: string]: any
}


const router = useRouter()
const progressData = ref<Progress[]>([])

const tabList = ref([
    { text: '条线', value: 2 },
    { text: '分局', value: 1 },
])



function changRole(params: any) {
    curSelectRole.text = params.text
    curSelectRole.value = params.value
    // 此时代表 查询的是分局 或区域

    loadSignCountArea(1)




}



const curSelectRole = reactive<SelectType>({
    text: '全部',
    value: undefined
})
const curSelectArea = reactive<SelectType>({
    text: '全部',
    value: undefined
})







//当前选择的是 分局 条线 还是分局
const tabItem = ref(2)
function changeSelectTab() {

    curSelectArea.value = undefined
    curSelectArea.text = '全部'

    curSelectRole.value = undefined
    curSelectRole.text = '全部'
    //当切换tab的时候 一切清空 默认查询条线
    searchType.value = 2
    if (tabItem.value == 2) {
        loadSignCountRole()
    } else {
        loadSignCountArea(1)

    }
}




//记录当前查询的是哪一个 默认查询的是条线
//0 区域 1 分局 2条线 
const searchType = ref(2)


function GO(item: Progress) {

    if (item.type == 2) {
        curSelectRole.value = item.roleCode
        curSelectRole.text = item.roleName || ''

    } else if (item.type == 0) {
        curSelectArea.value = item.areaId
        curSelectArea.text = item.areaName || ''
    } else if (item.type == 1) {
        router.push({
            path: '/signInBranchDetail',
            query: {
        
                areaIdL2: item.areaIdL2,
                areaNameL2: item.areaNameL2,
            }
        })
        return
    }
    searchType.value = 1
    loadSignCountArea(1)

}



function loadSignCountArea(type?: number) {
    let params: any = {
        type: type ?? tabItem.value,
        areaIdL2: curSelectArea.value,
        staffRoleCode: curSelectRole.value
    }
    getSignCountAreaAPI(params).then((res: any) => {
        if (res.code == 200) {
            for (const item of res.data) {
                if (item.signRate <= 65 && item.signRate > 0) {
                    item.rateClass = 'warning'
                } else if (item.signRate <= 0) {
                    item.rateClass = 'danger'
                }
            }
            progressData.value = res.data || []
        }
    })
}


//条线时才调这个接口
function loadSignCountRole() {
    getSignCountRoleAPI({ staffRoleCode: curSelectRole.value }).then((res: any) => {
        if (res.code == 200) {
            for (const item of res.data) {

                if (item.signRate <= 65 && item.signRate > 0) {
                    item.rateClass = 'warning'
                } else if (item.signRate <= 0) {
                    item.rateClass = 'danger'
                }
            }
            res.data = mapSort(res.data, 'roleName')
            progressData.value = res.data || []


        }
    })
}










const title = computed(() => {
    let title = ''
    let areaName = curSelectArea.text == '全部' ? '' : curSelectArea.text
    let roleName = curSelectRole.text == '全部' ? '' : curSelectRole.text
    const orgName = userInfo.orgName


    if (tabItem.value == 2) {
        title = roleName == '' ? `${orgName}分公司各条线晨会出征完成情况` : `${orgName}分公司${roleName}晨会出征完成情况`
    } else {
        title = areaName == '' ? `${orgName}分公司各分局晨会出征完成情况` : `${orgName}分公司${areaName}晨会出征完成情况`
    }




    return title
})
onMounted(() => {
    loadSignCountRole()
})
</script>

<style lang="scss" scoped>
.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 700;
        font-size: 16px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

    .selectOption-item {
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;
        // justify-content: space-between;
        position: relative;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
            position: absolute;
            right: 10px;
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }
}









.item {
    padding: 7px 14px;
    background: #EDF5FF;
    border-radius: 5px;
    margin-bottom: 8px;

    &:last-child {
        border-bottom: none;
    }

    .label {
        font-weight: 700;
        font-size: 14px;
        color: #3D3D3D;
    }

    .numbers {
        display: flex;
        align-items: center;
        justify-content: space-around;
        margin-top: 8px;

        span {
            display: flex;
            flex-direction: column;
            align-items: center;

            &>div:first-child {
                font-weight: 700;
                font-size: 18px;
                color: #3D3D3D;
            }

            &>div:last-child {
                margin-top: 2px;
                font-weight: 400;
                font-size: 12px;
                color: #3D3D3D;
            }
        }

        .rate {
            font-weight: bold;

            &.warning {
                font-size: 18px;
                color: #FF6A1A;
            }

            &.danger {
                color: red;
            }
        }
    }
}



:deep(.van-popover__wrapper) {
    flex: 1 0 auto;
}

:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
    width: auto;
}
</style>