<template>
  <div>
    <div class="container_">
      <div class="container__header" style="border-bottom: 0px;">
        <div>
          <img src="./assets//communityBattle.png" alt="" srcset="">
          <span class="ml-6">网格管理详情</span>
        </div>

        <div class="selectOption">
          <van-popover :actions="areaList" @select="changArea" placement="bottom">
            <template #reference>
              <div class="selectOption-item">
                <span>{{ curSelectArea.text }}</span>
                <van-icon name="play" class="play" color="#1A76FF" />
              </div>
            </template>
          </van-popover>
        </div>

      </div>


      <div class="container_body ">
        <div class="body_box">
          <div class="box-item">
            <div style="color: #3A83FC;">{{ gridManagementDetails.totalGrids }}</div>
            <div>督办网格数</div>
          </div>
          <div class="box-item">
            <div style="color: #34C759;">{{ gridManagementDetails.feedbackCount }}</div>
            <div>已检查网格</div>
          </div>
          <div class="box-item">
            <div style="color: #FF9760;">{{ gridManagementDetails.noFeedbackCount }}</div>
            <div>未检查网格</div>
          </div>
        </div>
        <div class="mt-18">
          <div class=" mb-2 fs12">反馈进度:</div>
          <div style="display: flex;align-items: center;">
            <van-progress style="flex: 1 0 auto;" track-color="#F4F4F4" :show-pivot="false" stroke-width="6"
              color="#1A76FF" :percentage="gridManagementDetails.feedbackProgress" />
            <div style="color: #3A83FC;" class="ml-10 fs12">{{ gridManagementDetails.feedbackProgress }}%</div>
          </div>
        </div>




      </div>
    </div>
    <div class="container_ mt-15">
      <div class="container__header" style="border-bottom: 0px;">
        <div>
          <span>宣传检查统计（已检查网格）</span>
        </div>
      </div>
      <div class="container_body ">
        <myCharts :options="option" class="myEcharts" />
      </div>
    </div>

    <div class="container_  mt-15">
      <div class="container__header" style="border-bottom: 0px;">
        <div>
          <img src="./assets/count.png" alt="" srcset="">
          <span class="ml-6">分局统计</span>
        </div>
        <van-search v-model="searchName" :show-action="false" placeholder="请选择查询对象">

          <template #left-icon>
            <img class="mt-5" src="@/assets/search.svg" alt="" srcset="">

          </template>
        </van-search>
      </div>


      <div class="container_body">
        <div class="list-item" v-for="(item, index) in areaL2list_" :key="item.areaIdL2" @click="gotoDetile(item)">
          <h4>{{ item.areaNameL2 }}</h4>
          <div style="display: flex;align-items: center;" class="mt-5">
            <van-progress style="flex: 1 0 auto;" track-color="#fff" :show-pivot="false" stroke-width="6"
              color="#1A76FF" :percentage="item.gridRate" />
            <div style="color: #3A83FC;" class="ml-10 fs12">{{ item.gridRate }}%</div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface GridManagementDetails {
  totalGrids: number; // 暂办网格总数
  feedbackCount: number; // 有反馈数
  noFeedbackCount: number; // 无反馈数
  feedbackProgress: number; // 反馈进度，百分比
}
interface SelectType {
  text: string,
  value: number | string | undefined
}

import { getlistDeptOrSubOptions } from "@/hooks_modules/getlistDeptOrSubOptions";
import { getAreaDetailAPI, getAggAreaL2API, getPromotionEvaluationAPI } from "@/api/promoShots";
import { useRouter, useRoute } from "vue-router";
import myCharts from "@/components/charts/myCharts.vue";
const router = useRouter()
const route = useRoute()

const { areaList } = getlistDeptOrSubOptions({
  isPushAll: true
})


const curSelectArea = reactive<SelectType>({
  text: '全部',
  value: undefined
})

function changArea(params: any) {
  curSelectArea.text = params.text
  curSelectArea.value = params.value
  loadAreaDetail()
  loadAggAreaL2()
  initPromotionEvaluationAPI()
}


const gridManagementDetails = reactive<GridManagementDetails>({
  totalGrids: 0,
  feedbackCount: 0,
  noFeedbackCount: 0,
  feedbackProgress: 0, // 进度以百分比表示
});
const areaL2list = ref([])
const searchName = ref('')


const areaL2list_ = computed<{ areaIdL2: number, areaNameL2: string, gridNum: number, gridRate: number }[]>(() => {

  return areaL2list.value.filter((area: any) => area.areaNameL2.includes(searchName.value));
})




// 获取网格管理详情
function loadAreaDetail() {
  let params = {
    areaId: curSelectArea.value
  }

  getAreaDetailAPI(params).then((res: any) => {
    if (res.code == 200) {
      gridManagementDetails.totalGrids = res.data.gridTotal
      gridManagementDetails.feedbackCount = res.data.reactionNum
      gridManagementDetails.noFeedbackCount = res.data.unReactionNum
      gridManagementDetails.feedbackProgress = res.data.reactionRate
    }
  })

}
// 获取分局统计详情
function loadAggAreaL2() {
  let params = {
    areaId: curSelectArea.value
  }

  getAggAreaL2API(params).then((res: any) => {
    if (res.code == 200) {
      res.data.sort((a: any, b: any) => {
        return b.gridRate - a.gridRate
      })
      areaL2list.value = res.data
    }
  })
}
function gotoDetile(row: any) {
  router.push({
    path: '/inspectionLogMangement',
    query: {
      areaIdL2: row.areaIdL2,
      areaNameL2: row.areaNameL2
    }
  })
}




const promotionEvaluationList = ref<{ dictCode: string, dictName: string, total: string }[]>([])

function initPromotionEvaluationAPI() {
  getPromotionEvaluationAPI({ areaId: curSelectArea.value }).then((res: any) => {
    if (res.code == 200) {
      const index = res.data.findIndex((item: any) => item.dictCode == '0') //未评价
      if (index !== -1) {
        const removedItem = res.data.splice(index, 1)[0];
        res.data.push(removedItem);
      }
      promotionEvaluationList.value = res.data
    }
  })
}


const option = computed(() => {

  const color = ['#4caf50', '#91CC75', '#FAC857', '#EE6666', '#2196f3', '#a8a8a8'];
  var dataList = promotionEvaluationList.value.map((item, index) => {
    return {
      name: item.dictName,
      value: item.total,
    };
  });
  let option = {
    color: color,
    tooltip: {
      trigger: "item",
      formatter: function (params: any) {
        return `${params.marker} ${params.name} ${params.value}  ${params.percent}%`;
      },

    },
    legend: {
      orient: 'vertical',
      itemWidth: 18,
      itemHeight: 14,
      icon: 'roundRect',
      right: 10,
      bottom: '0%',
      data: dataList,
      textStyle: {
        fontSize: 14,
        padding: [0, 0, 0, 10],
      },

    },
    series: [
      // 展示层
      {
        type: 'pie',
        center: ['30%', '50%'],
        radius: ['40%', '75%'],
        // roseType: 'area',
        itemStyle: {
          borderRadius: 5, // 设置圆滑边角
          borderColor: '#fff', // 添加间隔颜色
          borderWidth: 1, // 设置间隔宽度
        },

        label: {
          show: false,
        },

        data: dataList,
      },
    ],
  }
  return option
})



onMounted(() => {
  curSelectArea.text = String(route.query.areaName)
  curSelectArea.value = String(route.query.areaId)

  loadAreaDetail()
  loadAggAreaL2()
  initPromotionEvaluationAPI()
})

</script>

<style lang="scss" scoped>
.selectOption {
  display: flex;
  align-items: center;

  & .selectOption-item {
    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
    font-weight: 400;
    font-size: 14px;
    color: #1A76FF;
    padding: 8px;
    display: flex;
    align-items: center;

    .play {
      transform: rotate(90deg);
      margin-left: 3px;
    }
  }

  & .selectOption-item:active {
    background: #d8dee6;
  }
}


.body_box {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .box-item {
    text-align: center;

    &>div:last-child {
      font-weight: 400;
      font-size: 12px;
    }

    &>div:first-child {
      font-weight: 500;
      font-size: 24px;
      line-height: 27px;
    }
  }
}

.list-item {
  background: #EDF5FF;
  border-radius: 5px;
  padding: 9px 9px 9px 9px;

  h4 {
    padding: 0px;
    margin: 0px;
    font-weight: 500;
    font-size: 14px;
    color: #217BFF;
    line-height: 16px;
  }
}

.list-item:not(:first-child) {
  margin-top: 10px;
}

.myEcharts {
  width: 100%;
  height: 150px;
}

:deep(.van-search__action) {
  line-height: 0;
}

:deep(.van-search__content) {
  background-color: #EDF5FF;
  border-radius: 5px;
  width: 180px;
}



:deep(.van-icon-search) {
  color: #1a76ff;

}


:deep(.van-field__control) {
  font-weight: 400;
  font-size: 14px;
  color: #72ABFF;
}

:deep(.van-field__control::placeholder) {
  color: #72ABFF;
}

:deep(.van-popover__wrapper) {
  flex: 1 0 auto;
}

:deep(.van-popover__content) {
  overflow-y: auto;
  max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
  width: auto;
}
</style>