<template>
    <div class="Home_" ref="home_">
        <component :is="item.moudule_" v-for="item in componentOption" :key="item.moudule_name"
            :ref="(el: HTMLDivElement) => item.ref_name && (refsArray[item.ref_name] = el)"
            v-on="generateEventHandlers(item.EventsList || [])"
            @="emitEventsListGenerateEventHandlers(item.emitEventsList || [])"
            v-bind="mergePropsAndVModelBindings(item.props, item.vModels)">
        </component>


        <div class="chatBi-box" v-if="isShowChatBi">
            <div class="left-box" :style="leftBoxPosition">
                <div class="left-text">有什么问题就来问我吧~</div>
            </div>

            <img src="@/assets/chatBi.png" alt="" srcset="" @click.native="gotoChatBi" v-draggable="offset">
        </div>



    </div>
</template>

<script lang="ts" setup>
import { useWindowSize } from '@vueuse/core'
import { Encrypt, Decrypt } from "@/utils/crypto/index";
import componentList from "@/views/template/config";
import { useUserInfo } from "@/stores/userInfo";
import { ref, reactive, onMounted, ComponentPropsOptions, ComponentOptions } from "vue";
import { EventsHooks, ComponentOptionProps } from "@/hooks_modules/Eventhooks";
import router from "@/router";
const { generateEventHandlers, emitEventsListGenerateEventHandlers, mergePropsAndVModelBindings } = EventsHooks()
const userInfoStore = useUserInfo();


const { width, height } = useWindowSize()


const offset = reactive({
    x: 300,
    y: 80
})

const leftBoxPosition = computed(() => {

    return {
        left: `${offset.x - 145}px`,
        top: `${offset.y - 20}px`
    }
})


const isShowChatBi = computed(() => {
    return userInfoStore.chatBiUrl
})


const refsArray = ref<{ [key: string]: HTMLDivElement | null }>({});
const componentOption: ComputedRef<ComponentOptionProps[]> = computed(() => {
    let list = userInfoStore.getMenuList.filter((item: MenuItem) => item.menuCode === 'home')[0]?.children || []
    const listOption = list.map((item: MenuItem) => {
        return {
            moudule_: componentList[item.menuCode].moudule_,
            moudule_name: item.menuCode,
            ref_name: item.menuCode,

        }
    })
    return listOption
})


watch(width, (newValue) => {

    offset.x = newValue - 90

}, { immediate: true })

function gotoChatBi() {
    router.push({ path: '/chatBi' })

}


onMounted(() => {
    console.log(Encrypt('NJGL0NJWY812'));
    // console.log(Decrypt('yEDKJFtmV4PTlbssua7btA=='));
    
    // console.log(Encrypt('NJ65010987'));

    

})




</script>


<style lang="scss" scoped>
.Home_ {



    .chatBi-box {
        .left-box {
            background-color: #fff;
            text-align: center;
            position: absolute;
            border-radius: 5px;
            padding: 5px 10px;
            width: 150px;
            font-size: 12px;
            animation: fadeInOut 5s forwards;

            .left-text {
                background: linear-gradient(0deg, #205BE4 0%, #8A1AE0 100%);
                -webkit-background-clip: text;
                background-clip: text;
                -webkit-text-fill-color: transparent;


            }

            .left-text::after {
                position: absolute;
                top: 22px;
                right: 0px;

                content: '';
                display: block;
                width: 20px;
                height: 10px;
                background-color: #fff;
                -webkit-clip-path: polygon(66% 0, 0 0, 85% 100%);
                clip-path: polygon(66% 0, 0 0, 85% 100%);
            }
        }



        img {
            width: 64px;
            margin-right: -40px
        }
    }

    @keyframes fadeInOut {
        0% {
            opacity: 0;
            display: none;
            transform: translateY(-30px);
            /* 初始位置可以设置为隐藏位置 */
        }

        30% {
            opacity: 1;
            display: block;
            transform: translateX(0);
            /* 中间展示时恢复到正常位置 */
        }

        70% {
            opacity: 1;
            transform: translateX(0);
            /* 中间展示时恢复到正常位置 */
        }

        100% {
            opacity: 0;
            display: none;
            transform: translateY(30px);
            /* 结束时可以稍微偏移 */
        }
    }


}
</style>