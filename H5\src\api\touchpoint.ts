
import request from "../utils/request";
import configAxios from './config'

//触点打卡保存
export function touchpointSaveAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/work/save`,
        method: "post",
        data,
    });
}



//触点打卡查询
export function touchpointListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/work/page`,
        method: "post",
        data,
    });
}
//触点打卡审核
export function touchpointAuditAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/work/audit`,
        method: "post",
        data,
    });
}

