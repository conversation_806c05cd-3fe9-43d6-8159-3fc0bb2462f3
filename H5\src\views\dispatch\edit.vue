<template>
    <div class='card container_'>
        <div class="container__header">
            <div>
                <img src="@/assets/comparativeCalculationAudit.svg" alt="" srcset="">
                <span class="ml-6">派单系统</span>
            </div>

            <i class="icon-shaixuan fs18" :style="{
                color: searchListIsShow ? '#1A76FF' : '#666'
            }" @click="searchListIsShow = !searchListIsShow"></i>

        </div>



        <collapseTransition>
            <div v-show="searchListIsShow">
                <div class="selectOption-item" @click="departmentPopoverIsShow = true">
                    <span class="van-ellipsis">{{ department.value ? department.text : '选择派单部门（IDC）' }} </span>
                    <van-icon name="play" class="play" color="#1A76FF" />
                </div>

                <div class="selectOption-item" @click="calendarIsShow = true">
                    <span class="van-ellipsis">{{ time ? time : '请选择时间' }}</span>
                    <van-icon name="play" class="play" color="#1A76FF" />
                </div>

            </div>

        </collapseTransition>
        <van-search v-model="collectionName" placeholder="请搜索派单主题" class="search">
            <template #left-icon>
                <img class="mt-5" src="@/assets/search.svg" alt="" srcset="" style="width: 17px;">
            </template>
        </van-search>
        <van-search v-model="searchName" placeholder="请搜索客户姓名" class="search" show-action>
            <template #left-icon>
                <img class="mt-5" src="@/assets/search.svg" alt="" srcset="" style="width: 17px;">
            </template>
            <template #action>
                <van-popover :actions="tabList" @select="changeSelectTab" placement="bottom">
                    <template #reference>
                        <div class="selectOption-item ">
                            <span>{{ curSelectType.text }}</span>
                            <van-icon name="play" class="play" color="#1A76FF" />
                        </div>
                    </template>
                </van-popover>
            </template>

        </van-search>
        <div style=" width: calc(100% - 18px - 18px); margin: 15px auto;" @click="confirmSearch">
            <van-button type="primary" color=" linear-gradient( 90deg, #73ACFF 0%, #237CFF 100%)" block>搜索</van-button>
        </div>


        <div class=" container_body ">
            <van-list v-model:loading="loading" :finished="finished"
                :finished-text="tableData.length > 0 ? '没有更多了' : ''" @load="onLoad">

                <div v-for="item, index in tableData" :key="item.id" class="list-item" :style="{
                    borderLeft: item.status == '1' ? '8px solid #ee9d0194' : '8px solid #7AA6FF'
                }">
                    <div v-if="route.query.type == '人员'" class="font">派单主题：{{ item.collectionName }}</div>
                    <div v-else class="font">客户名称：{{ item.customerName }}</div>
                    <div class="font">创建时间：{{ item.createTime }}</div>
                    <div class="form">
                        <div class="form-item" v-for="el in item.fieldsMetas" :key="el.id">
                            <template v-if="el.fieldType == 'input'">
                                <div style="display: flex;flex-direction: column;width: 100%;" class="mt-10">
                                    <div class="font">{{ el.fieldName }}:</div>
                                    <van-field :disabled="item.status == '2'" v-model="(el.modelValue as string)"
                                        :placeholder="el.fieldDesc" label-width="0" center />
                                </div>


                            </template>
                            <template v-else-if="el.fieldType == 'select'">
                                <div class="form_row mt-10">
                                    <span class="font">{{ el.fieldName }}:</span>
                                    <van-popover :actions="(el.fieldExt as PopoverAction[])" @select="(element) => {
                                        // el.fieldValue = element.text
                                        el.modelValue = element.text
                                    }" placement="bottom" v-if="item.status == '1'">
                                        <template #reference>
                                            <div class="selectOption-item ">
                                                <span>{{ el.modelValue ? el.modelValue : el.fieldDesc }}</span>
                                                <van-icon name="play" class="play" color="#1A76FF" />
                                            </div>
                                        </template>
                                    </van-popover>
                                    <div class="selectOption-item disable" v-else>
                                        <span>{{ el.modelValue ? el.modelValue : el.fieldDesc }}</span>
                                        <van-icon name="play" class="play" color="#1A76FF" />
                                    </div>

                                </div>





                            </template>
                            <template v-else-if="el.fieldType == 'date'">
                                <div class="form_row mt-10" style="width: 100%;">
                                    <span class="font ">{{ el.fieldName }}:</span>
                                    <div class="selectOption-item" :class="item.status == '2' ? 'disable' : ''"
                                        @click="el.showTimePicker = true" style="margin: 0;margin-top: 5px;">
                                        <span class="van-ellipsis">{{ el.modelValue ? el.modelValue : '请选择时间' }}</span>
                                        <van-icon name="play" class="play" color="#1A76FF" />
                                    </div>
                                </div>



                                <!-- 时间选择弹出层 -->
                                <van-popup v-model:show="el.showTimePicker" position="bottom" round
                                    v-if="el.fieldExt == 'datetime'">
                                    <van-picker-group v-model:active-tab="el.activeTimeTab" title="请选择时间"
                                        :disabled="item.status == '2'" :tabs="['选择日期', '选择时间']" @confirm="((params: any) => {


                                            const year = el.currentDate[0];
                                            const month = el.currentDate[1].padStart(2, '0');
                                            const day = el.currentDate[2].padStart(2, '0');
                                            const hour = el.currentTime[0].padStart(2, '0');
                                            const minute = el.currentTime[1].padStart(2, '0');

                                            el.modelValue = `${year}-${month}-${day} ${hour}:${minute}`;
                                            el.showTimePicker = false;
                                        })" @cancel="el.showTimePicker = false">
                                        <van-date-picker v-model="el.currentDate" :min-date="minDate"
                                            :max-date="maxDate_" />
                                        <van-time-picker v-model="el.currentTime" :min-hour="minHour"
                                            :max-hour="maxHour" :min-minute="minMinute" />
                                    </van-picker-group>


                                </van-popup>
                                <van-calendar v-model:show="el.showTimePicker" :default-date="defaultDate"
                                    :disabled="item.status == '2'" first-day-of-week="1" switch-mode="year-month"
                                    :allow-same-day="true" :type="el.fieldExt == 'date' ? 'single' : 'range'"
                                    :show-confirm="false" :max-date="maxDate" @confirm="(params: any) => {
                                        console.log(params);

                                        defaultDate.value = params
                                        if (Array.isArray(params)) {
                                            const startTime = formatDate(params[0], 'yyyy-MM-dd')
                                            const endTime = formatDate(params[1], 'yyyy-MM-dd')
                                            el.modelValue = startTime + ' ~ ' + endTime;
                                        } else {
                                            const startTime = formatDate(params, 'yyyy-MM-dd')
                                            el.modelValue = startTime;

                                        }


                                        el.showTimePicker = false;
                                    }" v-else />

                            </template>
                            <template v-else-if="el.fieldType == 'checkbox'">
                                <div class="mt-10 form_row mb-10">
                                    <div style="flex: 0  0 auto;" class="mr-10 font mb-5">{{ el.fieldName }}:</div>
                                    <van-checkbox-group v-model="(el.modelValue as Array<string>)"
                                        direction="horizontal" :disabled="item.status == '2'">
                                        <van-checkbox
                                            v-for="(el_, index) in Array.isArray(el.fieldExt) ? el.fieldExt : []"
                                            :key="index" :name="typeof el_ === 'object' ? el_.label : el_">
                                            {{ typeof el_ === 'object' ? el_.label : el_ }}
                                        </van-checkbox>
                                    </van-checkbox-group>
                                </div>


                            </template>

                        </div>
                        <div v-if="item.status == 1">
                            <van-button type="primary" color=" linear-gradient( 90deg, #73ACFF 0%, #237CFF 100%)" block
                                @click="submit(item)" size="small">提交</van-button>
                        </div>
                    </div>
                    <div class="status" :class="item.status == '1' ? 'warming' : 'sucss'">{{ item.status == '1'
                        ? '待处理' : '已完成'
                    }}</div>

                </div>


            </van-list>


            <van-empty description="暂无数据" v-if="tableData.length == 0" />



        </div>


        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />

        <DepartmentList v-model:show="departmentPopoverIsShow" @changeDepartment="changeDepartment"></DepartmentList>
    </div>
</template>

<script setup lang="ts">
import collapseTransition from "@/components/collapse-transition/index.vue";
import { getTicketPageAPI, updataDispatchReceiptAPI } from "@/api/dispatch.ts";
import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { useRoute } from "vue-router";
import { PopoverAction, showSuccessToast } from "vant";
import DepartmentList from "./modules/departmentList.vue";
import { formatDate } from "@/utils/loadTime";
import { text } from "stream/consumers";
interface FieldExtOption {
    label: string;
    // value: string;
    text?: string;

    [key: string]: any
}

interface FieldMeta {
    createTime: string;
    createBy: string;
    updateBy: string | null;
    deleted: number;
    id: string;
    collectionId: string;
    fieldScenerio: number;
    fieldType: string;
    fieldExt: string | FieldExtOption[];
    fieldName: string;
    fieldDesc: string;
    fieldSort: number;
    modelValue: string | string[]
    fieldValue: null | string
    [key: string]: any
}

interface TableData {
    activityName: string | null;
    collectionId: string;
    collectionName: string | null;
    count: number | null;
    createTime: string;
    customerName: string;
    department: string | null;
    fieldsMetas: FieldMeta[];
    id: string;
    ownerId: string | null;
    status: string | null | number;
    ticketNo: string;
    uscc: string | null;
}

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    onConfirm,
} = searchHooks({
    source: 'dispatchEdit',
})
const route = useRoute()

const searchName = ref('')
const pageSize = ref(10);
const total = ref(0);
const pageNum = ref(1);

const loading = ref<boolean>(false)
const finished = ref<boolean>(true)

const searchListIsShow = ref(false)

const curSelectType = reactive({
    text: '未完成',
    value: 1
})
const collectionName = ref('')
const tabList = ref([
    { text: '未完成', value: 1 },
    { text: '已完成', value: 2 },

])

function changeSelectTab(params: any) {

    curSelectType.value = params.value
    curSelectType.text = params.text
    pageNum.value = 1
    tableData.value = []
    loadTicklist()
}



const tableData = ref<TableData[]>([])
function loadTicklist() {
    const toast = showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        loadingType: 'spinner',
        duration: 0,
        overlay: true,
        teleport: '.main-page',
    });

    let params = {
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        customerName: searchName.value,
        startTime: formline.startTime ? `${formline.startTime} 00:00:00` : undefined,
        endTime: formline.endTime ? `${formline.endTime} 23:59:59` : undefined,
        status: curSelectType.value,
        collectionName: collectionName.value,
        departmentId: department.value
    }

    getTicketPageAPI(params).then((res: any) => {
        if (res.code == 200) {
            for (const item of res.data.records || []) {
                item.fieldsMetas = (item.fieldsMetas || []).map((el: any) => {
                    if (el.fieldType == 'checkbox') {
                        el.modelValue = el.fieldValue ? el.fieldValue.split(',') : []
                    } else {
                        el.modelValue = el.fieldValue ?? ''
                    }

                    if (el.fieldType == 'select') {
                        el.fieldExt = el.fieldExt ? JSON.parse(el.fieldExt) : []
                        // el.fieldExt = el.fieldExt.map((element: any) => ({ text: element.label, value: element.value }))
                        el.fieldExt = el.fieldExt.map((element: any) => ({ text: element.label }))

                    } else if (el.fieldType == 'checkbox') {
                        el.fieldExt = el.fieldExt ? JSON.parse(el.fieldExt) : []
                    } else if (el.fieldType == 'date') {
                        el.fieldExt = el.fieldExt ? JSON.parse(el.fieldExt) : 'date'

                        el.showTimePicker = false

                        // 将 showTimePicker、activeTimeTab、currentDate、currentTime、minDate、maxDate、minHour、maxHour、minMinute 挂载到 el 上，便于后续表单项独立控制时间选择器
                        el.showTimePicker = false
                        el.activeTimeTab = 0
                        const now = new Date();
                        el.currentDate = [
                            now.getFullYear().toString(),
                            (now.getMonth() + 1).toString().padStart(2, '0'),
                            now.getDate().toString().padStart(2, '0')
                        ];
                        el.currentTime = [
                            now.getHours().toString().padStart(2, '0'),
                            now.getMinutes().toString().padStart(2, '0')
                        ];
                    }

                    return el
                })
            }


            tableData.value = [...tableData.value, ...res.data.records]

            total.value = res.data.total
            if (total.value <= tableData.value.length) {
                finished.value = true
            } else {
                finished.value = false
            }

        }

    }).finally(() => {
        loading.value = false;
        toast.close()
    })
}




function onLoad() {
    pageNum.value += 1;
    loadTicklist()
}

const time = computed(() => {
    if (formline.startTime && formline.endTime) return `${formline.startTime} - ${formline.endTime}`
    else return ``
})



//所选部门 
const department = reactive({
    text: '',
    value: ''
})
const departmentPopoverIsShow = ref(false)
const changeDepartment = ({ selectedOptions }: any) => {
    department.text = selectedOptions[0].departmentName
    department.value = selectedOptions[0].departmentId

}

const minDate = ref(new Date(new Date().setFullYear(new Date().getFullYear() - 2)));
const maxDate_ = ref(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000 * 3)); // 一月后
const minHour = ref(0);
const maxHour = ref(23);
const minMinute = ref(0);



const confirmSearch = () => {
    pageNum.value = 1
    tableData.value = []
    loadTicklist()
}

function submit(row: TableData) {
    let params = {
        ticketId: row.id,
        fieldBodyList: row.fieldsMetas.map((item: FieldMeta) => {
            return {
                fieldId: item.id,
                fieldValue: Array.isArray(item.modelValue) ? item.modelValue.join(',') : item.modelValue,
            }
        })
    }

    updataDispatchReceiptAPI(params).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            tableData.value = []
            pageNum.value = 1
            showSuccessToast('提交成功')
            loadTicklist()
        }

    })

}

onMounted(() => {
    collectionName.value = route.query.collectionName as string || ''
    searchName.value = route.query.customerName as string || ''
    department.value = route.query.departmentId as string || ''
    department.text = route.query.departmentName as string || ''
    loadTicklist()
})
</script>

<style lang="scss" scoped>
.card {
    min-height: 100%;
    background: #fff;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px;
}

.selectOption-item {
    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
    font-weight: 400;
    font-size: 14px;
    color: #1A76FF;
    padding: var(--van-search-padding);
    margin: var(--van-search-padding);
    margin-bottom: 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    .play {
        transform: rotate(90deg);
    }
}

.selectOption-item:active {
    background: #d8dee6;
}


.list-item {
    border-radius: 10px;
    border: 1px solid #f3f6f9;
    padding: 10px;
    margin: 15px auto;
    background: #fff;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), 0 3px 5px rgba(0, 0, 0, 0.1);

    .selectOption-item {
        font-size: 12px;
        margin: 10px 0;
    }

    .selectOption-item.disable {
        pointer-events: none;
        opacity: 0.6;

        filter: grayscale(0.5);
    }

    :deep(.van-cell) {
        border-radius: 10px;
        font-size: 12px;
    }

    :deep(.van-field.van-cell) {
        padding: var(--van-cell-vertical-padding) 0px;
    }


    :deep(.van-field__label) {
        margin-right: 0px;

    }

    .form-item {
        display: flex;
        align-items: center;
    }

    .status {
        background-color: #72ABFF;
        position: absolute;
        right: 0px;
        top: 0px;
        font-size: 12px;
        padding: 5px 10px;
        border-radius: 0 15px 0 15px;
        color: #EDF5FF;
    }

    .sucss {
        background-color: #3F7FFF !important;
    }

    .warming {
        background-color: #ee9d0194 !important;
    }

    .form_row {
        display: flex;
        flex-direction: column;
        width: 100%;

    }

    .font {
        font-size: 12px;
        color: #000;
        font-weight: 600;
        min-width: 60px;
    }
}

.search {
    :deep(.van-search__content) {
        background-color: #EDF5FF;
        border-radius: 5px;
        width: 180px;
    }



    :deep(.van-icon-search) {
        color: #1A76FF;

    }


    :deep(.van-field__control) {
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
    }

    :deep(.van-field__control::placeholder) {
        color: #1A76FF;
    }

    .selectOption-item {
        margin: 0px;
        height: var(--van-search-input-height);
    }

}


:deep(.van-popover__wrapper) {
    flex: 1 0 auto;
}

:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
    width: auto;
}

:deep(.van-checkbox-group--horizontal) {
    row-gap: 5px;
}
</style>