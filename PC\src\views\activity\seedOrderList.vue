<template>
    <div>
        <div class="card-header">
            <div class="card-label">派单列表</div>
        </div>
        <SearchHeader>
            <el-form :inline="true" class="demo-form-inline " label-suffix=":" label-width="0" label-position="left">
                <el-form-item>
                    <el-input v-model="formLine.orderName" style="width: 200px" placeholder="搜索派单主题" clearable>
                        <template #prefix>
                            <el-icon class="el-input__icon">
                                <search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item v-if="roles.includes('area_dispatch')">
                    <el-select v-model="formLine.department" clearable placeholder="请选择部门" style="width: 200px">
                        <el-option v-for="item in departmentList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-select v-model="formLine.status" clearable placeholder="请选择状态" style="width: 200px">
                        <el-option v-for="item in statusOptionList" :key="item.value" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item class="ml-35">
                    <el-button type="primary" icon="Search" @click="refresh">搜索</el-button>
                    <!-- <div class="refreshButton ml-15" @click="refresh">
                        <el-icon :size="18" color="#8e9295">
                            <Refresh />
                        </el-icon>
                    </div> -->
                </el-form-item>
            </el-form>
            <router-link
                :to="{ path: '/editOrder', query: { activityNmae: route.query.activityNmae, id: route.query.id } }"
                v-if="roles.includes('department_dispatch') && (route.query.id && route.query.id != '')">
                <el-button type="primary" color="#0052D9">
                    <i class="icon-tianjia1 fs18"></i>
                    创建派单</el-button>
            </router-link>
        </SearchHeader>
        <el-table :data="tableData" v-loading="loading" element-loading-background="rgba(0, 0, 0, 0.4)"
            class="customTable" style="min-width: 100%; min-height: 200px" stripe highlight-current-row
            :row-key="(row: any) => row.activityId">
            <el-table-column prop="name" label="派单主题" align="center" />
            <el-table-column prop="activityName" label="所属活动" align="center" />
            <el-table-column prop="departmentName" label="发起部门" align="center" />
            <!-- <el-table-column prop="region" label="接受区域" align="center" /> -->
            <el-table-column prop="status" label="状态" align="center">
                <template #default="{ row }">
                    <el-tag :type="row.status == '2' ? 'success' : row.status == '1' ? 'warning' : 'info'"> {{
                        row.status == '2' ? '已完成' : row.status == '1' ? '已派单' : '待处理' }} </el-tag>
                </template>
            </el-table-column>
            <el-table-column prop='createTime' label='创建时间' align='center' />
            <el-table-column label="操作" align="center" width="300">
                <template #default="{ row }">
                    <template v-if="roles.includes('department_dispatch')">
                        <el-button type="primary" size="small" :disabled="row.status != 0"
                            @click="router.push({ path: '/editOrder', query: { activityNmae: route.query.activityNmae, descriptionId: row.id } })">编辑</el-button>
                        <el-button size="small" :disabled="row.status != 0"
                            @click="dialogIsShow = true, dialogTitle = '派单', collectionId = row.id" class="btn"
                            :class="row.status == 0 ? 'btn-primary' : 'btn-light'">派单</el-button>
                        <el-button type="primary" size="small"
                            @click="router.push({ path: '/receiptStatistics', query: { id: row.id } })">查看</el-button>
                    </template>
                    <template v-else>
                        <el-button size="small" :disabled="row.status == 2"
                            @click="dialogIsShow = true, dialogTitle = '派单', collectionId = row.id" class="btn"
                            :class="row.status == 2 ? 'btn-light' : 'btn-primary'">派单</el-button>

                        <el-button size="small" class="btn" :disabled="row.status == 2"
                            @click="dialogIsShow = true, dialogTitle = '反馈', collectionId = row.id"
                            :class="row.status == 2 ? 'btn-light' : 'btn-success'">反馈</el-button>
                        <el-button type="primary" size="small"
                            @click="router.push({ path: '/receiptStatistics', query: { id: row.id } })">查看</el-button>
                    </template>







                </template>
            </el-table-column>
        </el-table>
        <el-pagination class="customPagination" @size-change="handleSizeChange" prev-icon="DArrowLeft"
            next-icon="DArrowRight" @current-change="handleCurrentChange" :page-sizes="[10, 50, 100, 200]" background
            :page-size="pageSize" :current-page="pageNumber" :pager-count="5"
            layout="prev, pager, next,sizes,jumper,total" :total="total"
            style="margin: 40px 60px 20px 0px; justify-content: end">
        </el-pagination>

        <el-dialog v-model="dialogIsShow" title="派单管理" width="600" class="curtomDialog" :lock-scroll="false">
            <!-- 派单管理弹窗内容区 -->
            <div>
                <div style="display: flex; justify-content: space-between; align-items: center;" class="mb-25">
                    <div style="font-weight: 700; font-size: 18px; color: #000;">{{ dialogTitle == '派单' ? '接受派单' :
                        '收集派单' }}
                    </div>
                    <el-button type="primary" class="mt-8" color="#2564EB">
                        <el-icon>
                            <Download />
                        </el-icon>
                        <template v-if="roles.includes('department_dispatch')">
                            <span v-if="dialogTitle == '派单'" @click="downloadTemplate('派单模板')">下载派单模板 </span>
                        </template>
                        <template v-else-if="roles.includes('area_dispatch')">
                            <span v-if="dialogTitle == '派单'" @click="downloadTemplate('客户经理信息')">下载派单 Excel </span>
                            <span v-else @click="downloadTemplate('全量派单Excel')">收集派单 Excel</span>
                        </template>
                    </el-button>
                </div>

                <!-- 接收派单 -->
                <!-- <div class="mb-24">
                    <div style="background: #f5f7fa; border-radius: 8px; padding: 16px;">
                        <div style="display: flex; align-items: flex-start; ">
                            <el-icon style="color: #2564EB; margin-right: 8px;">
                                <InfoFilled />
                            </el-icon>
                            <div>
                                <div style="color: #516773;">当前有 <span
                                        style="color: #0052D9; font-weight: bold;">12</span>
                                    个待处理派单</div>
                                <div style="color: #516773;"> 最新派单时间：2024-01-18 14:30 </div>
                            </div>
                        </div>
                    </div>
                </div> -->

                <!-- 转发派单 -->

                <div style="font-weight: 700; font-size: 18px; margin-bottom: 16px;color: #000;" class="mt-24">
                    {{ dialogTitle == '派单' ? '转发派单' : '反馈部门' }}
                </div>



                <el-upload ref="uploadRef" action="#" v-model:file-list="fileList" :limit="1" :show-file-list="true"
                    accept=".xlsx,.xls" drag :http-request="uploadFile" :auto-upload="false">
                    <el-icon style="font-size: 40px; color: #c0c4cc;">
                        <UploadFilled />
                    </el-icon>
                    <div style="margin: 12px 0;">拖拽文件到此处或</div>
                    <el-link style="font-size: 16px;color:#2967EB">点击上传</el-link>
                    <div style="color: #8e9295; margin-top: 8px;">支持格式：Excel（.xlsx，.xls）</div>
                </el-upload>

            </div>
            <template #footer>


                <el-button type="primary" @click="submit" style="width: 100%;" size="large" color="#2564EB">
                    <template v-if="dialogTitle == '派单'">
                        <span v-if="roles.includes('area_dispatch')">确认转发</span>
                        <span v-else-if="roles.includes('department_dispatch')">确认派单</span>
                    </template>
                    <template v-else>
                        <span>确认反馈</span>
                    </template>

                </el-button>

            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
import { InfoFilled, Download, UploadFilled } from '@element-plus/icons-vue'
import { ElStep, type UploadInstance } from 'element-plus'
import SearchHeader from '@/components/searchHeader/index.vue'
import { useUserInfo } from "@/stores/userInfo";
import { getDepartmentList } from "@/hooks_modules/getDepartmentList.ts";
import {
    getDispatchListAPI,
    downloadTemplateAPI,
    uploadTemplateAPI,
    downloadManagerInfoAPI,
    downloadAllManagerInfoAPI,
    uploadManagerInfoAPI,
    uploadAllManagerInfoAPI
} from "@/api/activity";
// import { debounce } from "lodash-es";
const { getDepartment } = getDepartmentList()
interface TableData {
    id: number;
    title: string;
    activity: string;
    department: string;
    region: string;
    status: string;
    createTime: string;
}
const route = useRoute()
const router = useRouter()
const userInfoStore = useUserInfo()

const uploadRef = ref<UploadInstance>()
const roles = computed(() => userInfoStore.userInfo.roles || [])
const fileList = ref<any[]>([])
const loading = ref(false)
const formLine = reactive({
    orderName: '',
    department: '',
    status: '',
})
const pageNumber = ref(1)
const pageSize = ref(10)
const total = ref(0)


const departmentList = ref<{ value: string, label: string }[]>([])
const statusOptionList = ref([
    {
        value: '2',
        label: '已完成'
    },
    {
        value: '1',
        label: '已派单'
    },
    {
        value: '0',
        label: '待处理'
    },
])

watch(() => roles.value, (newVal) => {
    if (newVal.includes('area_dispatch')) {
        getDepartment().then(data => {

            departmentList.value = data.map((item: any) => ({
                value: item.departmentId,
                label: item.departmentName
            }));
        }).catch(error => {
            console.error('获取部门列表失败:', error);

        });
    }
}, { deep: true, immediate: true })


const dialogIsShow = ref(false)
const dialogTitle = ref('')
const collectionId = ref('')

const tableData = ref<TableData[]>([])


const refresh = debounce(() => {
    loadDispatchList()

}, 300, { leading: true, trailing: false, })
const submit = () => {
    uploadRef.value!.submit()
}

function handleSizeChange(val: number) {
    pageSize.value = val;
    loadDispatchList()
}
function handleCurrentChange(val: number) {
    pageNumber.value = val;
    loadDispatchList()

}


//获取派单列表
function loadDispatchList() {
    let params = {
        pageNum: pageNumber.value,
        pageSize: pageSize.value,
        status: formLine.status,
        activityId: route.query.id,
        name: formLine.orderName,
        departmentId: formLine.department,
    }
    loading.value = true
    getDispatchListAPI(params).then((res: any) => {
        if (res.code == 200) {
            tableData.value = res.data.records
            total.value = res.data.total
        }
    }).finally(() => {
        loading.value = false
    })
}
//部门主任下载派单模板
function downloadTemplate(title: string) {

    switch (title) {
        case '派单模板':
            downloadTemplateAPI().then((res: any) => {
                initDownload(res)
            })
            break;
        case '客户经理信息':
            downloadManagerInfoAPI({ collectionId: collectionId.value }).then((res: any) => {
                initDownload(res)
            })
            break;
        case '全量派单Excel':
            downloadAllManagerInfoAPI({ collectionId: collectionId.value }).then((res: any) => {
                initDownload(res)
            })
            break;

    }

    async function initDownload(res: any) {
        const text = await res.text();

        try {
            const json = JSON.parse(text);
            // 如果能解析成 JSON，说明是错误信息
            if (json.code !== 200) {
                ElMessage.error(json.msg || '下载失败');
                return;
            }
        } catch (e) {
            // 直接使用响应数据创建Blob
            const blob = new Blob([res], { type: 'application/vnd.ms-excel' });
            // 创建下载链接
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(blob);
            link.download = title + '.xls';
            // 触发下载
            document.body.appendChild(link);
            link.click();
            // 清理
            document.body.removeChild(link);
            window.URL.revokeObjectURL(link.href);
            ElMessage.success('下载成功');
        }
    }


}







//上传接口
async function uploadFile(file: any) {
    const uploadForm = new FormData();
    uploadForm.append("file", file.file);
    uploadForm.append('collectionId', collectionId.value);
    if (dialogTitle.value == '派单') {
        if (roles.value.includes('department_dispatch')) {
            return uploadTemplateAPI(uploadForm).then((res: any) => {
                if (res.code == 200) {
                    ElMessage.success('上传成功');
                    loadDispatchList()

                    dialogIsShow.value = false
                } else {
                    ElMessage.error(res.msg);
                }
            })
        } else {
            return uploadManagerInfoAPI(uploadForm).then((res: any) => {
                if (res.code == 200) {
                    ElMessage.success('上传成功');
                    loadDispatchList()

                    dialogIsShow.value = false
                } else {
                    ElMessage.error(res.msg);
                }
            })
        }
    } else {
        return uploadAllManagerInfoAPI(uploadForm).then((res: any) => {
            if (res.code == 200) {
                ElMessage.success('上传成功');
                loadDispatchList()
                dialogIsShow.value = false
            } else {
                ElMessage.error(res.msg);
            }
        })
    }



}

onMounted(() => {
    loadDispatchList()
})
</script>

<style lang="scss" scoped></style>