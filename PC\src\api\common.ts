import request from "../utils/request";
import configAxios from './config'


//获取部门列表
export function getDepartmentListAPI() {
    return request({
        url: `${configAxios.taskHubServer}/common/dispatch/department/list`,
        method: "POST",
    });
}

/* 获取用户token */
export function getUserTokenAPI(params: any) {
    return request({
        url: `${configAxios.loginServer}/pc/getRedirectURL`,
        method: "GET",
        params,
    });
}

/* 获取用户信息 */
export function getUserInfoAPI() {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/me`,
        method: "GET",
    });
}