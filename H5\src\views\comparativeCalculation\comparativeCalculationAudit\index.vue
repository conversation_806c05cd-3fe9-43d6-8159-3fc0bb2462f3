<template>
    <div class='card container_'>
        <div class="container__header">
            <div>
                <img src="@/assets/comparativeCalculationAudit.svg" alt="" srcset="">
                <span class="ml-6">比算审核</span>
            </div>


            <cusTabs :tabList="tabList" v-model:curSelectTab="curSelectType" @changeSelectTab="changeSelectTab">
            </cusTabs>
        </div>





        <van-search v-model="searchName" @click="popoverIsShow = true" readonly show-action placeholder="请选择查询对象">
            <template #action>
                <img src="@/assets/calendar.svg" alt="" srcset="" @click.stop="calendarIsShow = true">
            </template>
            <template #left-icon>
                <img class="mt-5" src="@/assets/search.svg" alt="" srcset="">

            </template>
        </van-search>

        <van-popup v-model:show="popoverIsShow" round position="bottom" teleport="body">
            <van-picker title="人员列表" :columns="listData" :columns-field-names="{
                text: 'staffName',
                value: 'staffCode'
            }" @confirm="confirmStaffCode" @cancel="popoverIsShow = false" />
        </van-popup>




        <div class=" container_body ">
            <van-list v-model:loading="loading" :finished="finished"
                :finished-text="tableData.length > 0 ? '没有更多了' : ''" @load="onLoad">
                <div class="box_content" v-for="item, index in tableData" :key="index">
                    <div class="box_content_header">
                        <div style="display: flex;align-items: center;">
                            <div class="name" v-getName="{ item: item, }">{{ item.name }}</div>
                            <div class="ml-14">
                                <div class=" mt-5">{{ item.staffCode }}</div>
                                <div class="fs12">{{ item.roleName }}</div>
                            </div>
                        </div>
                        <div>
                            <span>上传时间：</span>
                            <span class="time">{{ item.date }}</span>
                        </div>
                    </div>
                    <div class="box_content_body">
                        <div class="uploadTitle">上传照片：</div>
                        <div class="imgList">


                            <van-image class="uploadImg" :radius="5" v-imgPreview="[item.images, indexE]" lazy-load
                                :src="el" v-for="el, indexE in item.images" />

                        </div>
                    </div>
                    <div class="row">
                        <div class="row_label">触点备注：</div>
                        <div class="row_value">{{ item.description }}</div>
                    </div>


                    <van-button color="#FF573D" class="mt-10" style="width: 100%;" size="small"
                        @click="auditConfirm('no', item)" v-if="item.auditStatus == 1">不通过</van-button>

                    <div class="auditIcon">
                        <i class="icon-yitongguo fs36" style="color: #6588B3;" v-if="item.auditStatus == 1"></i>
                        <i class="icon-weitongguo fs36 text-danger " v-if="item.auditStatus == 2"></i>

                    </div>
                </div>

            </van-list>
            <van-empty description="暂无数据" v-if="tableData.length == 0" />



        </div>



        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />






    </div>
</template>

<script setup lang="ts">
import cusTabs from "@/components/cusTabs/index.vue";
import { searchHooks } from "@/hooks_modules/changeTimeHooks.ts";
import { comparativeListHooks } from "../listHooks";
import { compareAuditAPI } from "@/api/comparative";
import { showToast, showSuccessToast, showFailToast } from "vant";

const tabList = ref([
    { text: '通过', value: 1 },
    { text: '未通过', value: 0 },
])


const curSelectType = ref<number>(1)   //1  已处理  //     0  未处理    

function changeSelectTab(item: any) {

    curSelectType.value = item.value
    pageNum.value = 1
    tableData.value = []
    loadList()
}


const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    searchName,
    listData,
    popoverIsShow,
    onConfirm,
    confirmStaffCode,
} = searchHooks()

const {
    loadList,
    onLoad,
    tableData,
    loading,
    finished,
    pageNum,
} = comparativeListHooks({
    curSelectType,
    formline
})





watch(formline, () => {

    pageNum.value = 1
    tableData.value = []
    loadList()
}, { deep: true })






function auditConfirm(type: string, item: any) {
    const params = {
        id: item.id,
        auditStatus: type == 'yes' ? 1 : 2,
    }

    if (type == 'no') {
        showConfirmDialog({
            title: '标题',
            message:
                '你确定要驳回此条比算审核吗？',
        })
            .then(async () => {
                compareAudit(params)
            })
            .catch(() => {

            });

    } else {
        compareAudit(params)
    }
}
async function compareAudit(params: any) {
    compareAuditAPI(params).then((res: any) => {
        if (res.code == 200) {
            showSuccessToast("审核成功");
            pageNum.value = 1;
            tableData.value = []
            loadList()
        } else {
            showFailToast("审核失败");
        }

    })
}






onMounted(() => {
    loadList()
})

</script>

<style lang="scss" scoped>
.card {
    min-height: 100%;
    background: #fff;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px 10px 0px 0px;
}

:deep(.van-search__action) {
    line-height: 0;
}

:deep(.van-search__content) {
    background-color: #EDF5FF;
    border-radius: 5px;
}



:deep(.van-icon-search) {
    color: #1a76ff;
}

:deep(.van-search__field) {
    height: 40px;
}


:deep(.van-field__value) {
    height: 36px;
    line-height: 36px;
    font-weight: 400;
    font-size: 14px;
    color: #72ABFF;
}

:deep(.van-field__control) {
    font-weight: 400;
    font-size: 14px;
    color: #72ABFF;
}

:deep(.van-field__control::placeholder) {
    color: #72ABFF;
}





.box_content {
    background: #EDF5FF;
    border-radius: 10px 10px 10px 10px;
    padding: 21px;
    position: relative;

    .box_content_header {
        font-weight: 400;
        font-size: 13px;
        color: #3D3D3D;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .name {
            font-weight: 500;
            font-size: 18px;
            color: #1A76FF;
        }

        .time {
            font-weight: 400;
            font-size: 13px;
            color: #6B6B6B;
        }

    }

    .box_content_body {
        .uploadTitle {
            font-weight: 400;
            font-size: 14px;
            color: #3D3D3D;
            margin: 13px 0px;
        }

        .imgList {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;

            .uploadImg {
                height: 90px;
            }


        }
    }

    .row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 17px 0px;

        .row_label {
            flex: 0 0 80px;
            font-weight: 400;
            font-size: 14px;
            color: #3D3D3D;
        }

        .row_value {
            // flex: 1 0 ;
            word-break: break-all;
            overflow-wrap: break-word;
            font-weight: 400;
            font-size: 14px;
            color: #6B6B6B;
        }
    }

    .auditIcon {
        position: absolute;
        right: -10px;
        top: -10px;
    }
}

.box_content:not(:first-child) {
    margin-top: 15px
}
</style>