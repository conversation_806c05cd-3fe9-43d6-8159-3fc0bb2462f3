<template>
  <div class='mt-16 container_ Contribution'>
    <component :is="item.moudule_" v-for="item in componentOption" :key="item.moudule_name"
      :ref="(el: HTMLDivElement) => item.ref_name && (refsArray[item.ref_name] = el)"
      v-on="generateEventHandlers(item.EventsList || [])"
      @="emitEventsListGenerateEventHandlers(item.emitEventsList || [])"
      v-bind="mergePropsAndVModelBindings(item.props, item.vModels)">
    </component>


    <van-overlay :show="lading" class-name="loadingoverlay" :lock-scroll="false">
      <div class="wrapper" @click.stop>

        <van-loading type="spinner" size="48px" color="#ffa800" vertical><span
            class="fs20 text-warning">加载中...</span></van-loading>

      </div>
    </van-overlay>
  </div>
</template>

<script setup lang='ts'>

import { getContributionDataLeaderAPI } from "@/api/home";
import componentList from "./config.ts";
import { useUserInfo } from "@/stores/userInfo";
import { ref, reactive, onMounted, } from "vue";
import { EventsHooks, ComponentOptionProps } from "@/hooks_modules/Eventhooks";
const { generateEventHandlers, emitEventsListGenerateEventHandlers, mergePropsAndVModelBindings } = EventsHooks()
const userInfoStore = useUserInfo();
const refsArray = ref<{ [key: string]: HTMLDivElement | null }>({});
const componentOption: ComputedRef<ComponentOptionProps[]> = computed(() => {
  let list = userInfoStore.getMenuList.filter((item: MenuItem) => item.menuCode === 'home')[0]?.children || []
  let contributionlist = list.filter((item: MenuItem) => item.menuCode === 'Contribution')[0]?.children || []

  const listOption = contributionlist.map((item: MenuItem) => getMenuList_(item))



  return listOption
})

enum UserType {
  Other = 0,  // 其他
  Public = 1, // 公众
  Enterprise = 2 // 政企
}
type TableData = {
  ContributionMenu: ContributionLeader,
  list: ContributionItem[]
}


const tableData = reactive<TableData>({
  ContributionMenu: {
    type: 1,
    points: 0,
    tianYi: 0,
    kuanDai: 0,
    outCall: 0,
    signCount: 0,
    repairOrder: 0,
    repairOrderTotal: 0,
    businessRecords: 0,
    xinliangZgz: 0,
    ronghe: 0,
    tianyiXuyue: 0,
    kuandaiXuyue: 0,

    orgDept: 1,
    gov5g: 0,
    govCloudComputer: 0,
    govFttrb: 0,
    govHuZhuan: 0,
    govShangZhuan: 0,
    govTianyi: 0,
    govTianyiShilian: 0,
    govXiaowei: 0,
    bizRecord: 0,
    visitCount: 0,
    govQudaoJifen: 0,
  },
  list: []
})
const { ContributionMenu, list } = toRefs(tableData)

// 是否存在片区tab选项
const isHaveAreaTab = computed(() => {
  return userInfoStore.userInfo.havePq == 1 ? true : false
})
const curSelectType = ref<number | undefined>(0)  //     0  片区       1  门店    2  人员
const curSelectItem = ref<Partial<ContributionItem>>({})   //当前点击的时候 选择的id
const allIsShow = ref(false)  // 是否展开所有

const isGov = ref(false)



const getMenuList_ = (item: MenuItem): ComponentOptionProps => {
  let obj: ComponentOptionProps = {
    moudule_: componentList[item.menuCode].moudule_,
    moudule_name: item.menuCode,
    ref_name: item.menuCode,
  }

  switch (item.menuCode) {
    case 'header':
      obj = {
        ...obj,
        props: {
          children: item.children,
          orgDept: tableData.ContributionMenu.orgDept
        },

        vModels: [
          { prop: 'curSelectType', value: curSelectType },
          { prop: 'isHaveAreaTab', value: isHaveAreaTab },
          { prop: 'isGov', value: isGov },

        ],
        emitEventsList: [
          { eventName: 'changeSelectType', handler: changeSelectType },
        ],
      }
      break
    case 'overviewCard':
      obj = {
        ...obj,
        props: {
          ContributionMenu: ContributionMenu.value,
          curSelectType: curSelectType.value,
          curSelectItem: curSelectItem.value,
          allIsShow: allIsShow.value,
          isGov: isGov.value
        },

        emitEventsList: [
          { eventName: 'changeTime', handler: changeTime },
          { eventName: 'changeAllIsShow', handler: changeAllIsShow },

        ],
      }
      break
    case 'list':
      obj = {
        ...obj,
        props: {
          dataItem: list.value,
          isGov: isGov.value
        },
        emitEventsList: [
          { eventName: 'selectRow', handler: handleClick },
        ],
      }
      break
  }
  return obj
}

function changeAllIsShow() {
  allIsShow.value = !allIsShow.value
  for (const item of tableData.list) {
    item.isShow = allIsShow.value
  }

}


const lading = ref(false)
function loadContributionData() {
  let params: any = {
    type: curSelectType.value,
    startTime: formLine.startTime ? `${formLine.startTime} 00:00:00` : '',
    endTime: formLine.endTime ? `${formLine.endTime} 23:59:59` : '',
    orgDept: isGov.value ? 2 : 1
  }

  if (curSelectItem.value.type == 0) {
    params.pqId = curSelectItem.value.pqId
    curSelectType.value = 1
  } else if (curSelectItem.value.type == 1) {
    params.channelId = curSelectItem.value.channelId
    curSelectType.value = 2
  }

  lading.value = true
  getContributionDataLeaderAPI(params).then(res => {
    for (const key in ContributionMenu.value) {
      tableData.ContributionMenu[key] = res.data[key] ?? 0
    }
    for (const item of res.data.develops || []) {
      item.isShow = false
    }
    tableData.list = res.data.develops

  }).catch((error) => {
    console.log(error);
    showFailToast('请求失败');
  }).finally(() => {
    lading.value = false
  })
}


function changeSelectType(type: number) {
  curSelectItem.value = {}
  loadContributionData()
}










function handleClick(row: ContributionItem) {

  switch (row.type) {
    case 0:
      curSelectType.value = 1
      break;
    case 1:
      curSelectType.value = 2
      break;

  }

  nextTick(() => {
    curSelectItem.value = row
    loadContributionData()
  })
}



const formLine = reactive({
  startTime: '',
  endTime: ''
})
function changeTime(formline: any) {
  formLine.startTime = formline.startTime
  formLine.endTime = formline.endTime

  loadContributionData()
}


watch(() => isGov.value, () => {
  loadContributionData()
})



watch(() => userInfoStore.userInfo.orgDept, (newValue) => {
  switch (newValue) {
    case UserType.Public:
      isGov.value = false
      break;
    case UserType.Enterprise:
      isGov.value = true
      break;
    case UserType.Other:
      isGov.value = true
      break;
    default:
      isGov.value = false
      break;
  }
}, { deep: true, immediate: true })



// 生命周期钩子
onMounted(() => {
  const props = componentOption.value[0].props
  const children = props?.children
  if (children) {
    const children0 = children[0]
    switch (children0.menuName) {
      case '片区':
        isHaveAreaTab.value ? curSelectType.value = 0 : curSelectType.value = 1
        break;
      case '门店':
        curSelectType.value = 1
        break;
    }

  } else {
    // 如果没有childen 说明是一线人员 
    curSelectType.value = 2
  }
  loadContributionData()

});

onUnmounted(() => {

});
</script>

<style lang='scss' scoped>
.Contribution {
  position: relative;
}

.wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}



.loadingoverlay {
  position: absolute;
  border-radius: 5px;
  background: rgba(0, 0, 0, 0.7)
}

.container_body {
  // padding: 15px;
}



.icon-item span {
  font-size: 12px;
  color: #3D3D3D;
  text-align: center;
}



.activity-container {
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  gap: 20px;


}
</style>