
import request from "../utils/request";
import configAxios from './config'

//比算保存
export function compareSaveAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/compare/save`,
        method: "post",
        data,
    });
}

//比算审核
export function compareAuditAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/compare/audit`,
        method: "POST",
        data,
    });
}
//比算查询
export function getCompareListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/compare/page`,
        method: "POST",
        data,
    });
}

