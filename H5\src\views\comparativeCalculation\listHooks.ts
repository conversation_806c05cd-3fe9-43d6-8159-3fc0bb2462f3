

import { ref, reactive } from "vue";
import { getCompareListAPI } from "@/api/comparative";
import { useRoute } from "vue-router";
import { showLoadingToast } from "vant";
type tableData = {
    date: string,
    time: string,
    roleName: string,
    images: string[],
    [key: string]: any
}


export const comparativeListHooks = function (params: any = {}) {
    const { formline, curSelectType } = params
    const route = useRoute()
    const { VITE_IMAGE_URL } = import.meta.env
    const loading = ref(false)
    const finished = ref(true)
    const error = ref(false)

    const tableData = ref<tableData[]>([]);

    const pageSize = ref(10);
    const total = ref(0);
    const pageNum = ref(1);


    function loadList() {
        let params: { [key: string]: any } = {
            pageNum: pageNum.value,
            pageSize: pageSize.value,
        };
        if (route.query.endTime || formline?.endTime) {
            params.endTime = `${route.query.endTime || formline?.endTime} 23:59:59`
        }

        if (route.query.startTime || formline?.startTime) {
            params.startTime = `${route.query.startTime || formline.startTime} 00:00:00`
        }
        if (route.query?.staffCode || formline?.staffCode) {
            params.staffCode = (route.query?.staffCode || formline.staffCode[0]) == '全部' ? '' : formline.staffCode[0]
        }
        if (curSelectType) {
            params.auditStatus = curSelectType.value == 0 ? [2] : [1]
        }



        const toast = showLoadingToast({
            message: '加载中...',
            forbidClick: true,
            loadingType: 'spinner',
            duration: 0,
            overlay: true,
            teleport: '.main-page',
        });
        getCompareListAPI(params).then((res: any) => {
            if (res.code == 200) {

                for (const item of res.data.records || []) {
                    item.date = item.createTime.split(' ')[0]
                    item.time = item.createTime.split(' ')[1]
                    item.images = item.images ? item.images.split(',') : []
                    item.images = item.images.map((el: string) => {
                        if (el.indexOf(VITE_IMAGE_URL) == -1) {
                            return VITE_IMAGE_URL + el
                        } else {
                            return el
                        }

                    })

                }


                tableData.value = [...tableData.value, ...res.data.records]
                total.value = res.data.total


                if (total.value <= tableData.value.length) {
                    finished.value = true
                } else {
                    finished.value = false
                }

            }

        }).finally(() => {
            loading.value = false;
            toast.close()
        })

    }
    function onLoad() {
        pageNum.value += 1;
        loadList()
    }

    return {
        loadList,
        onLoad,
        error,
        pageNum,
        loading,
        finished,
        tableData
    }
}   
