import request from "../utils/request";
import configAxios from './config'
// login登录接口--获取个人信息
export function login(params: Object) {
    return request({
        url: `${configAxios.loginServer}/wecom/login`,
        method: "GET",
        params,
    })
}

// login登录接口--获取个人信息
export function menuList() {
    return request({
        url: `${configAxios.taskHubServer}/h5/menu/routes`,
        method: "GET",
    })
}


//文件上传 -图片
export function uploadFileAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/wecom/files`,
        method: "post",
        headers: {
            "Content-Type": "multipart/form-data",
        },
        data,
    });
}

//文件上传 -音频
export function uploadVoiceAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/wecom/files/voice`,
        method: "post",
        headers: {
            "Content-Type": "multipart/form-data",
        },
        data,
    });
}

//文件上传-识别图片文本 薛主任模型
export function byImageUrlQueryTextAPI(params: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/compare/qryPictureText`,
        method: "GET",
        params,
        timeout: 120 * 1000
    });
}
//文件上传-识别图片文本 子豪模型
export function byImageUrlChatPictureTextAPI(params: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/compare/chatPictureText`,
        method: "GET",
        params,
        timeout: 120 * 1000
    });
}



//文件上传-识别语音文本ID
export function getAudioUrlQueryTextIdAPI(params: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/review/qryVoiceText`,
        method: "GET",
        params,
    });
}
//文件上传-识别语音文本
export function byAudioUrlQueryTextAPI(params: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/review/getVoiceTextResult`,
        method: "GET",
        params,
    });
}




//文件删除
export function deleteFile(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/wecom/files`,
        method: "delete",
        data,
    });
}


//获取个人信息
export function getUserInfoAPI() {
    return request({
        url: `${configAxios.taskHubServer}/h5/user/me`,
        method: "get",

    });
}

//获取组织下所有员工列表
export function getAllStaffListAPI() {
    return request({
        url: `${configAxios.taskHubServer}/h5/user/staffs`,
        method: "get",
    });
}
//获取跳转翼销售code
export function getBusinessCodeAPI() {
    return request({
        url: `${configAxios.taskHubServer}/h5/business/code`,
        method: "get",
    });
}


// 获取所有的区域 或者分局 列表
export function getlistDeptOrSubOptionsAPI(params = {}) {
    return request({
        url: `${configAxios.taskHubServer}/h5/user/listDeptOptions`,
        method: "get",
        params
    });
}
// 获取所有的条线

export function getlistStaffRoleOptionsAPI() {
    return request({
        url: `${configAxios.taskHubServer}/h5/user/listStaffRole`,
        method: "get",
    });
}

// 根据工号获取人员名称
export function getNameByStaffCodeAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/user/getByStaffCode`,
        method: "POST",
        data: data,
    });
}

// 根据工号获取人员名称
export function getChatBiLinkAPI() {
    return request({
        url: `${configAxios.taskHubServer}/h5/chat/url`,
        method: "GET",
    });
}

// 获取字典列表
export function getDictInfoAPI(dictType: string) {
    return request({
        url: `${configAxios.taskHubServer}/h5/dict/list`,
        method: "POST",
        data: { dictType: dictType },

    });
}

// 获取字典列表
export function getAddressAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/hypotonic/queryAddress`,
        method: "POST",
        data
    });
}

// 获取跳转虎嗅商机录入 url
export function getEntryProtocolPageUrlAPI() {
    return request({
        url: `${configAxios.taskHubServer}/h5/busiAide/entryProtocolPageUrl`,
        method: "get",
    });
}

// 龙虎榜
export function queryDragonTigerRankingAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/dragonTigerRanking/queryDragonTigerRanking`,
        method: "POST",
        data
    });
}