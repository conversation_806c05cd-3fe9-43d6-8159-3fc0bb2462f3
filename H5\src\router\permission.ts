// 导入路由模块
import router from "@/router";
// 导入用户信息的Pinia存储模块
import { useUserInfo } from "@/stores/userInfo";
// 导入Pinia的storeToRefs工具
import { storeToRefs } from 'pinia'
// 导入NProgress库用于路由跳转时的加载进度条
import NProgress from "nprogress";
// 导入NProgress的CSS样式
import "nprogress/nprogress.css";

// 类型定义
interface RouteQuery {
  code?: string;
  randomParam?: string;
}

// 常量配置
const WHITE_LIST = ['/IDNotFound', '/home', '/stock', '/userData'] as const;
const ID_NOT_FOUND_PATH = '/IDNotFound';
const HOME_PATH = '/home';

// NProgress配置
NProgress.configure({ showSpinner: false });

/**
 * 处理URL参数
 * @param url 当前URL
 * @returns 处理后的查询参数
 */
const handleUrlParams = (url: string): RouteQuery => {
  const urlObj = new URL(url);
  const searchParams = new URLSearchParams(urlObj.search);
  
  return {
    code: searchParams.get('code') || undefined,
    randomParam: searchParams.get('randomParam') || undefined
  };
};

/**
 * 清理URL参数
 * @param url 当前URL
 */
const cleanUrlParams = (url: string): void => {
  const urlObj = new URL(url);
  const searchParams = new URLSearchParams(urlObj.search);
  
  if (searchParams.has('code')) {
    searchParams.delete('code');
  }
  if (searchParams.has('randomParam')) {
    searchParams.delete('randomParam');
  }
  
  window.history.replaceState({}, '', `${urlObj.pathname}${urlObj.hash}`);
};

/**
 * 路由守卫处理函数
 */
const handleRouteGuard = async (to: any, from: any, next: any) => {
  const userInfoStore = useUserInfo();
  const url = window.location.href;
  const { code, randomParam } = handleUrlParams(url);
  
  // 处理首页特殊逻辑
  if (from.path === HOME_PATH && (code || randomParam)) {
    cleanUrlParams(url);
  }
  
  // 初次进入系统时清除缓存
  if (to.path === HOME_PATH && (code || randomParam)) {
    userInfoStore.logOut();
  }
  
  // 开始加载进度条
  NProgress.start();
  
  const hasToken = userInfoStore.getToken;
  
  try {
    if (hasToken) {
      if (to.path === ID_NOT_FOUND_PATH) {
        next({ path: "/" });
      } else {
        next();
      }
    } else {
      if (WHITE_LIST.includes(to.path)) {
        next();
      } else {
        next(ID_NOT_FOUND_PATH);
      }
    }
  } catch (error) {
    console.error('路由守卫处理出错:', error);
    next(ID_NOT_FOUND_PATH);
  } finally {
    NProgress.done();
  }
};

// 注册全局前置守卫
router.beforeEach(handleRouteGuard);

// 注册全局后置守卫
router.afterEach(() => {
  NProgress.done();
});