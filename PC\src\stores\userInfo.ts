import { defineStore } from "pinia";
import { getUserTokenAPI, getUserInfoAPI } from "@/api/common";

const { VITE_MODE } = import.meta.env

import router from "../router";

interface UserInfosState {
  userInfo: Record<any, any>;
  // order_uuid: string;
  // currentBucket: Record<any, any>;
  // header: Record<any, any>;
  // accessKeyId: string;
  // secretAccessKey: string;
  // metadata: Record<any, any>;
  // curToken: string,
  // timestamp: string,
  token: string;
  AESKEY: string;
  // refreshToken: string,
  // category: string;
}

/**
 * 用户信息
 * @methods setUserInfos 设置用户信息
 */
export const useUserInfo = defineStore("userInfo", {
  state: (): UserInfosState => ({
    userInfo: {
      roles: [""],
      //department_dispatch 部门派单人 徐如玲
      //area_dispatch 业务负责人 厉健 

    },
    token: '',
    AESKEY: ''
  }),
  getters: {
    getToken(): string {
      return this.token || "";
    },
  },
  actions: {

    logOut() {
      this.$reset()
      sessionStorage.clear()

    },


    async setToken(code: string, type: string) {
      try {
        let res = await getUserTokenAPI({ [type]: code })
        const { accessToken, tokenType } = res.data
        this.token = `${tokenType} ${accessToken}`
        this.AESKEY = res.data.key
        await this.setUserInfo()
      } catch (error) {

      }

    },
    async setUserInfo() {
      await getUserInfoAPI().then(res => {
        this.userInfo = { ...res.data, roles: [res.data.primaryRoleCode] }
      })
    },

  },

  persist: {
    // enabled: true,
    key: VITE_MODE + "taskhub_PC_useUserInfo",
    storage: sessionStorage,
    paths: [
      "userInfo",
      "token",
      'AESKEY'
    ],
  },
});
