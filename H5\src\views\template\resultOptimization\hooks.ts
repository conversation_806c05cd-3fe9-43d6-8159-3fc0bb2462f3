
import { PopoverAction } from "vant";
import { getlistStaffRole } from "@/hooks_modules/getlistStaffRole";
import { getResultOptimizationListAPI } from "@/api/home";
import { useRouter } from "vue-router";
export const resultOptimizationHooks = function () {
    const { roleList } = getlistStaffRole()
    const router = useRouter()
    type List = {
        name: string
        avgHuoli: number
        areaId: string | null
        areaIdL2: string | null
        id: string
        [key: string]: any
    }
    const curSelectType = reactive({
        text: '全部', 
        value: ''
    })





    const tableList = ref<List[]>([])
    function resultOptimizationList() {
        let params = {
            staffRole: curSelectType.value
        }
        getResultOptimizationListAPI(params).then((res: any) => {
            if (res.code == 200) {
                console.log(res);
                
                tableList.value = res.data
            }

        })
    }

    //切换条线
    function onSelect(action: PopoverAction) {
        if (curSelectType.value == action.value) return
        curSelectType.text = action.text
        curSelectType.value = action.value
        resultOptimizationList()
    }

    function GO(item: List) {
        if (item.areaId) {
            router.push({
                path: '/resultOptimizationdDetailList', query: {
                    areaName: item.name,
                    areaId: item.areaId,
                    staffRole: curSelectType.value,
                    staffRoleName: curSelectType.text
                }
            })
        }


    }

    onMounted(() => {
       
    })



    onUnmounted(() => {

    });


    return {
        roleList,
        curSelectType,
        tableList,
        onSelect,
        GO,
        resultOptimizationList,
    }

}


