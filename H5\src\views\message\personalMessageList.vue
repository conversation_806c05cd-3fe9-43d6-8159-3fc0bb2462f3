<template>
    <div class="container_">
        <div class="container__header" v-if="!route.query.headerIsHide">
            <cusTabs :tabList="tabList" v-model:curSelectTab="curSelectTab" @changeSelectTab="changeSelectTab">
            </cusTabs>

        </div>
        <div class="container_body">
            <div v-for="item, index in messagesList" :key="index" class="message-body">
                <div class="message-header" @click="viewDetail(item)">
                    <img src="@/assets/defaultAvatar.png" alt="头像" class="avatar">
                    <div class="message-info">
                        <div class="message-title">{{ item.title }}</div>
                        <div class="message-time">{{ item.createTime }}</div>
                    </div>
                </div>
                <div class="message-content">
                    <span class="mr-3">内容:</span>
                    <span>{{ item.content }}</span>
                </div>
                <div class="message-content">
                    <span class="mr-3">附件:</span>
                    <div class="attachment-images mt-3">
                        <van-image width="89" height="89" :radius="5" v-imgPreview="[item.annexUrl, indexE]" lazy-load
                            fit="cover" v-for="attachment, indexE in item.annexUrl" :key="attachment" :src="attachment"
                            alt="附件"></van-image>
                    </div>
                </div>
            </div>
            <van-empty description="暂无数据" v-if="messagesList.length == 0" />
        </div>
        <van-popup v-model:show="showDetail" position="bottom" round class="selected-users-popup"
            :style="{ width: '100%', minHeight: '45%' }">
            <div class="container_body">
                <div class="message-body">
                    <div class="message-header">
                        <img src="@/assets/defaultAvatar.png" alt="头像" class="avatar">
                        <div class="message-info">
                            <div class="message-title">{{ messageObj.title }}</div>
                            <div class="message-time">{{ messageObj.createTime }}</div>
                        </div>
                    </div>
                    <div class="message-content">
                        <span class="mr-3">内容:</span>
                        <span>{{ messageObj.content }}</span>
                    </div>
                    <div class="message-content">
                        <span class="mr-3">附件:</span>
                        <div class="attachment-images mt-3">
                            <van-image width="89" height="89" :radius="5" v-imgPreview="[messageObj.annexUrl, indexE]"
                                lazy-load fit="cover" v-for="attachment, indexE in messageObj.annexUrl"
                                :key="attachment" :src="attachment" alt="附件"></van-image>
                        </div>
                    </div>
                </div>

            </div>


        </van-popup>


    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import { getMessageListBySenderAPI, readMessageDetailAPI } from "@/api/message";
import { useRoute } from "vue-router";
const route = useRoute()
interface Message {
    id: string;
    /** 消息标题 */
    title: string;
    /** 消息内容 */
    content: string;
    /** 附件URL，多个附件用逗号分隔 */
    annexUrl?: string[];
    /** 消息类型 */
    msgType: number;
    /** 创建人 */
    createBy?: string | null;
    /** 创建时间 */
    createTime: string;
    /** 阅读状态：0-未读，1-已读 */
    readFlag: number;
    [key: string]: any
}



const messagesList = ref<Message[]>([])
const messageObj = reactive<Message>({
    id: '',
    title: '',
    content: '',
    annexUrl: [],
    msgType: 0,
    createBy: null,
    createTime: '',
    readFlag: 0
})
const showDetail = ref(false)
const tabList = ref([
    { text: '未读消息', value: '未读消息' },
    { text: '已读消息', value: '已读消息', },

])
const curSelectTab = ref('未读消息')
function changeSelectTab(params: any) {

    loadMessageListBySender()
}


function loadMessageListBySender() {
    getMessageListBySenderAPI({ createBy: route.query.id, readFlag: curSelectTab.value == '未读消息' ? 0 : 1 }).then((res: any) => {
        if (res.code == 200) {
            for (const item of res.data) {
                item.annexUrl = item.annexUrl ? item.annexUrl.split(',') : []
            }
            messagesList.value = res.data.filter((item: any) => item.msgType == 1)
        }
    })
}
function viewDetail(row: Message) {
    readMessageDetailAPI({ id: row.id }).then((res: any) => {
        if (res.code == 200) {
            res.data.annexUrl = res.data.annexUrl ? res.data.annexUrl.split(',') : []
            for (const key in messageObj) {
                if (Object.prototype.hasOwnProperty.call(messageObj, key)) {
                    messageObj[key] = res.data[key];
                }
            }

            showDetail.value = true
            loadMessageListBySender()
        }
    })
}
onMounted(() => {
    curSelectTab.value = String(route.query.readFlag ?? '未读消息')
    loadMessageListBySender()
})

</script>

<style lang="scss" scoped>
.container_ {
    min-height: 100%;
}

.container__header {
    justify-content: flex-start;
}

.message-body {
    margin-bottom: 10px;
    border-bottom: 1px solid #EEF5FF;
    padding: 10px 0px;

    .message-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
    }

    .message-info {
        display: flex;
        flex-direction: column;

        .message-title {
            font-weight: 500;
            font-size: 16px;
            color: #3D3D3D;
        }

        .message-time {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            margin-top: 5px;
        }
    }

    .message-content {
        margin-bottom: 10px;
        font-weight: 400;
        font-size: 12px;
        color: #999999;

        .attachment-images {
            display: grid;
            grid-template-columns: repeat(3, 1fr);

            // gap: 10px;
        }

    }
}
</style>