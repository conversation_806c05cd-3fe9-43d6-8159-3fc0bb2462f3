<template>
    <div class="card container_">
        <iframe class="chatBi" src="" frameborder="0" id="chatBi"></iframe>
    </div>
</template>

<script setup lang="ts">
import { useUserInfo } from "@/stores/userInfo";
const userInfoStore = useUserInfo();



onMounted(() => {
    const iframe = document.getElementById('chatBi') as HTMLIFrameElement;
    if (iframe) {

        // 设置 iframe 的加载完成后的回调
        iframe.src = ''
        // 设置cookie
        const accessToken = userInfoStore.getToken;
        const accessToken_ = accessToken.replace('Bearer ', '');

        // 确保 contentWindow 可用后设置 cookie
        const iframeWindow = iframe.contentWindow as Window;
        iframeWindow.document.cookie = `accessToken=${accessToken_}; path=/`;
        document.cookie = `accessToken=${accessToken_}; path=/`;

        // // 设置 iframe 的 src
        setTimeout(() => {
            iframe.src = userInfoStore.chatBiUrl;
        }, 0);




    }
});


</script>

<style lang="scss" scoped>
.card {
    height: 100%;
    background: #fbfcff;
    // overflow-x: hidden;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px 10px 0px 0px;
}

.chatBi {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    box-sizing: border-box;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-bottom: 50px;
}
</style>