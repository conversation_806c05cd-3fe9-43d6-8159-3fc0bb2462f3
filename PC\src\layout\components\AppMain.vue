<template>
  <section class="app-main" ref="app_main">
    <router-view v-slot="{ Component, route }">
      <template v-if="Component">
        <Transition name="fade-transform" mode="out-in">
          <KeepAlive v-if="route.meta.keepAlive">
            <component
              class="viewcontent"
              :is="Component"
              :key="route.path"
            ></component>
          </KeepAlive>
          <component
            class="viewcontent"
            :is="Component"
            :key="route.path"
            v-else
          ></component>
        </Transition>
      </template>
    </router-view>
  </section>
</template>

<script setup>
// import { useWindowScroll, useElementSize, watchThrottled } from "@vueuse/core";

// import { computed, provide, ref,inject } from "vue";
// import { useRoute, useRouter } from "vue-router";
// const route = useRoute();
// const router = useRouter();
// const { y: scroll_Top } = useWindowScroll();

// router.afterEach((to, from) => {
//   let routerScrollTop =
//     JSON.parse(sessionStorage.getItem("routerScrollTop")) ?? {};
//   routerScrollTop[from.path] = scroll_Top.value;
//   sessionStorage.setItem("routerScrollTop", JSON.stringify(routerScrollTop));
// });
</script>

<style scoped>
.app-main {
  min-height: calc(100vh - 40px);
  width: 100%;
  position: relative;
  overflow: hidden;
  background-color: #f0f2f5;
  /* background-color: #171617; */
  padding: 16px;
}

.fixed-header + .app-main {
  padding-top: 60px;
}

.viewcontent {
  width: 100%;
  min-height: calc(100vh - 120px);
  background: #ffffff;
  border-radius: 10px 10px 10px 10px;
  overflow-y: scroll;
  overflow-x: hidden;
  padding: 16px;
  position: relative;
}

.viewcontent::-webkit-scrollbar {
  display: none;
}
</style>

<style lang="scss" scoped>
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
