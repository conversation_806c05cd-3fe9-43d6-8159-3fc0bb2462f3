<template>
    <div class="card">
        <div class=' container_ '>
            <div class="container__header">
                <div>
                    <img src="@/assets/debriefingDetail.svg" alt="" srcset="">
                    <span class="ml-6">{{ title }}</span>
                </div>
            </div>
            <div class="container_body ">
                <myCharts class="myEcharts" :options="option"></myCharts>
            </div>
        </div>
        <div class=' container_ mt-6'>
            <div class="container_body ">
                <div class="card-list" style="border-bottom: 1px solid #EEF5FF ;">
                    <div class="card overvirwCard">

                        <div class="card-body" style="background-color: #E9FDF2;">
                            <div class="status-block">
                                <img src="@/assets/login_Y.svg" alt="" srcset="">

                                <div class="info">
                                    <p class="status-row">
                                        <span>已登录人数</span>
                                        <span class="color2">{{ loginData.loginCount }}</span>
                                    </p>
                                    <p class="status-row">
                                        <span>占比</span>
                                        <span class="color2">{{ loginData.loginRate }}%</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="card-body" style="background: #FFF3F2;">
                            <div class="status-block">
                                <img src="@/assets/login_N.svg" alt="" srcset="">

                                <div class="info">
                                    <p class="status-row">
                                        <span>未登录人数</span>
                                        <span class="color0">{{ loginData.unLoginCount }}</span>
                                    </p>
                                    <p class="status-row">
                                        <span>占比</span>
                                        <span class="color0">{{ loginData.unLoginRate }}%</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <div class="card-list mt-25">
                    <div class="card" v-for="item, index in tabList" :key="index">
                        <div class="card-header">
                            <span class="role-name">{{ item.areaName ?? item.areaNameL2 }}</span>
                            <div class="go-button" @click="GO(item)">
                                <span>GO</span>
                                <van-icon name="arrow" color="#fff" />
                            </div>
                        </div>
                        <div class="card overvirwCard" style="margin-bottom: 6px">

                            <div class="card-body" style="background-color: #E9FDF2;">
                                <div class="status-block">
                                    <img src="@/assets/login_Y.svg" alt="" srcset="">

                                    <div class="info">
                                        <p class="status-row">
                                            <span>已登录人数</span>
                                            <span class="color2">{{ item.loginCount }}</span>
                                        </p>
                                        <p class="status-row">
                                            <span>占比</span>
                                            <span class="color2">{{ item.loginRate }}%</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body" style="background: #FFF3F2;">
                                <div class="status-block">
                                    <img src="@/assets/login_N.svg" alt="" srcset="">

                                    <div class="info">
                                        <p class="status-row">
                                            <span>未登录人数</span>
                                            <span class="color0">{{ item.unLoginCount }}</span>
                                        </p>
                                        <p class="status-row">
                                            <span>占比</span>
                                            <span class="color0">{{ item.unLoginRate }}%</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <van-empty description="暂无数据" v-if="tabList.length == 0" />
            </div>




        </div>

    </div>



</template>

<script setup lang="ts">
import myCharts from "@/components/charts/myCharts.vue";
import { getTodayTotalCountAPI, getLoginDetailAPI } from "@/api/home";
import { useUserInfo } from "@/stores/userInfo";
import { useRouter } from "vue-router";
import { mapSort } from "@/utils/mapSort";


interface LoginData {
    loginCount: number; // 登录次数
    loginRate: number;  // 登录率（百分比）
    totalCount: number; // 总人数
    unLoginCount: number; // 未登录人数
    unLoginRate: number; // 未登录率（百分比）
    [key: string]: any
}
interface AreaData extends LoginData {
    areaId: string | null;     // 区域 ID（可能为空）
    areaIdL2: string | null;          // 二级区域 ID
    areaName: string | null;   // 区域名称（可能为空）
    areaNameL2: string;        // 二级区域名称
    staffName: string | null;  // 员工姓名（可能为空）
    staffRole: string | null;  // 员工角色（可能为空）
    status: string | null;     // 状态（可能为空）
}


const userInfoStore = useUserInfo()
const router = useRouter()
const loginData = reactive<LoginData>({
    loginCount: 0,        // 当前登录人数
    loginRate: 0,         // 登录率
    totalCount: 0,      // 总人数
    unLoginCount: 0,    // 未登录人数
    unLoginRate: 0      // 未登录率
})

const tabList = ref<AreaData[]>([])



const option = computed(() => {
    return {
        legend: {
            show: false,
        },

        tooltip: {
            trigger: "axis",
            backgroundColor: "rgba(0, 0, 0, 0.63)", //设置背景颜色
            textStyle: {
                fontFamily: "proud",
                color: "#fff",
            },
            borderColor: "rgba(255,255,255, .5)",
            formatter: function (params: any) {

                params = params[0]
                console.log(params);
                return `<span style="color:${params.color}">${params.marker}${params.name}: ${params.value}%</span>`;
            },
        },

        grid: {
            borderColor: "#ECF0F3",
            borderWidth: 0,
            top: "8%",
            left: "0%",
            right: "6%",
            bottom: "10%",
            containLabel: true,
        },
        xAxis: {
            type: "category",
            data: tabList.value.map(item => item.areaName ?? item.areaNameL2),
            boundaryGap: false,
            axisLine: {
                show: true,
                lineStyle: {
                    color: "#71A9FF", // 设置 x 轴轴线颜色
                },
            },
            axisTick: {
                show: false,
            },
            axisLabel: {
                color: "#3D3D3D",
                fontSize: 12,
                fontFamily: "Source Han Sans-Regula",
                rotate: 45, // 设置刻度标签的旋转角度，以度为单位
            },
        },
        yAxis: {
            type: "value",
            minInterval: 1,
            axisLine: {
                show: false,

            },
            axisTick: {
                show: false,
            },
            axisLabel: {
                color: "#71A9FF",
                fontSize: 12,
                fontWeight: 500,
                fontFamily: "proud",
                formatter: function (value: any) {
                    return value === 0 ? value : `${value}%`;
                },
                margin: 15, // 调整轴线和刻度标签的距离
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: "dashed",
                    color: "#ECF0F3",
                },
            },
        },
        dataZoom: [{
            show: true,
            height: 20,
            xAxisIndex: [0],
            bottom: 10,
            start: 10,
            end: 80,
            handleIcon: 'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
            handleSize: '100%',
            handleStyle: {
                color: '#d3dee5'

            },
            textStyle: {
                color: '#fff'
            },
            borderColor: '#90979c'

        }, {
            type: 'inside',
            show: true,
            height: 20,
            start: 1,
            end: 35
        }],
        series: [
            {
                name: "登录率",
                data: tabList.value.map(item => item.loginRate),
                type: "line",
                smooth: true,
                symbolSize: 6,
                areaStyle: {
                    opacity: 0.3,
                    color: {
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: "#679FFF", // 渐变起始颜色
                            },
                            {
                                offset: 1,
                                color: "#F0F2F5", // 渐变结束颜色
                            },
                        ],
                    },
                },
                itemStyle: {
                    color: "#1A76FF",
                    borderColor: "#1A76FF", // 设置边框颜色为蓝色，与圆心一致
                    borderWidth: 2, // 可选：设置边框宽度，确保填充效果明显
                },
                lineStyle: {
                    color: "#6993FF",
                    width: 4,
                },
                label: {
                    show: true,
                    fontSize: 10,
                    color: "#3D3D3D",
                    formatter: '{c}', // 曲线上的数值标签添加百分号
                },
            },
        ],
    };
})



const title = computed(() => {
    const showOrgName = userInfoStore.userInfo.showOrgName
    if (showOrgName == '南京') return '南京分公司各区域登录情况分布'
    else {
        return `南京分公司${showOrgName}区登录情况分布`
    }

})


function GO(row: any) {
    let path: string
    let query: any = {}
    if (row.areaId) {
        path = '/loginRateAreaL2List'
        query.areaId = row.areaId
        query.areaName = row.areaName

    } else {
        path = '/loginRateAreaL2Staff'
        query.areaL2Id = row.areaIdL2
        query.areaL2Name = row.areaNameL2
    }

    router.push({
        path: path,
        query: query
    })
}



//获取今日登录列表
async function loadLoginDetail() {
    const res: any = await getLoginDetailAPI()
    if (res.code == 200) {
        if (res.data.length > 0) {
            const firstItem = res.data[0]
            if (firstItem.areaId) {
                res.data = mapSort(res.data, 'areaName')
            }
        }

        tabList.value = res.data
    }
}




//获取今日登录数据
async function loadTodayTotalCount() {
    const res: any = await getTodayTotalCountAPI()
    if (res.code == 200) {
        for (const key in loginData) {
            if (Object.prototype.hasOwnProperty.call(loginData, key)) {
                loginData[key] = res.data[key]
            }
        }
    }

}



onMounted(() => {
    loadTodayTotalCount()
    loadLoginDetail()
})




</script>

<style lang="scss" scoped src="./index.scss"></style>