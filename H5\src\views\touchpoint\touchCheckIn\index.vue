<template>
    <div class="card container_">
        <div class="container__header">
            <div>
                <img src="../assets/touchpoint.svg" alt="" srcset="" class="card_icon" />
                <span class="ml-6">工作写实</span>
            </div>
        </div>
        <div class="form_card">
            <van-form required="auto" ref="formField" :show-error-message="true">
                <van-cell-group>
                    <van-field name="uploader" label="工作照片" label-align="left" input-align="right"
                        :rules="[{ required: true, message: '请拍摄照片' }]">
                        <template #input>
                            <div class="right_align">
                                <van-uploader :after-read="handleAfterRead" :before-read="handleBeforeRead"
                                    result-type="file" v-model="formData.fileList" capture="camera" preview-size="89"
                                    :before-delete="deleteFile" :max-count="3">
                                    <template #default>
                                        <div class="form_card_camera">
                                            <van-image width="28px" height="23px" :src="camera" />
                                        </div>
                                    </template>
                                </van-uploader>
                            </div>
                        </template>
                    </van-field>
                    <van-field v-model="formData.address" label-align="left" rows="1" autosize label="工作写实地址"
                        type="textarea" placeholder="请输入地址（选填）">
                    </van-field>
                    <van-field v-model="formData.description" :rules="[{ required: true, message: '请输入留言' }]"
                        label-align="left" autosize label="写实备注" type="textarea" maxlength="100" placeholder="请输入留言" />
                </van-cell-group>
                <van-field input-align="right" style="border: 0px;">
                    <template #input>
                        <span @click="navTo" class="sign_list">工作写实记录<van-icon name="arrow" /></span>
                    </template>
                </van-field>
            </van-form>
        </div>
        <div class="sure_btn">
            <van-button type="primary" :disabled="uploadLoading" :loading="loading"
                color=" linear-gradient( 90deg, #73ACFF 0%, #237CFF 100%)" block @click="submit">提交</van-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import camera from "../../template/MyDay/assets/images/camera.png";
import { UploaderFileListItem } from "vant";
import { useRoute, useRouter } from "vue-router";
import { touchpointSaveAPI } from "@/api/touchpoint";
// import { uploadFileAPI, deleteFile } from "@/api/common";
import { showToast, showSuccessToast, showFailToast } from "vant";
import { debounce } from "@/utils/debounce";
import { uploadFileHooks } from "@/hooks_modules/upload_hooks";
const {
    handleAfterRead,
    handleBeforeRead,
    deleteFile,
    uploadLoading,
    formLine,
    formData
} = uploadFileHooks()
const router = useRouter();
let formField = ref();




const loading = ref(false)
const submit = debounce(() => {
    formField.value
        .validate()
        .then(() => {
            loading.value = true
            let params = {
                images: formLine.images.join(','),
                address: formData.address,
                description: formData.description
            }
            touchpointSaveAPI(params).then((res: any) => {
                if (res.code == 200) {
                    showSuccessToast("提交成功");
                    router.push({ path: '/touchDetail' })
                } else {
                    showFailToast('提交失败')
                }

            }).finally(() => {
                loading.value = false
            })



        })
        .catch(() => {
            showFailToast("请完善信息");

        })
}, 300, true)


function navTo() {
    router.push("/touchDetail");
}

</script>

<style lang="scss" scoped>
.card {
    min-height: 100%;
    background: #fff;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px 10px 0px 0px;
    position: relative;
}


.form_card {
    height: 80%;
    overflow: scroll;

    :deep(.van-field__control) {
        color: #A8A8A8;

    }

    ::v-deep .van-field {
        border-bottom: 1px solid #edf5ff;
    }
}

.form_card_camera {
    width: 89px;
    height: 89px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(103, 159, 255, 0.15);
}

.sure_btn {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 100px;
    width: calc(100% - 18px - 18px);
}

.sign_list {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 12px;
    color: #1a76ff;
    line-height: 13px;
    font-style: normal;
    text-transform: none;
}

.right_align {
    text-align: right;

    ::v-deep .van-uploader__wrapper {
        justify-content: flex-end !important;
    }
}

.card_icon {
    object-fit: contain;
}
</style>