<template>
    <div>
        <van-popup v-model:show="popoverIsShow" round position="bottom" teleport="body">
            <van-picker title="人员列表" :columns="staffList" :columns-field-names="{
                text: 'staffName',
                value: 'staffCode'
            }" @confirm="confirmStaffCode" @cancel="popoverIsShow = false">
                <template #title>
                    <van-search class="mt-5 mb-5" v-model="searchStaffName" placeholder="请输入搜索关键词" />
                </template>
            </van-picker>
        </van-popup>
    </div>
</template>

<script setup>
import { useUserInfo } from "@/stores/userInfo";
const props = defineProps({

})
const userInfoStore = useUserInfo()

const emit = defineEmits(['confirmStaffCode'])

const searchStaffName = defineModel('searchStaffName', {
    type: String,
    default: ''
})
const popoverIsShow = defineModel('popoverIsShow', {
    type: Boolean,
    default: false
})

const listData = computed(() => {

    let list = [{ staffCode: '全部', staffName: '全部人员', staffId: '', orgId: '' }, ...userInfoStore.getStaffList]
    let index = list.findIndex(item => item.staffCode == userInfoStore.userInfo.staffCode)
    const myself = list[index]
    list.splice(index, 1)
    list.splice(1, 0, myself)
    return list
})
const staffList = computed(() => {
    const input = searchStaffName.value.toLowerCase();

    return listData.value.filter(item => {
        const staffName = item.staffName ? item.staffName.toLowerCase() : '';
        const staffRoleName = item.staffRoleName ? item.staffRoleName.toLowerCase() : '';
        const staffCode = item.staffCode ? item.staffCode.toLowerCase() : '';

        // 检查是否匹配 staffName、staffRoleName 或 staffCode
        return (
            staffName.includes(input) ||
            staffRoleName.includes(input) ||
            staffCode.includes(input)
        );
    });
})

function confirmStaffCode(params) {
    emit('confirmStaffCode', params)
}
</script>

<style lang="scss" scoped></style>