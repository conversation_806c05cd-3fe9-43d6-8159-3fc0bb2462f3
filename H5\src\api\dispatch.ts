import request from "../utils/request";
import configAxios from './config'



//客户页面
export function getTicketByCustomerNameAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/dispatch/ticket/byCustomerName`,
        method: "post",
        data,
    });
}

//派单页面
export function getCollectionPageAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/dispatch/collection/page`,
        method: "post",
        data,
    });
}


//派单列表页
export function getTicketPageAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/dispatch/ticket/page`,
        method: "post",
        data,
    });
}


//获取部门列表
export function getDepartmentListAPI() {
    return request({
        url: `${configAxios.taskHubServer}/common/dispatch/department/list`,
        method: "POST",
    });
}


//提交派单表单
export function updataDispatchReceiptAPI(data:any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/dispatch/ticket/receipt`,
        method: "POST",
        data
    });
}