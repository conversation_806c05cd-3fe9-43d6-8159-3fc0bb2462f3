import vue from "@vitejs/plugin-vue";
import requireTransform from "vite-plugin-require-transform";
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from '@vant/auto-import-resolver'
import AutoImport from "unplugin-auto-import/vite";
// import basicSsl from '@vitejs/plugin-basic-ssl'
import compression from 'vite-plugin-compression';
// import viteImagemin from 'vite-plugin-imagemin'

import { defineConfig, loadEnv, ConfigEnv } from "vite";
import path from "path";
// import commonjs from "vite-plugin-commonjs";
const viteConfig = defineConfig((mode: ConfigEnv) => {

  const env = loadEnv(mode.mode, process.cwd());

  // const buildType = env.VITE_BUILD_TYPE;
  return {
    plugins: [
      vue(),
      // commonjs(),
      // basicSsl(), // 配置证书
      // compression({
      //   algorithm: 'gzip', // or 'gzip'
      //   ext: '.gz', // or '.gz'
      // }),
      requireTransform({
        fileRegex: /.ts$|.tsx$|.vue$/,
      }),
      AutoImport({
        include: [
          /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
          /\.vue$/,
          /\.vue\?vue/, // .vue
        ],
        imports: [
          'vue',
          'vue-router',
          'pinia',
          '@vueuse/core',
        ],
        dts: 'types/auto-imports.d.ts',
        resolvers: [VantResolver()],
      }),
      Components({
        dts: true,
        // dirs: ['src/components/template'],
        resolvers: [VantResolver()],
        types: [],
      })
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    css: {
      devSourcemap: true,
      // 确保样式文件的热更新
      modules: {
        localsConvention: 'camelCase'
      },
      preprocessorOptions: {
        scss: {
          api: 'modern', // or 'modern'
          additionalData: `@import "@/styles/mixin.scss";`  // 
        },
      },
    },
    base: env.VITE_BASE_ASSEST, // 打包路径


    server: {
      host: true,
      // port: env.VITE_PORT as unknown as number, // 服务端口号
      open: false, // 服务启动时是否自动打开浏览器
      // https: false,
      hmr: true, // 开启热更新
      proxy: {
        [env.VITE_APP_BASE_API]: {
          target: env.VITE_BASE_URL,
          changeOrigin: true,
          secure: false,
          configure: (proxy) => {
            proxy.on('proxyReq', (proxyReq, req, res) => {
              // 打印原始请求对象（包括 URL 和头部信息）
              // console.log('Request URL:', req.url);  // 查看请求的 URL
              // console.log('Request Headers:', req.headers);  // 查看请求头部信息

              // 如果需要，可以从 proxyReq 中获取更多详细信息
              // console.log('Proxy Request URL:', proxyReq.path); // 获取代理后的请求路径
              // console.log('Proxy Request Headers:', proxyReq.getHeaders()); // 获取代理请求的头部信息

              // 移除 'X-Forwarded-For' 头
              // proxyReq.removeHeader('X-Forwarded-For');
            });
          },

          rewrite: (path) =>


            path.replace(new RegExp("^" + env.VITE_APP_BASE_API), env.VITE_MODE == 'localhost' ? '' : env.VITE_APP_BASE_API),

          // bypass(req, res, options) {

          //   const proxyURL = options.target + options.rewrite(req.url);
          //   res.setHeader('x-req-proxyURL', proxyURL) // 将真实请求地址设置到响应头中
          // },

        },
        // "^/api": {
        //   target: "https://amb.u2i.net",
        //   changeOrigin: true,
        //   secure: false,
        // },


      },
    },
    build: {
      sourcemap: mode.command === "serve",
      // minify: mode.command === "build" ? "esbuild" : false,
      minify: "terser",
      chunkSizeWarningLimit: 1500,
      emptyOutDir: true,
      outDir: 'dist',
      reportCompressedSize: false,
      terserOptions: {
        compress: {
          drop_console: env.VITE_MODE === 'production',
          drop_debugger: env.VITE_MODE === 'production',
        },
      },

      // esbuild: {
      //   drop: mode.command === "build" ? ['console', 'debugger'] : [], //去除console和debugger
      // },
      rollupOptions: {
        commonjsOptions: {
          transformMixedEsModules: true,
        },
        output: {
          chunkFileNames: 'js/[name]-[hash].js', // 引入文件名的名称
          entryFileNames: 'js/[name]-[hash].js', // 包的入口文件名称
          assetFileNames: '[ext]/[name]-[hash].[ext]', // 资源文件像 字体，图片等
          // assetFileNames: ({ name }) => {
          //   if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(name ?? '')) {
          //     return 'img/[name]-[hash].[ext]'; // 将图片放到 img 目录
          //   }
          //   return '[ext]/[name]-[hash].[ext]';
          // },
          // 将 node_modules 三方依赖包最小化拆分
          manualChunks(id) {

            if (id.includes('utils/crypto/index')) {
              return 'crypto_util'; // 将该文件单独打包为 `crypto` chunk
            }

            if (id.includes('node_modules')) {
              const paths = id.toString().split('node_modules/')
              if (paths[2]) {
                return paths[2].split('/')[0].toString()
              }

              return paths[1].split('/')[0].toString()
            }

          },
        },
      },


    },
    define: {
      // enable hydration mismatch details in production build
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'true'
    },

    optimizeDeps: {
      /**
       * 依赖预构建，vite 启动时会将下面 include 里的模块，编译成 esm 格式并缓存到 node_modules/.vite 文件夹，
       * 页面加载到对应模块时如果浏览器有缓存就读取浏览器缓存，如果没有会读取本地缓存并按需加载
       * 尤其当您禁用浏览器缓存时（这种情况只应该发生在调试阶段）必须将对应模块加入到 include 里，
       * 否则会遇到开发环境切换页面卡顿的问题（vite 会认为它是一个新的依赖包会重新加载并强制刷新页面），
       * 因为它既无法使用浏览器缓存，又没有在本地 node_modules/.vite 里缓存
       * 温馨提示：如果你使用的第三方库是全局引入，也就是引入到 src/main.ts 文件里，
       * 就不需要再添加到 include 里了，因为 vite 会自动将它们缓存到 node_modules/.vite
       */
      include: [
        'pinia',
        'lodash-es',
        'axios',
        'echarts',
      ],
      // 打包时强制排除的依赖项
      exclude: [
        'vant',
        '@vant/use',
      ],
    },
  };
});

export default viteConfig;
