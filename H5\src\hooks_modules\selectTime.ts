import { ref, onMounted, onUnmounted, computed } from "vue";
import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { formatDate, constgetBeforeDayDate } from "@/utils/loadTime";
type ParamsProps = {
    source: string,
    callback: (data?: any) => void;
}

export const selectTime = function (params: Partial<ParamsProps> = {}) {
    const { callback } = params
    const {
        formline,
        defaultDate,
        maxDate,
        calendarIsShow,
        onConfirm,
    } = searchHooks({
        callback: confirm_Time,
    })

    const minDate = new Date(2024, new Date().getMonth(), 1)
    const actionsList = ref([
        { text: '当日', value: '1' },
        { text: '近一周', value: '2' },
        { text: '当月', value: '3' },
    ])
    const curSelectTime = reactive({
        text: '当日',
        value: '1',
    })

    const timeSection = computed(() => {
        let startTime: Date
        switch (curSelectTime.value) {
            //当日
            case '1':
                return `${formatDate(new Date(), 'yyyy-MM-dd')}`

            // 当月
            case '3':
                startTime = new Date(new Date().getFullYear(), new Date().getMonth(), 1)
                return `${formatDate(startTime, 'MM-dd')}-${formatDate(new Date(), 'MM-dd')}`
            // 近一周
            case '2':
                startTime = constgetBeforeDayDate(new Date(), 6)
                return `${formatDate(startTime, 'MM-dd')}-${formatDate(new Date(), 'MM-dd')}`
            case '4':
                return `${formatDate(formline.startTime, 'MM-dd')}-${formatDate(formline.endTime, 'MM-dd')}`
        }
    })


    function changeSelete(params: any) {
        if (curSelectTime.value == params.value) return
        curSelectTime.value = params.value
        curSelectTime.text = params.text
        let startTime: Date
        switch (params.value) {
            //当日
            case '1':
                formline.startTime = formatDate(new Date(), 'yyyy-MM-dd')
                formline.endTime = formatDate(new Date(), 'yyyy-MM-dd')
                defaultDate.value = [new Date(), new Date()]
                break;
            // 当月
            case '3':
                startTime = new Date(new Date().getFullYear(), new Date().getMonth(), 1)
                formline.startTime = formatDate(startTime, 'yyyy-MM-dd')
                formline.endTime = formatDate(new Date(), 'yyyy-MM-dd')
                defaultDate.value = [startTime, new Date()]
                break;
            // 近一周
            case '2':
                startTime = constgetBeforeDayDate(new Date(), 6)
                formline.startTime = formatDate(startTime, 'yyyy-MM-dd')
                formline.endTime = formatDate(new Date(), 'yyyy-MM-dd')
                defaultDate.value = [startTime, new Date()]
                break;
        }
        callback && callback()
    }





    //确定选择时间 
    function confirm_Time() {

        getTimeRangeLabel(formline.startTime, formline.endTime)

        const { text, value } = getTimeRangeLabel(formline.startTime, formline.endTime)
        curSelectTime.text = text
        curSelectTime.value = value
        callback && callback()
    }


    function getTimeRangeLabel(startDate: string, endDate: string): { text: string; value: string } {
        const now = stripTime(new Date()); // 当前日期，仅保留日期部分
        const start = stripTime(new Date(startDate));
        const end = stripTime(new Date(endDate));

        const diffInDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1; // 包含起始日期
        if (diffInDays === 1 && isSameDay(start, now) && isSameDay(end, now)) {
            return {
                text: '当日',
                value: '1'
            };
        } else if (isCurrentMonthRange(start, end, now)) {
            return {
                text: '当月',
                value: '3'
            };
        } else if (diffInDays === 7 && isLastWeekRange(start, end, now)) {
            return {
                text: '近一周',
                value: '2'
            };
        } else {
            return {
                text: '自选',
                value: '4'
            };
        }
    }

    // 辅助函数：移除时间部分，只保留日期
    function stripTime(date: Date): Date {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate());
    }

    // 辅助函数：判断两日期是否为同一天
    function isSameDay(date1: Date, date2: Date): boolean {
        return date1.getTime() === date2.getTime();
    }

    // 辅助函数：判断是否为当月范围
    function isCurrentMonthRange(start: Date, end: Date, now: Date): boolean {
        const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
        return (
            isSameDay(start, firstDayOfMonth) && // 开始日期是当月第一天
            isSameDay(end, now) // 结束日期是今天
        );
    }

    // 辅助函数：判断范围是否为“近一周”
    function isLastWeekRange(start: Date, end: Date, now: Date): boolean {
        const sevenDaysAgo = new Date(now);
        sevenDaysAgo.setDate(now.getDate() - 6); // 计算7天前的日期

        return (
            isSameDay(end, now) && // 结束日期是今天
            isSameDay(start, sevenDaysAgo) // 开始日期是7天前
        );
    }
    // 生命周期钩子
    onMounted(() => {

    });

    onUnmounted(() => {

    });


    return {
        formline,
        defaultDate,
        maxDate,
        minDate,
        calendarIsShow,
        actionsList,
        curSelectTime,
        timeSection,
        changeSelete,
        onConfirm,
    }

}


