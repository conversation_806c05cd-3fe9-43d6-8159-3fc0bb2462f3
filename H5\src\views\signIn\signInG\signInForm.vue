<template>
  <div class="card container_">
    <!-- <div class="container__header">
        <div>
          <img src="../../template/MyDay/assets/images/chczIcon.png" alt="" srcset="" class="card_icon" />
          <span class="ml-6">晨会出征</span>
        </div>
      </div> -->
    <div class="form_card">
      <van-form required="auto" ref="formField">
        <van-cell-group>
          <van-field name="fileList" label="出征照" label-align="left" input-align="right"
            :rules="[{ required: true, message: '请拍摄照片' }]">
            <template #input>
              <div class="right_align">
                <van-uploader :after-read="handleAfterRead" :before-read="handleBeforeRead" v-model="formData.fileList"
                  capture="camera" preview-size="89" :max-count="3">
                  <template #default>
                    <div class="form_card_camera">
                      <van-image width="28px" height="23px" :src="camera" />
                    </div>
                  </template>
                </van-uploader>
              </div>
            </template>
          </van-field>
          <van-field v-model="formLine.shouldNumber" name="number" label="应到人数" placeholder="应到人数" label-align="left"
            input-align="right" type="digit" :rules="[{ required: true ,message: '请填写应到人数'}]">
            <template #button>
              <span>人</span>
            </template>
          </van-field>
          <van-field v-model="formLine.realNumber" name="signNumber" label="出征人数" label-align="left" input-align="right"
            type="digit" placeholder="请输入" :rules="[{ required: true, message: '请填写出征人数' }]">
            <template #button>
              <span>人</span>
            </template>
          </van-field>
          <van-field v-model="formLine.reason" name="desc" label="出征说明" label-align="left" rows="2" autosize
            type="textarea" maxlength="1000" placeholder="出征人数与当班人员不符时，请填写说明！" show-word-limit
            :rules="[{ required: true, message: '请填写说明' }]" />
        </van-cell-group>
      </van-form>
    </div>
    <div class="sure_btn">
      <van-button type="primary" color=" linear-gradient( 90deg, #73ACFF 0%, #237CFF 100%)" block
        @click="submit">确认</van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast, showSuccessToast, showFailToast } from "vant";
import camera from "../../template/MyDay/assets/images/camera.png";
import { uploadFileHooks } from "@/hooks_modules/upload_hooks";
import { signSaveAPI } from "@/api/morning";
import { debounce } from "@/utils/debounce";
const router = useRouter();
let formField = ref();


const {
  handleAfterRead,
  handleBeforeRead,
  formLine,
  formData
} = uploadFileHooks()



const submit = debounce(() => {
  formField.value
    .validate()
    .then(() => {
      let params = {
        images: formLine.images.join(','),
        shouldNumber: formLine.shouldNumber,
        realNumber: formLine.realNumber,
        reason: formLine.reason,
      }

      signSaveAPI(params).then(res => {
        if (res.data) {
          showSuccessToast("提交成功");
          router.push({ path: '/signInDiary' })
        } else {
          showFailToast("晨会出征每日只可打卡一次");
        }

      })

    })
    .catch(() => {
      showFailToast("请完善信息");
    });
}, 300, true)

</script>

<style lang="scss" scoped>
@import "../formCard.scss";
</style>