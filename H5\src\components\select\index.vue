<template>

    <div class="custom-select">
        <div class="select-trigger" @click="toggleDropdown">
            <span>{{ defalueTitle }}</span>
            <van-icon name="play" class="play" color="#1A76FF" />
        </div>
        <ul v-if="isOpen" class="select-options">
            <li v-for="(item, index) in options" :key="index" @click="selectOption(item)"
                :class="{ selected: item.value === selected }">
                {{ item.text }}
            </li>
        </ul>
    </div>

</template>

<script setup lang="ts">
import { onMounted } from "vue";
type Options = {
    text: string,
    value: string
}


const emits = defineEmits([''])
const props = defineProps({
    options: {
        type: Array,
        defalue: () => {
            return []
        }
    },
    defalueTitle: {
        type: String,
        defalue: ''
    },
})

const modelValue = defineModel(
    {
        type: String,
        default: ''
    })




const isOpen = ref<boolean>(false)
const selected = ref('Select an option')
const options = ref([
    { text: 'Option 1', value: '1' },
    { text: 'Option 2', value: '2' },
    { text: 'Option 3', value: '3' },
    { text: 'Option 4', value: '4' },
    { text: 'Option 5', value: '5' },
    { text: 'Option 6', value: '6' },
    { text: 'Option 7', value: '7' },
    { text: 'Option 8', value: '8' },
    { text: 'Option 9', value: '9' },
    { text: 'Option 10sssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssssss', value: '10' },
    { text: 'Option 11', value: '11' },
    { text: 'Option 12', value: '12' },


])

function toggleDropdown() {
    isOpen.value = !isOpen.value;;
}
function selectOption(option: Options) {
    selected.value = option.text;
    modelValue.value = option.value
    isOpen.value = false;
}

onMounted(() => {

})
</script>

<style lang="scss" scoped>
.custom-select {
    position: relative;
    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
}

.select-trigger {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    cursor: pointer;
    font-size: 14px;

    i {
        margin-left: 3px;
        transform: rotate(90deg);
    }


}

.arrow {
    border: solid black;
    border-width: 0 2px 2px 0;
    display: inline-block;
    padding: 4px;
    transform: rotate(45deg);
    transition: 0.2s;
}

.select-options {
    position: absolute;
    width: max-content;
    max-width: 80vw;
    border-radius: 5px 5px 5px 5px;
    border-top: none;
    max-height: 150px;
    overflow-y: auto;
    overflow-x: auto;
    background: #EDF5FF;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    left: 50%;
    transform: translateX(-50%);
}

.select-options li {
    padding: 10px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.select-options li:hover {
    background-color: #f5f5f5;
}

.select-options li.selected {
    background-color: #e6f7ff;
}
</style>