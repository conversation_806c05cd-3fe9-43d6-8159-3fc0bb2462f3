<template>
    <div>
        <component :is="item.moudule_" v-for="item in componentOption" :key="item.moudule_name"
            :ref="(el: HTMLDivElement) => item.ref_name && (refsArray[item.ref_name] = el)"
            v-on="generateEventHandlers(item.EventsList || [])"
            @="emitEventsListGenerateEventHandlers(item.emitEventsList || [])"
            v-bind="mergePropsAndVModelBindings(item.props, item.vModels)">
        </component>
    </div>


</template>

<script lang="ts" setup>
import componentList from "@/views/template/config";
import { useUserInfo } from "@/stores/userInfo";
import { ref, reactive, onMounted, ComponentPropsOptions, ComponentOptions } from "vue";
import { EventsHooks, ComponentOptionProps } from "@/hooks_modules/Eventhooks";
const { generateEventHandlers, emitEventsListGenerateEventHandlers, mergePropsAndVModelBindings } = EventsHooks()
const userInfoStore = useUserInfo();


const refsArray = ref<{ [key: string]: HTMLDivElement | null }>({});
// const componentOption: ComputedRef<ComponentOptionProps[]> = computed(() => {
//     let list: Array<ComponentOptionProps> = [
//         {
//             moudule_: componentList['userInfo'].moudule_,
//             moudule_name: 'userInfo',
//             ref_name: 'userInfo',
//         },
//         {
//             moudule_: componentList['commonlyUsed'].moudule_,
//             moudule_name: 'commonlyUsed',
//             ref_name: 'commonlyUsed',
//         },
//         {
//             moudule_: componentList['development'].moudule_,
//             moudule_name: 'development',
//             ref_name: 'development',
//         },
  
//     ]

//     return list
// })
const componentOption: ComputedRef<ComponentOptionProps[]> = computed(() => {
    let list = userInfoStore.getMenuList.filter((item: MenuItem) => item.menuCode === 'userData')[0]?.children || []
    const listOption = list.map((item: MenuItem) => {
        return {
            moudule_: componentList[item.menuCode].moudule_,
            moudule_name: item.menuCode,
            ref_name: item.menuCode,

        }
    })
    return listOption
})




onMounted(() => {


})




</script>


<style lang="scss" scoped></style>