<template>
    <div class='card container_'>
        <div class="container__header">
            <div>
                <router-link to="/test">
                    <img src="@/assets/comparativeCalculationAudit.svg" alt="" srcset="">
                    <span class="ml-6">商机审核</span>
                </router-link>

            </div>


            <cusTabs :tabList="tabList" v-model:curSelectTab="curSelectType" @changeSelectTab="changeSelectTab">
            </cusTabs>
        </div>





        <van-search v-model="searchName" @click="popoverIsShow = true" readonly show-action placeholder="请选择查询对象">
            <template #action>
                <img src="@/assets/calendar.svg" alt="" srcset="" @click.stop="calendarIsShow = true">
            </template>
            <template #left-icon>
                <img class="mt-5" src="@/assets/search.svg" alt="" srcset="">

            </template>
        </van-search>


        <staffList v-model:searchStaffName="searchName" v-model:popoverIsShow="popoverIsShow"
            @confirmStaffCode="confirmStaffCode"></staffList>



        <div class=" container_body ">
            <van-list v-model:loading="loading" :finished="finished"
                :finished-text="tableData.length > 0 ? '没有更多了' : ''" @load="onload">
                <div class="box_content" v-for="item in tableData" :key="item.id">
                    <div class="box_content_header">
                        <div style="display: flex;align-items: center;">
                            <div class="name" v-getName="{ item: { staffCode: item.account } }">{{ item.creatorName }}
                            </div>
                            <span>:</span>
                            <div class="staffCode">{{ item.account }}</div>
                        </div>

                    </div>
                    <div class="row">
                        <span>触达时间：</span>
                        <span>{{ item.contactCreateTime }}</span>
                    </div>
                    <div class="row">
                        <span>触达地点：</span>
                        <span>{{ item.address }}</span>
                    </div>
                    <div class="row">
                        <span>客户号码：</span>
                        <span>{{ item.customerMobile }}</span>
                    </div>
                    <div class="row">
                        <span>客户需求：</span>
                        <span>{{ item.requirementDetail }}</span>
                    </div>
                    <img v-if="item.imgUrl" class="imgList mt-11" :src="item.imgUrl" alt="" srcset=""
                        v-imgPreview="[[item.imgUrl], 0]" v-lazy="item.imgUrl">
                    <div class="tips"
                        :class="item.reviewStatus == 1 ? 'approve' : item.reviewStatus == 2 ? 'reject' : 'pendingAudit'">

                        <i class="icon-shenhe fs12"></i>
                        <span v-if="item.reviewStatus == 1">审核通过时间：{{ item.updateTime }}</span>
                        <span v-if="item.reviewStatus == 2">审核拒绝时间：{{ item.updateTime }}</span>
                        <span v-if="item.reviewStatus == 0">AI预审建议：{{ item.aiAdviceText }}</span>

                    </div>

                    <div style="display: flex;justify-content: space-between;align-items: center; margin-top: 15px;">
                        <van-button color="#4189FF" @click="auditConfirm(0, item)" v-if="item.reviewStatus == 2"
                            style="width: 100%;" round>重新审核</van-button>
                        <van-button color="#4189FF" @click="auditConfirm(0, item)" v-if="item.reviewStatus == 1"
                            style="width: 100%;" round>撤销</van-button>
                        <van-button color="#FF4D41" @click="auditConfirm(2, item)" v-if="item.reviewStatus == 0"
                            style="width: 50%;margin-right: 10px;" round>不通过</van-button>
                        <van-button color="#4189FF" @click="auditConfirm(1, item)" v-if="item.reviewStatus == 0"
                            style="width: 50%;margin-left: 10px;" round>通过</van-button>

                    </div>
                    <div class="auditIcon">
                        <i class="icon-yitongguo fs36" style="color: #6588B3;" v-if="item.reviewStatus == 1"></i>
                        <i class="icon-weitongguo fs36 text-danger " v-if="item.reviewStatus == 2"></i>
                        <i class="icon-daishenhe fs36 text-warning " v-if="item.reviewStatus == 0"></i>

                    </div>
                </div>

            </van-list>
            <van-empty description="暂无数据" v-if="tableData.length == 0" />



        </div>



        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />






    </div>
</template>

<script setup lang="ts">
import cusTabs from "@/components/cusTabs/index.vue";
import { searchHooks } from "@/hooks_modules/changeTimeHooks.ts";
import { showToast, showSuccessToast, showFailToast } from "vant";
import { getAuditListAPI, reviewBusiAPI } from "@/api/opportunity.ts";

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    searchName,
    listData,
    popoverIsShow,
    onConfirm,
    confirmStaffCode,
} = searchHooks({
    source: 'opportunity',
    callback: changeTime
})


interface AuditItem {
    id: string;              // 行id
    creatorName: string;     // 提交人姓名
    account: string;         // 工号
    contactCreateTime: string; // 触达时间
    address: string;         // 触达地点
    customerMobile: string;  // 客户号码
    requirementDetail: string; // 需求
    imgUrl: string;        // 需求图片url
    reviewStatus: 0 | 1 | 2; // 0: 待审核 1: 通过 2: 不通过
    updateTime?: string;     // 审核时间
    aiAdviceText?: string;   // AI建议
}



const tabList = ref([
    { text: '待审核', value: 0 },
    { text: '通过', value: 1 },
    { text: '未通过', value: 2 },


])


const tableData = ref<AuditItem[]>([]);




const loading = ref(false)
const finished = ref(true)
const pageSize = ref(10);
const total = ref(0);
const pageNum = ref(1);

const curSelectType = ref<number>(0)

function changeSelectTab(item: any) {

    curSelectType.value = item.value
    pageNum.value = 1
    tableData.value = []
    loadList()
}


function changeTime() {
    pageNum.value = 1
    tableData.value = []
    loadList()
}
function onload() {
    pageNum.value += 1
    loadList()
}
function loadList() {
    let params = {
        phoneOrAccount: formline.staffCode[0] && formline.staffCode[0] != '全部' ? formline.staffCode[0] : '',
        reviewStatus: curSelectType.value,
        startTime: formline.startTime ? `${formline.startTime} 00:00:00` : undefined,
        endTime: formline.endTime ? `${formline.endTime} 23:59:59` : undefined,
        current: pageNum.value,
        size: pageSize.value,

    }
    loading.value = true
    getAuditListAPI(params).then((res: any) => {
        if (res.code == 200) {
            for (const item of res.data.records) {
                item.imgUrl = item.imgUrl ? 'https://njwxtest.jlonline.com/njszztmb/' + item.imgUrl : ''
            }

            tableData.value = [...tableData.value, ...res.data.records]
            total.value = res.data.total
            if (total.value <= tableData.value.length) {
                finished.value = true
            } else {
                finished.value = false
            }
        }
    }).catch((error) => {
        console.log(error);

    }).finally(() => {

        loading.value = false
    })
}

function auditConfirm(type: number, item: AuditItem) {

    reviewBusiAPI({ id: item.id, reviewStatus: type }).then((res: any) => {
        if (res.code == 200) {
            showSuccessToast('操作成功')
            pageNum.value = 1
            tableData.value = []
            loadList()
        } else {
            showFailToast('操作失败')
        }
    })
}

watch(() => formline.staffCode, () => {
    pageNum.value = 1
    tableData.value = []
    loadList()
})











onMounted(() => {
    loadList()
})

</script>

<style lang="scss" scoped>
.card {
    min-height: 100%;
    background: #fff;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px;
}

:deep(.van-search__action) {
    line-height: 0;
}

:deep(.van-search__content) {
    background-color: #EDF5FF;
    border-radius: 5px;
}



:deep(.van-icon-search) {
    color: #1a76ff;
}

:deep(.van-search__field) {
    height: 40px;
}


:deep(.van-field__value) {
    height: 36px;
    line-height: 36px;
    font-weight: 400;
    font-size: 14px;
    color: #72ABFF;
}

:deep(.van-field__control) {
    font-weight: 400;
    font-size: 14px;
    color: #72ABFF;
}

:deep(.van-field__control::placeholder) {
    color: #72ABFF;
}





.box_content {
    background: #EDF5FF;
    border-radius: 10px 10px 10px 10px;
    padding: 11px;
    position: relative;

    .box_content_header {
        font-weight: 400;
        font-size: 16px;
        line-height: 18px;
        color: #3D3D3D;
        display: flex;
        align-items: center;

        .name {
            font-weight: 500;
            font-size: 16px;
            color: #2675FF;
        }

        .staffCode {
            font-weight: 400;
            font-size: 16px;
            color: #666666;
        }



    }

    .row {
        display: flex;
        margin-top: 8px;
        font-weight: 400;
        font-size: 16px;
        line-height: 18px;
        color: #3D3D3D;

        span:first-child {
            font-weight: 400;
            font-size: 14px;
            color: #3D3D3D;
            line-height: 18px;
            flex: 0 0 auto;
        }

        span:last-child {
            font-weight: 400;
            font-size: 14px;
            color: #666666;
            line-height: 18px;
        }


    }

    .imgList {
        width: 100%;
        height: 128px;
        object-fit: cover;
        border-radius: 10px;
    }

    .auditIcon {
        position: absolute;
        right: -10px;
        top: -10px;
    }
}

.tips {
    border-radius: 15px;
    padding: 3px 10px;
    font-weight: 400;
    font-size: 10px;

    margin-top: 10px;
    display: inline-flex;
    align-items: center;

    span {
        margin-left: 3px;
    }
}

.tips.approve {
    color: #16A34A;
    background: #DCFCE7;
}

.tips.reject {
    color: #DF0000;
    background: #FEE2E2;
}

.tips.pendingAudit {
    color: #2675FF;
    background: #DBEAFE;
}


.box_content:not(:first-child) {
    margin-top: 15px
}
</style>