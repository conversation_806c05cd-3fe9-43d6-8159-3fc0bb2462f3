.card {
    font-family: 'Source <PERSON>, Source <PERSON>';
    min-height: 100%;
    background: #fbfcff;
    overflow-y: scroll;
    overflow-x: hidden;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px;
    padding-bottom: 20px;

    .header {
        display: flex;
        align-items: center;
        font-weight: 500;
    }
}

.selectOption {
    display: flex;
    align-items: center;

    & .selectOption-item {
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
        }
    }

    & .selectOption-item:active {
        background: #d8dee6;
    }

}

.card_body {
    .option {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
        gap: 10px;
        row-gap: 20px;
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        padding: 15px;
    }

    .option-item {
        text-align: center;

        .value {

            font-weight: 500;
            font-size: 18px;
            line-height: 20px;
            margin-bottom: 10px;
        }

        .text {
            font-weight: 500;
            font-size: 14px;
            color: #365791;
        }
    }
}

.commonOption {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;

    .commonOption-item {
        border-radius: 5px;
        background: #EDF5FF;
        height: 95px;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        img{
            width: 41px;
            height: 41px;
        }
        &>div {
            font-weight: 500;
            font-size: 14px;
            color: #3D3D3D;
            height: 25px;
            // line-height: 25px;
            margin-top: 5px;
        }
    }
}