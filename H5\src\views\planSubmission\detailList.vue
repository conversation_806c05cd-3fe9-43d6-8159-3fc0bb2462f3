<template>
    <div class="container_ card">
        <div class="container__header">
            <div>
                <img src="./assets/detail.svg" alt="" srcset="" />
                <span class="ml-6">方案详情</span>
            </div>
        </div>
        <div class="container_body card_body">
            <div class="row">
                <span>方案类型</span>
                <span>{{ solutionDetails.proposalType }}</span>
            </div>
            <div class="row" @click="getCusInfo('Name')">
                <span>客户名称</span>
                <span>{{ solutionDetails.customerName }}</span>
            </div>
            <div class="row" @click="getCusInfo('Tel')">
                <span>联系电话</span>
                <span>{{ solutionDetails.customerTel }}</span>
            </div>
            <div class="row">
                <span>方案描述</span>
                <span>{{ solutionDetails.proposalInfo }}</span>
            </div>
            <div class="image">
                <span class="full-row">图片详情</span>
                <img v-imgPreview="[solutionDetails.images, index]" lazy-load :src="item"
                    v-for="(item, index) in solutionDetails.images" alt="" srcset="">
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
interface SolutionDetails {
    id: string;
    customerName: string;
    proposalType: string
    createTime: string;
    customerTel: string
    proposalInfo: string;
    customerNameJm: string
    customerTelJm: string
    images: string[];
    [key: string]: any
}

import { getProposalListAPI } from "@/api/planSubmission";
import { useRoute } from "vue-router";
import { Decrypt } from "@/utils/crypto/index";
const route = useRoute();


const solutionDetails = reactive<SolutionDetails>({
    id: '',
    customerName: '',
    proposalType: '',
    createTime: '',
    customerTel: '',
    proposalInfo: '',
    customerNameJm: '',
    customerTelJm: '',
    images: []
});

function loadList() {
    let params = {
        proposalId: route.query.id
    }

    getProposalListAPI(params).then((res: any) => {
        if (res.code == 200) {
            const data = res.data.records[0];
            data.images = data.images ? data.images.split(',') : []
            data.images = data.images.map((el: string) => el)
            for (const key in solutionDetails) {
                if (Object.prototype.hasOwnProperty.call(solutionDetails, key)) {
                    solutionDetails[key] = data[key]

                }
            }
        }
    })
}
function getCusInfo(type: string) {
    if (type == 'Name') {
        solutionDetails.customerName = Decrypt(solutionDetails.customerNameJm)
    } else {
        solutionDetails.customerTel = Decrypt(solutionDetails.customerTelJm)
    }


}
onMounted(() => {
    loadList()
})
</script>

<style lang="scss" scoped>
.card {
    min-height: 100%;
    background: #fbfcff;
    // overflow-x: hidden;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px 10px 0px 0px;
}

.container_body {
    padding: 0px;
}

.row {
    display: flex;
    justify-content: space-between;
    padding: 15px 18px;
    border-bottom: 1px solid #EDF5FF;

    &>span:first-child {
        font-weight: 500;
        font-size: 14px;
        color: #3D3D3D;
        width: 100px;
        flex: 0 0 auto;
    }

    &>span:last-child {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
    }
}

.image {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    place-content: center;
    /* 创建三列 */
    gap: 5px;
    padding: 15px 18px;

    .full-row {
        grid-column: span 3;
        font-weight: 500;
        font-size: 14px;
        color: #3D3D3D;
        width: 100px;
    }

    &>img {
        height: 100px;
        width: 100px;
        object-fit: cover;
        border-radius: 5px;
    }
}
</style>