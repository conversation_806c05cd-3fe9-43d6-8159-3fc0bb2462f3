<template>
  <div class="has-logo">
    <logo :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu :default-active="activeMenu" :collapse="isCollapse" background-color="#6563db" text-color="#fff"
        :unique-opened="false" :collapse-transition="false" mode="vertical">
        <sidebar-item v-for="route in routersList" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import Logo from "./Logo.vue";
import SidebarItem from "./SidebarItem.vue";
import { ref, reactive, computed, onMounted } from "vue";
import { useSidebarStore } from "@/stores/layoutSetting/app.ts";
import { useUserInfo } from "@/stores/userInfo";
const SidebarState = useSidebarStore();
const userInfoStore = useUserInfo();
import { useRoute, useRouter } from "vue-router";
const route = useRoute();
const router = useRouter();

const sidebar = computed(() => SidebarState.sidebar);
const isCollapse = computed(() => !sidebar.value.opened);
const userInfo = computed(() => userInfoStore.userInfo); ``
const activeMenu = computed(() => {
  const { meta, path } = route;
  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
});

const routersList = computed(() => {
  const { roles } = userInfo.value;

  // 递归处理路由权限
  const filterRoutes = (routes) => {
    return routes.filter((item) => {
      // 如果菜单项被隐藏，直接过滤掉
      if (item.meta.hidden) {
        return false;
      }

      // 处理子菜单
      if (item.children && item.children.length > 0) {
        item.children = filterRoutes(item.children);
        // 如果过滤后子菜单为空，且当前菜单没有权限要求，则显示当前菜单
        if (item.children.length === 0 && !item.meta?.roles) {
          return false;
        }
      }

      // 如果菜单项没有权限要求，直接显示
      if (!item.meta?.roles || item.meta.roles.length === 0) {
        return true;
      }

      // 如果有权限要求，检查用户是否有权限
      return roles.some((role) => item.meta.roles.includes(role));
    });
  };
  
  return filterRoutes(router.options.routes);
});

onMounted(() => { });
</script>
