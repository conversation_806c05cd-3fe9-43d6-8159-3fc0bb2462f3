<template>
    <div>
        <div class="card-header">
            <!-- <div class="selectOption-item" @click="calendarIsShow = true">
                <span>{{ time ? time : '请选择时间' }}</span>
                <van-icon name="play" class="play" color="#1A76FF" />
            </div> -->

            <div class="selectOption">
                <van-popover :actions="actions" @select="(params: any) => params.handle()" placement="bottom">
                    <template #reference>
                        <div class="selectOption-item">
                            <span>{{ time ? time : '请选择时间' }}</span>
                            <van-icon name="play" class="play" color="#1A76FF" />
                        </div>
                    </template>
                </van-popover>
            </div>


            <cusTabs :tabList="tabList" class="ml-10" v-model:curSelectTab="tabValue"
                @changeSelectTab="changeSelectTab">
            </cusTabs>
        </div>
        <div class=' container_ mt-6'>
            <div class="container__header">
                <div>
                    <img src="@/assets/computed.png" alt="" srcset="">
                    <span class="ml-6">
                        <span>{{ slectTypeText }}过程专项</span>

                        <van-popover placement="bottom-start">
                            <div class="popoverBody">
                                <li>每日18:00前展示T-3数据，18:00后更新T-2数据</li>
                            </div>
                            <template #reference>
                                <van-icon class="ml-3" name="question" color="#1A76FF" />
                            </template>
                        </van-popover>

                    </span>
                </div>
                <div class="fs12" @click="goToDetail">查看详情 <van-icon name="arrow" color="#B8D4FF" /></div>
            </div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th :colspan="tabValue == 1 ? 7 : 9" class="main-title">存量用户{{ slectTypeText }}专项竞赛通报 </th>
                        </tr>
                        <tr>
                            <th :rowspan="tabValue == 1 ? 3 : 2" class="sticky">区域</th>
                            <template v-if="tabValue == 1">
                                <th rowspan="2"> 筑墙加约率</th>
                                <th rowspan="2">防狼用户留存率</th>
                                <th rowspan="2">携转用户留存率</th>
                                <th rowspan="2">欠停赢回率</th>
                                <th colspan="1">宽带拆挽压降</th>
                            </template>
                            <template v-else>
                                <th colspan="3">千兆智慧家</th>
                                <th colspan=" 3">单进融</th>
                                <th colspan=" 2">权益</th>
                                <!-- <th rowspan="2">综合模拟</th>
                                <th rowspan="2">排名</th> -->
                            </template>
                        </tr>

                        <tr>

                            <template v-if="tabValue == 1">
                                <!-- <th>{{ curTimeType == 0 ? '日' : '月' }}指标</th>
                                <th>{{ curTimeType == 0 ? '日' : '月' }}压降率</th> -->
                                <th>当{{ curTimeType == 0 ? '日' : '月' }}离网</th>
                            </template>
                            <template v-else>
                                <!-- 千兆智慧家 -->
                                <th>{{ curTimeType == 0 ? '日' : '月' }}指标</th>
                                <th>{{ curTimeType == 0 ? '日' : '月' }}完成</th>
                                <th>{{ curTimeType == 0 ? '日' : '月' }}完成率</th>

                                <!-- 单进融 -->
                                <th>{{ curTimeType == 0 ? '日' : '月' }}指标</th>
                                <th>{{ curTimeType == 0 ? '日' : '月' }}完成</th>
                                <th>{{ curTimeType == 0 ? '日' : '月' }}完成率</th>

                                <!-- 权益 -->
                                <th>累计完成</th>
                                <th>序时完成率</th>
                            </template>

                        </tr>

                    </thead>
                    <tbody>
                        <template v-if="tabValue == 1">
                            <tr v-for="(row, index) in tableData" :key="index">
                                <td class="sticky">
                                    <div class="areaNameL2">
                                        {{ row.areaNameL2 }}
                                    </div>
                                </td>
                                <td :class="row.colorrate40Zqpd">{{ row.rate40Zqpd ?? 0 }}%</td>
                                <!-- <td>{{ row.rate2 }}%</td>
                                    <td>{{ row.rate3 }}%</td>
                                    <td>{{ row.rate4 }}%</td> -->
                                <td :class="row.colorrate5Flpd">{{ row.rate5Flpd ?? 0 }}%</td>
                                <td :class="row.colorrate5Xzpd">{{ row.rate5Xzpd ?? 0 }}%</td>

                                <!-- 待完成 -->
                                <td :class="row.colorrateQtyh">{{ row.rateQtyh ?? 0 }}%</td>


                                <!-- <td :class="row.colorrateZbCj">{{ row.rateZbCj ?? 0 }}%</td>
                                <td :class="row.colorindicator">{{ row.indicator ?? 0 }}</td> -->
                                <td :class="row.colorkdDismantle">{{ row.kdDismantle ?? 0 }}</td>
                            </tr>

                        </template>
                        <template v-else>
                            <tr v-for="(row, index) in tableData" :key="index">
                                <td class="sticky">
                                    <div class="areaNameL2">
                                        {{ row.areaNameL2 }}
                                    </div>
                                </td>
                                <td :class="row.colordailyIndicator">{{ row.dailyIndicator ?? 0 }}</td>
                                <td :class="row.colordailyCompletion">{{ row.dailyCompletion ?? 0 }}</td>
                                <td :class="row.colorcompletionRate">{{ row.completionRate ?? 0 }}%</td>

                                <td>-</td>
                                <td :class="row.colordjr">{{ row.djr ?? 0 }}</td>
                                <td>-</td>


                                <td :class="row.colorcompletedQyjz">{{ row.completedQyjz ?? 0 }}</td>
                                <td :class="row.colorrateQyjz">{{ row.rateQyjz ?? 0 }}%</td>
                                <!-- <td>{{ row.scoreQyjz }}</td> -->
                                <!-- <td :class="row.colorrank">{{ row.rank }}</td> -->

                            </tr>
                        </template>




                    </tbody>
                </table>
            </div>
            <van-empty description="暂无数据" class="mt-8" style="background-color: #fff;border-radius: 15px;"
                v-if="tableData.length == 0" />



        </div>

        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="single" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />

        <van-popup v-model:show="monthPopup" round position="bottom">

            <van-date-picker v-model="currentDate" title="选择年月" :min-date="monthMinDate" :max-date="monthMaxDate"
                :formatter="formatter" :columns-type="columnsType" @confirm="confirmMonth" />

        </van-popup>

    </div>
</template>
<script setup lang="ts">
import { DatePickerColumnType } from "vant";
import cusTabs from "@/components/cusTabs/index.vue";
import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { getControlLossAPI, getEnhanceValueAPI } from "@/api/home";
import router from "@/router";
import { data } from "autoprefixer";

interface List {
    rate40Zqpd?: number; // 筑墙打坝加约率40
    scoreZqpd?: number;  // 筑墙打坝模拟得分
    rate5Flpd?: number;  // 防狼用户留存率5
    scoreFlpd?: number;  // 防狼模拟得分
    rate5Xzpd?: number;  // 协转留存率5
    scoreXzpd?: number;  // 协转模拟得分
    score?: number;   //综合得分


    colorrate40Zqpd?: string;
    colorRate5Flpd?: string;
    colorRate5Xzpd?: string;
    [key: string]: any
}
const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    onConfirm,
} = searchHooks({
    source: 'sevenThings',
    callback: loadList
})

const curTimeType = ref(0) //默认当日
const monthPopup = ref(false) // 月弹窗


const currentDate = ref([String(new Date().getFullYear()), String(new Date().getMonth() + 1)]);
const columnsType: DatePickerColumnType[] = ['year', 'month'];
const monthMinDate = new Date(2024, 9)
const monthMaxDate = new Date()

const monten = ref('')

const actions = [
    {
        text: '单日',
        value: '0',
        handle: () => {
            curTimeType.value = 0
            calendarIsShow.value = true
        }
    },
    {
        text: '单月',
        value: '1',
        handle: () => {
            curTimeType.value = 1
            monthPopup.value = true
        }
    },

]


function confirmMonth({ selectedValues }: any) {
    monten.value = `${selectedValues[0]}-${selectedValues[1]}`
    monthPopup.value = false
    loadList()
}


const tabList = [
    { text: '控流失', value: 1 },
    { text: '提价值', value: 2 },

]
const tabValue = ref(1)
const slectTypeText = ref('控流失')


const tableData = ref<List[]>([]);


const DayTime = ref('')
const time = computed(() => {
    // if (formline.startTime) return `${formline.startTime}`
    if (curTimeType.value == 0) {
        return `${formline.startTime}`
    } else {
        return `${monten.value}`
    }

})

function goToDetail() {
    router.push({
        path: '/sevenThingsDetail',
        query: {
            startTime: formline.startTime ? `${formline.startTime} 00:00:00` : '',
            endTime: formline.endTime ? `${formline.endTime} 23:59:59` : '',
            type: tabValue.value,
            typeName: slectTypeText.value
        }
    })
}


function changeSelectTab(item: any) {

    slectTypeText.value = item.text
    loadList()
}





function loadList() {
    const API = tabValue.value == 1 ? getControlLossAPI : getEnhanceValueAPI
    let params = {
        type: curTimeType.value,
        day: formline.startTime ? formline.startTime : undefined,
        month: monten.value ? monten.value : undefined,
    }


    API(params).then((res: any) => {
        if (res.code == 200) {
            DayTime.value = res.data.day
            if (tabValue.value == 1) {
                // for (const item of res.data.details || []) {
                //     item.scoped = sum(item.scoreFlpd, item.scoreXzpd, item.scoreZqpd)
                // }
                // res.data.details.sort((a: List, b: List) => b.scoped - a.scoped)
                // assignScopedRank(res.data.details, 'scoped'); // 新增rank 字段
                res.data.details = markData(res.data.details)

            } else {
                // res.data.details.sort((a: List, b: List) => b.scoreQyjz - a.scoreQyjz)
                // assignScopedRank(res.data.details, 'scoreQyjz'); // 新增rank 字段
                res.data.details = markRankClasses(res.data.details)
            }

            tableData.value = res.data.details || []

            if (tableData.value.length == 0) return
            // if (tabValue.value == 2) {
            //     tableData.value.push({
            //         areaNameL2: '合计',
            //         rateQyjz: res.data.countRateQyjz,
            //         completedQyjz: res.data.details.reduce((a: any, b: any) => a + b.completedQyjz, 0),
            //     })


            // } else {

            //     tableData.value.push({
            //         areaNameL2: '合计',
            //         rate40Zqpd: res.data.countRate40Zqpd,
            //         rate5Flpd: res.data.countRate5Flpd,
            //         rate5Xzpd: res.data.countRate5Xzpd,

            //     })
            // }

        }
    })
}


// 新增的函数：根据scoped值分配rank
function assignScopedRank(data: List[], key: string): void {
    const set = [...new Set(data.map(item => item[key]))]
    for (const item of data) {
        item.rank = set.findIndex(el => el === item[key]) + 1
    }



}


function markRankClasses<T extends Record<string, any>>(data: T[]): T[] {
    const list = [
        'completedQyjz',
        'rateQyjz',
        // 'dailyIndicator',
        'dailyCompletion',
        'completionRate',
        'djr',

    ] as const; // 使用 `as const` 使其成为只读元组类型
    type ListKeys = typeof list[number]; // 获取 list 元素的联合类型
    const rates: Record<ListKeys, number[]> = {} as Record<ListKeys, number[]>;
    for (const key of list) {
        rates[key] = data.map(item => item[key] ?? 0).sort((a, b) => b - a)
        markColors(data, getTopAndBottom(rates[key]), key);
    }

    return data; // 返回标记后的原始数组

}
function markData(data: List[]): List[] {
    const list = [
        'rate40Zqpd',
        'rate5Flpd',
        'rate5Xzpd',
        'rateQtyh',
        'rateZbCj',
        'indicator',
        'kdDismantle',

    ] as const; // 使用 `as const` 使其成为只读元组类型
    type ListKeys = typeof list[number]; // 获取 list 元素的联合类型
    const rates: Record<ListKeys, number[]> = {} as Record<ListKeys, number[]>;

    for (const key of list) {
        rates[key] = data.map(item => item[key] ?? 0).sort((a, b) => b - a)
        markColors(data, getTopAndBottom(rates[key]), key);
    }
    return data; // 返回标记后的原始数组
}
function markColors(data: List[], result: { top: number[]; bottom: number[] }, key: string) {
    if (key == 'rate5Flpd') {
        const set = [...new Set([...result.top, ...result.bottom])]
        if (set.length == 1 && set[0] == 100) {
            return
        }
    }
    // if(key == 'rank') debugger

    const bottomSet = new Set(result.bottom); // 使用 Set 存储 bottom 值
    const topSet = new Set(result.top); // 使用 Set 存储 top 值

    for (const item of data) {
        const rateValue = item[key] ?? 0;
        if (bottomSet.has(rateValue)) {
            item[`color${key}`] = key == 'rank' ? 'green' : 'yellow'; // 标记为黄色
        } else if (topSet.has(rateValue)) {
            item[`color${key}`] = key == 'rank' ? 'yellow' : 'green'; // 标记为绿色
        }
    }
}

function getTopAndBottom<T>(arr: T[]): { top: T[]; bottom: T[] } {
    const sortedArray = [...arr].sort((a: any, b: any) => b - a); // 降序排序
    const top = sortedArray.slice(0, 3); // 获取前三个
    const bottom = sortedArray.slice(-3); // 获取后三个
    return { top, bottom };
}



const formatter = (type: string, option: any) => {
    if (type === 'year') {
        option.text += '年';
    }
    if (type === 'month') {
        option.text += '月';
    }
    return option;
};


onMounted(() => {
    loadList()
})
</script>

<style lang="scss" scoped>
.card-header {
    height: 46px;
    padding: 2px 15px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

    .selectOption {
        display: flex;
        align-items: center;
        flex: 1 0 auto;

        .selectOption-item {
            flex: 1 0 auto;
            background: #EDF5FF;
            border-radius: 5px 5px 5px 5px;
            font-weight: 400;
            font-size: 14px;
            color: #1A76FF;
            padding: 8px;
            display: flex;
            align-items: center;
            // justify-content: space-between;
            position: relative;

            .play {
                transform: rotate(90deg);
                margin-left: 3px;
                position: absolute;
                right: 10px;
            }
        }

        .selectOption-item:active {
            background: #d8dee6;
        }
    }
}


.table-container {
    width: 100%;
    overflow-x: auto;
    margin-top: 10px;

    padding-bottom: 10px;

    table {
        width: 100%;
        border-collapse: collapse;
        text-align: center;
        font-size: 13px;

        th,
        td {
            padding: 8px;
            border: 1px solid #ddd;
            white-space: nowrap; // 禁止换行
        }

        .areaNameL2 {
            width: 100px;
            white-space: normal;
        }

        .sticky {
            /* 固定表格内容的门店列 */
            position: sticky;
            left: 0;
            background-color: #fff;
            /* 设置背景颜色以覆盖其他列 */
            z-index: 1;
            /* 确保门店列在最上层 */
        }

        .green {
            background-color: #92d050;
        }

        .yellow {
            background-color: #ffc000;
        }



        .main-title {
            font-size: 13px;
            font-weight: bold;
            padding: 12px;
            background-color: #2e70ba;
            color: #fff;
            text-align: center;
        }

        thead tr:nth-child(2) th {
            background-color: #2e70ba;
            color: #fff;
            font-weight: bold;
            padding: 10px;
            text-align: center;
        }

        thead tr:nth-child(3) th {
            background-color: #2e70ba;
            color: #fff;
            font-weight: bold;
            padding: 8px;
        }

        tbody tr:nth-child(even) {
            background-color: #f2f2f2;
        }


        // tbody tr:last-child {
        //     background-color: #2299dd !important;
        // }

        tbody tr:hover {
            background-color: #e6f7ff;
        }

        tbody td.highlight {
            background-color: #ffe58f;
            font-weight: bold;
        }
    }


}

:deep(.van-popover__wrapper) {
    flex: 1 0 auto;
}
</style>