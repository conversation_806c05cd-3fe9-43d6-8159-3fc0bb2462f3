<template>
    <div class="card container_">

        <div class="container__header">
            <div>
                <span class="ml-6" style="font-weight: 500;">我管理的商机单</span>
                <div class="checkbox-wrapper-10  ml-4" style="display: flex;align-items: center;"
                    v-if="userInfoStore.userInfo.orgDept == UserType.Enterprise || userInfoStore.userInfo.orgDept == UserType.Other">
                    <input :checked="isGov" type="checkbox" id="cb5" @change="isGov = !isGov" class="tgl tgl-flip">
                    <label for="cb5" data-tg-on="政企" data-tg-off="公众" class="tgl-btn"></label>

                    <i class="icon-shouzhixuanzhong-copy left mb-4" style="color: #1A76FF;"></i>
                </div>
            </div>
            <div class="selectOption">
                <van-popover v-model:show="showPopover" :actions="actions" @select="onSelect" placement="bottom-end">
                    <template #reference>
                        <div class="selectOption-item">
                            <span>{{ curSelectTimeTime }}</span>
                            <van-icon name="play" class="play" color="#1A76FF" />
                        </div>
                    </template>
                </van-popover>
            </div>
        </div>
        <div class="container_body card_body">
            <div class="option">
                <div class="option-item" v-for="item, index in optionList" :key="index">
                    <div class="value" :style="{ color: item.color }">{{ leadTableData[item.props] }}{{
                        item.props == 'dispatchrate' ? '%' : '' }}</div>
                    <div class="text">{{ item.text }}</div>
                </div>
            </div>

        </div>


        <div class="container__header mt-10">
            <div>
                <span class="ml-6" style="font-weight: 500;">我的商机</span>
            </div>
        </div>
        <div class="container_body card_body">
            <div class="option">
                <div class="option-item" v-for="item, index in optionList" :key="index">
                    <div class="value" :style="{ color: item.color }">{{ myTableData[item.props] }}{{
                        item.props == 'dispatchrate' ? '%' : '' }}</div>
                    <div class="text">{{ item.text }}</div>
                </div>
            </div>

        </div>

        <div class="container__header mt-10">
            <div>
                <span class="ml-6" style="font-weight: 500;">商机录入</span>
            </div>
        </div>
        <div class="container_body card_body">
            <div class="commonOption">

                <div class=" commonOption-item" @click="goTo">
                    <img src="../assets/entering.svg" alt="" srcset="">
                    <div>
                        普通基础商机
                        <br>
                        <span class="fs10">（商机助手）</span>
                    </div>
                </div>
                <router-link to="/opportunityEntry" v-if="isGov">
                    <div class=" commonOption-item">
                        <img src="../assets/opportunityEntry.svg" alt="" srcset="">
                        <div>
                            政企基础商机
                        </div>
                    </div>
                </router-link>
                <div class=" commonOption-item" @click="goToHuXiu" v-if="isGov">
                    <img src="../assets/huxiu.svg" alt="" srcset="">
                    <div>
                        新兴产数商机
                        <br>
                        <span class="fs10">（虎嗅平台）</span>
                    </div>
                </div>

            </div>
        </div>



        <div class="container__header mt-10">
            <div>
                <span class="ml-6" style="font-weight: 500;">商机处理</span>
            </div>
        </div>
        <div class="container_body card_body">
            <div class="commonOption">
                <a href="http://wx.go189.cn/jdb-mp/index.html">
                    <div class=" commonOption-item">
                        <img src="../assets/dispose.svg" alt="" srcset="">
                        <div>
                            接单宝
                        </div>
                    </div>
                </a>
                <router-link to="/businessAudit" v-if="isShowBusinessAudit">
                    <div class=" commonOption-item">
                        <img src="../assets/audit.svg" alt="" srcset="">
                        <div>
                            商机审核
                            <br> <span class="fs10">（翼销售）</span>
                        </div>
                    </div>
                </router-link>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { getBusinessCodeAPI } from "@/api/common";
import { ref, reactive, onMounted, onUnmounted, computed, toRefs, ComputedRef } from "vue";
import { opportunityHooks, UserType } from "../hooks";
import { formatDate } from "@/utils/loadTime";

const optionList = ref([
    {
        text: '录入量',
        props: 'recordCount',
        color: '#004CBE',
    },

    {
        text: '接单量',
        color: '#4892FF',
        props: 'acceptCount',

    },
    {
        text: '待接单',
        color: '#FF9760',
        props: 'unacceptCount',

    },
    {
        text: '处理中',
        color: '#34C759',
        props: 'handlingCount',

    },
    {
        text: '已结单',
        color: '#48CBFF',
        props: 'finishCount',

    },
    {
        text: '转化率',
        color: '#004CBE',
        props: 'dispatchrate',

    },
])





const {
    myTableData,
    leadTableData,
    curSelectTimeTime,
    showPopover,
    actions,
    roleCode,
    isShowBusinessAudit,
    isGov,
    userInfoStore,
    onSelect,
    goToHuXiu,
    loadList,
} = opportunityHooks()


const { VITE_SALE_URL_DISPOSE, VITE_SALE_URL_ENTERING } = import.meta.env
async function goTo() {

    if (roleCode.value == 'shop_leader') {
        getBusinessCodeAPI().then((res: any) => {
            if (res.code == 200) {
                location.href = VITE_SALE_URL_ENTERING + res.data
            }
        })
    } else {
        location.href = 'http://wx.go189.cn/qywx/searchSend/homePage?corpId=wxe8c2710f51e9ee56'
    }
}







</script>

<style lang="scss" scoped>
@import url('../index.scss');
</style>
<!-- <style lang="scss" src="../index.scss" scoped></style> -->
<style lang="scss" src="@/views/template/Contribution/switch.scss" scoped></style>
<style lang="scss" scoped>
.left {
    position: relative;
    /* 或者 absolute，确保能看到位置变化 */
    transform: rotate(-90deg);
    animation: identifier_ 1.5s infinite;
}
</style>