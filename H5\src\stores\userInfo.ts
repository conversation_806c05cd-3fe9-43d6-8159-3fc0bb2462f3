import { defineStore } from "pinia";
import { getUserInfoAPI, getAllStaffListAPI, getChatBiLinkAPI } from "@/api/common";
const { VITE_MODE } = import.meta.env
import { login, menuList } from "@/api/common.ts"
import router from "@/router"; // 引入自己封装的路由  
import { getToken, removeToken } from "@/utils/auth.ts";
import { encrypt, decrypt } from "@/utils/auth.ts";
/**
 * 用户信息
 * @methods setUserInfos 设置用户信息
 */
export const useUserInfo = defineStore("userInfo", {
  state: (): UserInfosState => ({
    userInfo: {},
    token: "",
    // AES密钥
    AESKEY: "",
    // 龙虎榜是否显示
    isShowLh: false,
    menuList: [],
    staffList: [],
    isShowChatBi: false,
    chatBiUrl: '',
  }),
  getters: {/**
 * 获取当前的token值
 * 
 * 此方法返回当前实例的token属性值如果token属性未设置，则返回空字符串
 * 
 * @returns {string} 当前的token值，如果未设置则为""
 */
    /**
     * 获取当前的token值
     * 
     * 此方法返回当前实例的token属性值如果token属性未设置，则返回空字符串
     * 
     * @returns {string} 当前的token值，如果未设置则为""
     */
    getToken(): string {
      return this.token || "";
    },
    getAESkey(): string {
      return this.AESKEY || "";
    },
    getMenuList(): MenuItem[] {
      return this.menuList || [];
    },
    getUserInfo(): Organization {
      return this.userInfo
    },
    getStaffList(): StaffVO[] {
      let list = this.staffList.map(item => ({
        ...item, // 复制原对象的所有属性
        staffName: `${item.staffName} - ${item.staffCode}` // 更新 staffName 属性
      }))
      return list
    }
  },
  actions: {
    logOut() {
      this.$reset()
      sessionStorage.clear()
      removeToken()
    },
    async codeInit() {
      const route = router.currentRoute.value;
      const { backToken, randomParam: routeRandomParam, backAESkey } = route.query;

      // 获取URL参数
      const urlParams = this.getUrlParams();
      const { code: urlCode, randomParam: urlRandomParam } = urlParams;

      // 合并路由参数和URL参数
      const code = String(urlCode || '');
      const randomParam = String(routeRandomParam || urlRandomParam || '');

      try {
        if (backToken) {
          await this.handleBackTokenLogin(backToken as string, backAESkey as string);
        } else if (code || randomParam) {
          await this.handleCodeLogin(code, randomParam);
          // 检查用户角色并重定向
          const roles = ['dept_leader', 'area', 'dy_dept_director'];
          if (roles.includes(this.userInfo.primaryRoleCode as string)) {
            router.push('/userData');
          }

        }
      } catch (error) {
        console.error('登录初始化失败:', error);
        router.push('/IDNotFound');
      }
    },

    // 获取URL参数
    getUrlParams() {
      const url = window.location.href;
      const urlObj = new URL(url);
      const searchParams = new URLSearchParams(urlObj.search);

      return {
        code: searchParams.get('code') || '',
        randomParam: searchParams.get('randomParam') || ''
      };
    },

    // 处理backToken登录
    async handleBackTokenLogin(backToken: string, backAESkey: string) {
      this.setToken(backToken);
      this.setAESkey(backAESkey);
      await this.initializeUserSession();
    },

    // 处理code登录
    async handleCodeLogin(code: string, randomParam: string) {
      const isLoginSuccessful = await this.loginInit(code, randomParam);
      // 登录成功后，增加查看次数
      if (isLoginSuccessful) {
        let viewCount = null
        const currentTime = new Date().getTime()
        if (localStorage.getItem('viewCount_')) {
          viewCount = JSON.parse(localStorage.getItem('viewCount_') || '{}')
        } else {
          // 第一次调用，初始化时间和计数
          viewCount = { count: 0, timestamp: currentTime }
        }

        // 判断是否跨自然日（比较日期字符串）
        const currentDate = new Date().toDateString() // 获取当前日期字符串，如 "Mon Dec 25 2023"
        const lastDate = viewCount.timestamp ? new Date(viewCount.timestamp).toDateString() : ''

        if (currentDate !== lastDate) {
          // 跨自然日，重新赋值时间并重置计数为1
          viewCount.timestamp = currentTime
          viewCount.count = 1
        } else {
          // 同一自然日，只增加计数
          viewCount.count++
        }
        if (viewCount.count < 4) this.isShowLh = true
        localStorage.setItem('viewCount_', JSON.stringify(viewCount))
      }
      if (!isLoginSuccessful) return;

      await this.initializeUserSession();
    },

    // 初始化用户会话
    async initializeUserSession() {
      await this.setUserInfo();
      await this.setMenuList();



      // 获取聊天链接
      await this.getChatBiLink();
      // 加载员工列表
      if (this.getMenuList.length > 1) {
        await this.loadAllStaffList();
      }
    },

    async getChatBiLink() {
      const chatBiRes: any = await getChatBiLinkAPI()
      if (chatBiRes.code == 200) {
        this.isShowChatBi = true
        this.chatBiUrl = chatBiRes.data
      }
    },

    async loginInit(code: string, randomParam: string) {
      const router = useRouter()


      try {
        let params = { code: code, randomParam: randomParam }
        const res: any = await login(params)
        if (res.code == 200) {
          this.setToken(res.data.tokenType + " " + res.data.accessToken || "")
          this.AESKEY = res.data.key
          return true
        } else if (res.code == 'A402') {
          router.push({
            path: '/IDNotFound', query: {
              phoneNumber: res.msg
            }
          })
        } else {
          router.push({
            path: '/IDNotFound'
          })
        }

      } catch (error) {
        router.push('/IDNotFound')
        return false
      }
    },

    RecordToken() {
      const backToken = this.token
      this.logOut()

      // const url = window.location.href.replace(/(\/home)\?.*$/, '$1');
      const url = window.location.href.replace(/(\?.*)$/, '');
      window.history.replaceState('', '', `${url}?backToken=${backToken}`);
    },


    setToken(val: string) {
      this.token = val
    },
    setAESkey(val: string) {
      this.AESKEY = val
    },
    removeToken() {
      this.token = ""
      removeToken()
    },

    async setMenuList() {
      try {
        const { data } = await menuList()
        this.menuList = data
        // router.push({ name: this.menuList[0].menuCode })

      } catch (error) {
        console.log(error, "获取菜单失败")
      }
    },
    async setUserInfo() {
      await getUserInfoAPI().then(res => {
        this.userInfo = res.data
      })
    },
    loadAllStaffList() {
      getAllStaffListAPI().then(res => {
        this.staffList = res.data
      })
    }
  },

  persist: [
    {
      key: VITE_MODE + "_useUserInfo",
      storage: sessionStorage,
      pick: [
        'userInfo',
        'token',
        'chatBiUrl',
        'AESKEY',
        'backToken',
        'menuList',
        'staffList',
        'isShowChatBi'
      ]
    },

  ],


});
