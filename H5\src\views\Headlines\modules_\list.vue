<template>
    <div class=' container_ mt-6'>
        <div class="container__header">
            <div>
                <img src="@/assets/resultOptimization.svg" alt="" srcset="">
                <span class="ml-6">{{ title }}</span>
            </div>
        </div>


        <div class="container_body ">

            <div class="card-list" v-if="isShowOverviewCard">
                <div class="card overvirwCard">

                    <div class="card-body" v-for="item in tabDate" :key="item.code">
                        <div class="status-block">
                            <img src="@/assets/yjjf.png" alt="" srcset="" v-if="item.code == 0">
                            <img src="@/assets/lhjf.png" alt="" srcset="" v-else-if="item.code == 1">
                            <img src="@/assets/yxjf.png" alt="" srcset="" v-else-if="item.code == 2">
                            <div class="info" :class="`info${item.code}`">
                                <p class="status-row">
                                    <span> {{ item.name }}</span>
                                    <span :class="`color${item.code}`">{{ item.count }}</span>
                                </p>
                                <p class="status-row">
                                    <span>占比</span>
                                    <span :class="`color${item.code}`">{{ item.rate }}%</span>
                                </p>
                            </div>
                        </div>
                    </div>

                </div>
            </div>


            <div class="card-list">
                <div class="card" v-for="item in tabList" :key="item.roleCode">
                    <div class="card-header">
                        <span class="role-name">{{ item.roleName || item.name }}</span>


                        <div class="go-button" @click="GO(item)" v-if="isShowGO">
                            <span>GO</span>
                            <van-icon name="arrow" color="#fff" />
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="status-block">
                            <img src="@/assets/yjjf.png" alt="" srcset="">
                            <div class="info">
                                <p>人数: {{ item.yj }}</p>
                                <p>占比: {{ item.yjRate }}%</p>
                            </div>
                        </div>
                        <div class="status-block">
                            <img src="@/assets/lhjf.png" alt="" srcset="">
                            <div class="info">
                                <p>人数: {{ item.lh }}</p>
                                <p>占比: {{ item.lhRate }}%</p>
                            </div>
                        </div>
                        <div class="status-block">
                            <img src="@/assets/yxjf.png" alt="" srcset="">
                            <div class="info">
                                <p>人数: {{ item.yx }}</p>
                                <p>占比: {{ item.yxRate }}%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <van-empty description="暂无数据" v-if="tabList.length == 0" />
        </div>




    </div>
</template>

<script setup lang='ts'>
import { getHeadBarEchartsAPI } from "@/api/home";
import { PropType } from "vue";
import { useRoute } from "vue-router";

interface List {
    code: number;
    count: number;
    name: string;
    rate: number;
}
interface TabList {
    lh: number;
    lhRate: number;
    roleCode: string;
    roleName: string;
    totalCount: number;
    yj: number;
    yjRate: number;
    yx: number;
    yxRate: number;
    [key: string]: any
}

const route = useRoute()
const emits = defineEmits(['GO'])
const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    isShowGO: {
        type: Boolean,
        default: true
    },
    isShowOverviewCard: {
        type: Boolean,
        default: false
    },
    tabList: {
        type: Array as PropType<TabList[]>,
        default: () => []
    }


})


const { title, isShowOverviewCard, tabList } = toRefs(props)

const tabDate = ref<List[]>()




watch(() => isShowOverviewCard.value, (value) => {
    if (value) {
        loadHeadlinesEcharts()
    }
}, { immediate: true })


//获取预警 良好 优秀 占比和数值
function loadHeadlinesEcharts() {
    let params = {
        startTime: route.query.startTime ? route.query.startTime : undefined,
        endTime: route.query.endTime ? route.query.endTime : undefined,
    }
    getHeadBarEchartsAPI(params).then((res: any) => {
        tabDate.value = res.data || []
    })
}

function GO(item: any) {
    emits('GO', item)
}

</script>

<style lang="scss" scoped>
.card-list {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .card {
        background-color: #EDF5FF;
        border-radius: 5px;
        padding: 10px;
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;

            .role-name {
                font-size: 14px;
                font-weight: bold;
                color: #3D3D3D;
            }

            .go-button {
                background-color: #1A76FF;
                color: #fff;
                padding: 3px 5px;
                border-radius: 20px;
                cursor: pointer;
                font-weight: 400;
                font-size: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .card-body {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .status-block {
                width: 100%;
                display: flex;
                align-items: center;

                img {
                    width: 24px;
                }

                .info {
                    flex: 1 0 auto;

                    p {
                        margin: 0;
                        font-size: 10px;
                        color: #3D3D3D;
                        line-height: 13px;
                        margin-left: 5px;
                    }
                }

            }


        }
    }

    .overvirwCard {
        background-color: transparent;
        box-shadow: none;
        padding: 0px;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        margin: 10px 0px;
        margin-bottom: 20px;

        gap: 8px;

        .card-body {
            border-radius: 5px 5px 5px 5px;
            padding: 4px 5px;



            .status-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 8px;

                .color0 {
                    font-weight: 500;
                    font-size: 9px;
                    color: #C92528;
                }

                .color1 {
                    font-weight: 500;
                    font-size: 9px;
                    color: #F2B30C;
                }

                .color2 {
                    font-weight: 500;
                    font-size: 9px;
                    color: #199964;
                }
            }
        }

        .card-body:nth-child(1) {
            background: #FFF3F2;
        }

        .card-body:nth-child(2) {
            background: #FFF7EA;

        }

        .card-body:nth-child(3) {
            background: #E9FDF2;

        }


    }
}
</style>