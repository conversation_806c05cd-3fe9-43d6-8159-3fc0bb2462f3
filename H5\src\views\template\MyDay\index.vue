<template>
  <div class="box mt-16 container_">
    <div class="container__header">
      <div>
        <img src="./assets/images/time.png" alt="" srcset="" />
        <span class="ml-6">我的一天</span>
      </div>
      <router-link :to="{
        path: '/loginRateAreaL2Staff',
      }">
        <div class="logiRate fs14" v-if="isShowTodayTotalCount">
          <span class="fs13">今日登录率</span>：
          <span>{{ todayTotalCount }}%</span>
        </div>
      </router-link>
    </div>
    <div class="container_body">

      <van-swipe indicator-color="#66C6F2" @change="onChange">
        <van-swipe-item v-for="(list, index) in mydayConfig" :key="index">
          <transition name="fade-transform">
            <van-row :gutter="[10, 10]" v-show="index === current">
              <van-col style="box-sizing: border-box;" span="8" v-for="(item, index) in list" :key="index"
                @click="navTo(item)">
                <div class="card-menu">
                  <div class="card-image">
                    <van-image class="card-icon" contain :src="item.icon_">
                    </van-image>
                    <div class="card-content-title">
                      {{ item.menuName }}
                    </div>
                  </div>
                </div>
              </van-col>
            </van-row>
          </transition>
        </van-swipe-item>
      </van-swipe>



    </div>
  </div>
</template>

<script setup lang="ts">
import collapseTransition from "@/components/collapse-transition/index.vue";
import { useUserInfo } from "@/stores/userInfo";
import { getBusinessCodeAPI } from "@/api/common";
import { ref, reactive, onMounted, onUnmounted, computed, toRefs } from "vue";
import { useRoute, useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import { getTodayTotalCountAPI } from "@/api/home";



enum UserType {
  Other = 0,  // 其他
  Public = 1, // 公众
  Enterprise = 2 // 政企
}
const router = useRouter();
const route = useRoute();
const userInfoStore = useUserInfo();
const props = defineProps({});

// 获取当前用户是否是政企用户
const isGov = computed(() => {
  let isGov_ = false;
  switch (userInfoStore.userInfo.orgDept) {
    case UserType.Public:
      isGov_ = false;
      break;
    case UserType.Enterprise:
      isGov_ = true;
      break;
    case UserType.Other:
      isGov_ = true
      break;
    default:
      isGov_ = false;
      break;
  }
  return isGov_;
});







const mydayConfig = computed(() => {
  let list = userInfoStore.getMenuList.filter((item: MenuItem) => item.menuCode === 'home')[0]?.children || []
  let MyDaylist = list.filter((item: MenuItem) => item.menuCode === 'MyDay')[0]?.children || []
  MyDaylist.forEach((item, index) => {
    item.icon_ = new URL(`./assets/images/${item.icon}.png`, import.meta.url).href
    item.isLink = item.type == 3 ? true : false
  });

  if (isGov.value) {
    const govOrder = ['signInG', 'signInP', 'customerVisitLead', 'customerVisit', 'bizG', 'bizP', 'planSubmission', 'dispatch', 'currentCapacity'];
    // govOrder中的项优先，顺序固定
    const govItems = govOrder
      .map((code) => MyDaylist.find((item) => item.menuCode === code))
      .filter(Boolean) as MenuItem[];
    // 剩余未在govOrder中的项，按sort升序排列
    const restItems = MyDaylist
      .filter((item) => !govOrder.includes(item.menuCode))
      .sort((a, b) => (a.sort ?? 0) - (b.sort ?? 0));
    // 如果 menuCode 是 bizG，将 menuName 改为 商机填报
    MyDaylist.forEach(item => {
      if (item.menuCode === 'bizG' || item.menuCode === 'bizP') {
        item.menuName = '商机填报';
      }
    });


    MyDaylist = [...govItems, ...restItems];
  }

console.log(mydayConfig);












  const groupedList = [];
  const LENGTH_ = 6

  for (let i = 0; i < MyDaylist.length; i += LENGTH_) {
    groupedList.push(MyDaylist.slice(i, i + LENGTH_));
  }
  return groupedList;


})

const current = ref(0)
function onChange(index: any) {
  current.value = index;
}





defineExpose({});


const { VITE_SALE_URL_clockIn } = import.meta.env

const navTo = (item: any) => {
  if (item.isLink) {
    getBusinessCodeAPI().then((res: any) => {
      if (res.code == 200) {
        window.location.href = `${VITE_SALE_URL_clockIn}${res.data}`
      }

    }).catch((error => {
      console.log(error);

    }))


  } else {
    router.push(item.path);

  }
};



const todayTotalCount = ref(0)
const isShowTodayTotalCount = ref(true)
//获取今日登录数据
async function loadTodayTotalCount() {
  const res: any = await getTodayTotalCountAPI()
  if (res.code == 200) {
    todayTotalCount.value = res.data.loginRate ?? 0
  } else {
    isShowTodayTotalCount.value = false
  }

}



// 生命周期钩子
onMounted(() => {
  loadTodayTotalCount()
});

onUnmounted(() => {

});
</script>

<style lang="scss" scoped>
.box {
  background: #fbfcff;
  box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #ffffff;
  border-radius: 3%;
}

.logiRate {
  color: #1A76FF;
}

.box::after {
  content: "";
  display: block;
  clear: both;
}

// 背景色
.card-menu {
  background: rgba(103, 159, 255, 0.15);
  aspect-ratio: 1/1;
  border-radius: 5%;
}

// 图片与文字区域布局
.card-image {
  // padding: 10%;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  flex-flow: column nowrap;
  justify-content: center;
  align-items: center;
}

.card-icon {
  // width: 65%;
  width: 51px;
  box-sizing: border-box;
  padding: 5%;
}

.card-content-title {
  font-size: 12px;
  padding: 5%;
  box-sizing: border-box;
  font-weight: 550;
}


::v-deep(.van-swipe__indicator) {
  background-color: #a8a8a8;
}
</style>
