<template>

    <div class=' container_'>
        <div class="container__header">
            <div>
                <img src="@/assets/computed.png" alt="" srcset="">
                <span class="ml-6">宣传检查结果查询</span>
            </div>

        </div>
        <div class="table-container pb-10">

            <table>
                <thead>

                    <tr>
                        <th class="sticky">区域</th>
                        <th>督办<br>网格数</th>
                        <th>其中有<br>反馈</th>
                        <th>有反馈<br>占比</th>
                        <th>其中无<br>反馈</th>
                    </tr>

                </thead>
                <tbody>


                    <tr v-for="(row, index) in tableData" :key="row.areaId">
                        <td class="sticky">
                            <div class="store detail" @click="gotoDetail(row)">{{ row.areaName }}</div>
                        </td>
                        <td>{{ row.gridTotal }}</td>
                        <td>{{ row.reactionNum }}</td>
                        <td>{{ row.reactionRate ?? 0 }}%</td>
                        <td>{{ row.unReactionNum }}</td>
                    </tr>


                </tbody>
            </table>
        </div>
        <van-empty description="暂无数据" class="mt-8" style="background-color: #fff;border-radius: 15px;"
            v-if="tableData.length == 0" />
    </div>
</template>

<script setup lang="ts">
import { mapSort } from "@/utils/mapSort";
import { getAreaDetailAPI, getAreaDetailListAPI } from "@/api/promoShots";
import { useRoute, useRouter } from "vue-router";
const router = useRouter()

interface GridManagementDetails {
    areaId: string
    areaName: string
    gridTotal: number; // 暂办网格总数
    reactionNum: number; // 有反馈数
    unReactionNum: number; // 无反馈数
    reactionRate: number; // 反馈进度，百分比
}




const tableData = ref<GridManagementDetails[]>([])

function loadList() {
    getAreaDetailListAPI({}).then((res: any) => {
        if (res.code == 200) {
            tableData.value = mapSort(res.data, 'areaName') || []
        }
        // 计算合计
        const totalGridTotal = tableData.value.reduce((sum, item) => sum + item.gridTotal, 0);
        const totalReactionNum = tableData.value.reduce((sum, item) => sum + item.reactionNum, 0);
        const totalUnReactionNum = tableData.value.reduce((sum, item) => sum + item.unReactionNum, 0);
        const totalReactionRate = totalGridTotal
            ? Number(((totalReactionNum / totalGridTotal) * 100).toFixed(2))
            : 0;

        // 新增合计项
        tableData.value.push({
            areaId: "total",
            areaName: "合计",
            gridTotal: totalGridTotal,
            reactionNum: totalReactionNum,
            unReactionNum: totalUnReactionNum,
            reactionRate: totalReactionRate,
        });

    })

}


function gotoDetail(row: GridManagementDetails) {
    if (row.areaName == '合计') return
    router.push({
        path: '/communityBattleArea',
        query: {
            areaId: row.areaId,
            areaName: row.areaName
        }
    })
}

onMounted(() => {
    loadList()
})
</script>

<style lang="scss" scoped>
.container_ {
    min-height: 100%;
    border-radius: 10px;
}

.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

    .selectOption-item {
        flex: 1 0 auto;
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;
        // justify-content: space-between;
        position: relative;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
            position: absolute;
            right: 10px;
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }
}


.table-container {
    width: 100%;
    overflow-x: auto;
    // padding: 8px;

    table {
        width: 100%;
        border-collapse: collapse;
        text-align: center;
        font-size: 13px;

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            white-space: nowrap; // 禁止换行
            // white-space: normal;
            /* 允许换行 */
        }

        .sticky {
            /* 固定表格内容的门店列 */
            position: sticky;
            left: 0;
            background-color: #fff;
            /* 设置背景颜色以覆盖其他列 */
            z-index: 1;
            /* 确保门店列在最上层 */
        }


        .store {
            min-width: 45px;
            max-width: 120px;
            white-space: normal;
        }

        .detail {
            color: #1A76FF;
            text-decoration: underline;
            text-decoration-thickness: 1px;
            /* 下划线的厚度 */
            text-underline-offset: 2px;
            /* 下划线与文字的距离 */
        }

        .main-title {
            font-size: 13px;
            font-weight: bold;
            padding: 12px;
            background-color: #2e70ba;
            color: #fff;
            text-align: center;
        }

        thead tr:nth-child(1) th {
            background-color: #2e70ba;
            color: #fff;
            font-weight: bold;
            padding: 10px;
            text-align: center;
        }

        thead tr:nth-child(2) th {
            background-color: #2e70ba;
            color: #fff;
            font-weight: bold;
            padding: 8px;
        }

        tbody tr:nth-child(even) {
            background-color: #f2f2f2;
        }



        tbody tr:hover {
            background-color: #e6f7ff;

            .sticky {
                background-color: #e6f7ff !important;

            }
        }

        tbody tr:last-child {
            background-color: #e4e6ef;

            .sticky {
                background-color: #e4e6ef !important;
            }

            .detail {
                color: #3D3D3D;
                text-decoration: none;
            }
        }

        tbody td.highlight {
            background-color: #ffe58f;
            font-weight: bold;

        }

    }
}
</style>