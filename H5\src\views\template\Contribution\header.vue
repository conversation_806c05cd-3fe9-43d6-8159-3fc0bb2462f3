<template>
    <div class="container__header">
        <div>
            <img src="./assets/Contribution.svg" alt="" srcset="">
            <span class="ml-6" style="display: flex;align-items: center;">
                <span>我的贡献</span>
                <div class="checkbox-wrapper-10  ml-4" style="display: flex;align-items: center;"
                    v-if="orgDept == 2 || orgDept == 0">
                    <input :checked="isGov" type="checkbox" id="cb5" @change="changeIsGov" class="tgl tgl-flip">
                    <label for="cb5" data-tg-on="政企" data-tg-off="公众" class="tgl-btn"></label>

                    <i class="icon-shouzhixuanzhong-copy left mb-4" style="color: #1A76FF;"></i>
                </div>

            </span>
        </div>

        <cusTabs :tabList="tabList" v-model:curSelectTab="curSelectMenuCode" @changeSelectTab="changeOrderType">
        </cusTabs>
    </div>
</template>

<script setup lang="ts">
import { nextTick, ref, computed } from "vue";

type TabItem = {
    text: string,
    value: string | number
}

const emits = defineEmits<{
    (e: 'changeSelectType', value: number | undefined): void;
}>();

const props = defineProps({
    children: {
        type: Array as PropType<MenuItem[]>,
        deafault: (): MenuItem[] => []
    },
    isHaveAreaTab: {
        type: Boolean,
        deafault: false
    },
    orgDept: {
        type: Number,
        deafault: 1
    }
})




const { children, isHaveAreaTab } = toRefs(props)
const tabList = computed(() => {
    if (isHaveAreaTab.value) {
        return children?.value?.map(item => {
            return {
                text: item.menuName,
                value: item.menuCode
            }
        })
    } else {
        return children?.value?.filter(item => item.menuCode !== 'pq')?.map(item => {
            return {
                text: item.menuName,
                value: item.menuCode
            }
        })
    }
})




const isGov = defineModel('isGov', {
    type: Boolean,
    default: false
})

const curSelectType = defineModel('curSelectType', {
    type: Number,
    default: 0
})
const curSelectMenuCode = ref('')


function changeOrderType(item: TabItem) {
    switch (item.text) {

        case '片区':
            curSelectType.value = 0
            emits('changeSelectType', 0)

            break;
        case '门店':
            curSelectType.value = 1
            emits('changeSelectType', 1)
            break;
        case '人员':
            curSelectType.value = 2
            emits('changeSelectType', 2)
            break;
    }

}

function changeIsGov(value: any) {
    isGov.value = !isGov.value


}

watch(() => curSelectType.value, (newValue) => {
    switch (newValue) {
        case 0:
            curSelectMenuCode.value = 'pq'
            break;
        case 1:
            curSelectMenuCode.value = 'channel'
            break;
        case 2:
            curSelectMenuCode.value = 'staff'
            break;

    }

}, { immediate: true })


</script>

<style lang="scss" scoped>
.card-header-right {
    position: relative;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    touch-action: manipulation;
    gap: 10px;
    /* 提升移动端触摸体验 */

    &>.item {
        width: 45px;
        height: 46px;
        text-align: center;
        line-height: 46px;
        font-weight: 400;
        font-size: 16px;
        color: #919191;
        cursor: pointer;
        position: relative;
        transition: color 0.3s;
        user-select: none;
    }

    &>.item.activeItem {
        font-weight: 500;
        font-size: 16px;
        color: #1A76FF;
    }
}

.left {
    position: relative;
    /* 或者 absolute，确保能看到位置变化 */
    transform: rotate(-90deg);
    animation: identifier_ 1.5s infinite;
}

:deep(.card-header-right) {
    gap: 5px;
}
</style>
<style lang="scss" scoped src="./switch.scss"></style>