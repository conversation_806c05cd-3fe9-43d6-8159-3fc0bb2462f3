<template>
    <div>
        <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="date" label="Date" />
            <el-table-column prop="name" label="Name" />
            <el-table-column prop="address" label="Address" />
        </el-table>
    </div>

</template>

<script lang="ts" setup>
const tableData = ref([
    {
        date: '2016-05-03',
        name: '<PERSON>',
        address: 'No. 189, Grove St, Los Angeles',
    },
    {
        date: '2016-05-02',
        name: '<PERSON>',
        address: 'No. 189, Grove St, Los Angeles',
    },
    {
        date: '2016-05-04',
        name: '<PERSON>',
        address: 'No. 189, Grove St, Los Angeles',
    },
    {
        date: '2016-05-01',
        name: '<PERSON>',
        address: 'No. 189, Grove St, Los Angeles',
    },
])
</script>