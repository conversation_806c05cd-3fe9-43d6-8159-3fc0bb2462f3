# Logs                # 日志文件
logs                 # 忽略 logs 目录
*.log               # 忽略所有 .log 文件
npm-debug.log*      # 忽略 npm 调试日志
yarn-debug.log*     # 忽略 yarn 调试日志
yarn-error.log*     # 忽略 yarn 错误日志

**/H5/node_modules/    # 忽略所有目录下的 node_modules 目录
**/PC/node_modules/    # 忽略所有目录下的 node_modules 目录
**/node_modules/
node_modules/       # 忽略根目录下的 node_modules 目录


**/H5/dist/    # 忽略所有目录下的 dist 目录
**/PC/dist/    # 忽略所有目录下的 dist 目录
**/dist/
dist               # 忽略 dist 目录（构建输出目录）
dist-ssr           # 忽略 dist-ssr 目录（SSR构建输出目录）
*.local            # 忽略所有 .local 文件

# Editor directories and files  # 编辑器相关文件和目录
.vscode/*           # 忽略 .vscode 目录下的所有文件
!.vscode/extensions.json  # 但保留 .vscode/extensions.json 文件
.idea              # 忽略 .idea 目录（IntelliJ IDEA 配置）
.DS_Store          # 忽略 .DS_Store 文件（macOS 系统文件）
*.suo              # 忽略 Visual Studio 用户选项文件
*.ntvs*            # 忽略 Node.js Tools for Visual Studio 文件
*.njsproj          # 忽略 Node.js 项目文件
*.sln              # 忽略 Visual Studio 解决方案文件
*.sw?              # 忽略 Vim 临时文件