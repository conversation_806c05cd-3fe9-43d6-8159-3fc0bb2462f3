<template>
    <div class="card">
        <div class="card-header">
            <div style="display: flex; align-items: center;">
                <van-icon name="arrow-left" size="4.5vw" class="mr-5" @click="router.go(-1)" />
                <img src="@/assets/orderType.svg" alt="" srcset="">
                <div>{{ route.query.kangJiaName }}</div>
                <div class="ml-5 fs14" style="color: #1a76ff;">{{ route.query.kangJiaType }}类</div>

                <van-popover placement="bottom">
                    <div class="popoverBody">
                        <li>接触量：外呼时长大于0的数量</li>
                        <li>接触率：接触量/派单量</li>
                        <li>外呼量：外呼时长大于30秒的数量</li>
                        <li>外呼率：外呼量/派单量</li>
                        <li>以上数据均来自智慧营销，最终口径以业务为准</li>

                    </div>
                    <template #reference>
                        <van-icon class="ml-3" name="question" color="#1A76FF" />
                    </template>
                </van-popover>
            </div>
            <div class="searchBox" @click="popoverIsShow = true">
                <img class="mr-3" src="@/assets/search.svg" alt="" srcset="">
                <span class="value">{{ searchName ? searchName.split('-')[0] : '请选择查询对象' }} </span>
            </div>

        </div>
        <div class="mt-8 card-body" v-for="item in tableData" :key="item.kangJiaCode">

            <div class="card-body-header">
                <span class="itemName" v-getName="{ item: item }">{{ item.staffName }}</span>
                <span class="ml-2 itemRoleName">{{ item.staffRoleName }}</span>
            </div>
            <myCharts class="myEcharts" :options="getEchartsOption(item)"></myCharts>


        </div>
        <van-empty description="暂无数据" class="mt-8" style="background-color: #fff;border-radius: 15px;"
            v-if="tableData.length == 0" />


        <van-popup v-model:show="popoverIsShow" round position="bottom" teleport="body">
            <van-picker title="人员列表" :columns="staffList" :columns-field-names="{
                text: 'staffName',
                value: 'staffCode'
            }" @confirm="confirmStaffCode" @cancel="popoverIsShow = false">
                <template #title>
                    <van-search class="mt-5 mb-5" v-model="searchStaffName" placeholder="请输入搜索关键词" />
                </template>
            </van-picker>
        </van-popup>
    </div>
</template>

<script setup lang="ts">
import myCharts from "@/components/charts/myCharts.vue";
import { getSendOrderEveryoneDetailListAPI } from "@/api/home";
import { useRoute, useRouter } from "vue-router";
import { ref, reactive } from "vue";
import { searchHooks } from "@/hooks_modules/changeTimeHooks.ts";
const route = useRoute()
const router = useRouter()

interface Data {
    kangJiaName: string
    kangJiaType?: 0 | 1 | 2;  // 框架分类，0-S, 1-A, 2-B, 可选
    kangJiaCode?: string;  // 框架代码，可选
    outCallNum?: number;  // 外呼量，可选
    outCallRate?: number;  //外呼率
    successResultNum?: number // 成功数

    dispatchNum?: number;  // 派单量，可选
    touchNum?: number;  // 接触量，可选
    touchRate?: number;  // 接触率，可选

    // transformNum?: number;  // 转化量，可选
    // transformRate?: number;  // 转化率，可选
    [key: string]: any
}

const {
    formline,
    listData,
    popoverIsShow,
    searchName,
    confirmStaffCode,
} = searchHooks()

const searchStaffName = ref('') // 搜索的人名字

const staffList = computed(() => {
    const input = searchStaffName.value.toLowerCase();

    return listData.value.filter(item => {
        const staffName = item.staffName ? item.staffName.toLowerCase() : '';
        const staffRoleName = item.staffRoleName ? item.staffRoleName.toLowerCase() : '';
        const staffCode = item.staffCode ? item.staffCode.toLowerCase() : '';

        // 检查是否匹配 staffName、staffRoleName 或 staffCode
        return (
            staffName.includes(input) ||
            staffRoleName.includes(input) ||
            staffCode.includes(input)
        );
    });
})


const tableData = ref<Data[]>([]);





function getEchartsOption(row: Data) {

    const data = [
        { value: 0, name: '接触量', props: 'touchNum' },
        { value: 0, name: '接触率', props: 'touchRate' },
        // { value: 0, name: '转化量', props: 'transformNum' },
        // { value: 0, name: '转化率', props: 'transformRate' },
        { value: 0, name: '外呼量', props: 'outCallNum' },
        { value: 0, name: '外呼率', props: 'outCallRate' },
        { value: 0, name: '成功数', props: 'successResultNum' },
    ]
    // 设置比例因子，将接触率和转化率的数值缩放
    let scaleFactor = 4; // 将接触率和转化率数据放大，使其适应 x 轴
    let mycolor = ['#1A76FF', '#27C272']
    let option = {
        grid: {
            left: "15%",
            top: "0%",
            right: '15%',
            bottom: "0%",
        },
        tooltip: {
            trigger: "item",
            formatter: function (params: any) {
                // 反向缩放，展示实际值
                let originalValue = params.name.includes('率') ? params.value / scaleFactor : params.value;
                return `${params.name}: ${originalValue}${params.name.includes('率') ? '%' : ''}`;
            }
        },
        xAxis:
        {
            show: false,
            type: "value",
            max: 400, // 设置接触量和转化量的最大值
        },


        yAxis: [
            {
                type: "category",
                data: data.map(item => item.name),
                axisLine: { show: false },
                axisTick: { show: false },
                axisLabel: {
                    color: " #3D3D3D ",
                    fontSize: 13,
                    show: true,
                    fontWeight: 500,
                    margin: 10,

                },
                inverse: true,
            },


            {
                type: 'category',
                inverse: true,
                axisTick: 'none',
                axisLine: 'none',
                show: true,
                axisLabel: {

                    align: 'left', // Align the labels to the left
                    formatter: function (params: any, index: any) {
                        return `{color${index % 2}|${params}${index % 2 == 0 ? '' : '%'}}`;
                    },
                    rich: {
                        color0: { color: mycolor[0], fontSize: 14 }, // For first label
                        color1: { color: mycolor[1], fontSize: 14 }, // For second label

                    },
                },
                data: data.map(item => ({ value: row[item.props], name: item.name, })
                )
            }
        ],
        series: [
            {
                name: "",
                type: "bar",
                data: data.map(item => {
                    return item.name.includes('率') ? row[item.props] * scaleFactor : row[item.props]
                }),
                showBackground: true,
                backgroundStyle: { color: "#EDF5FF", borderRadius: [8, 8, 8, 8] },
                barCategoryGap: 10, //间距

                barWidth: 10, //宽度
                itemStyle: {
                    normal: {
                        borderRadius: [20, 20, 20, 20],
                        color: function (d: any) {
                            return mycolor[d.dataIndex % 2];
                        },
                    },
                },
                yAxisIndex: 0, //定位层级，类似z-index
            },
        ],
    };
    return option
}





function loadDetaild() {
    let parmas = {
        kangJiaType: route.query.kangJiaType,
        frameCode: route.query.frameCode,
        startTime: route.query.startTime ? `${route.query.startTime} 00:00:00` : '',
        endTime: route.query.endTime ? `${route.query.endTime} 23:59:59` : '',
        staffCode: formline.staffCode[0] == '全部' ? '' : formline.staffCode[0],
    }
    getSendOrderEveryoneDetailListAPI(parmas).then(res => {
        for (const item of res.data || []) {
            item.outCallRate = item.dispatchNum == 0 ? 0 : Math.round((item.outCallNum / item.dispatchNum) * 100)
            item.touchRate = item.dispatchNum == 0 ? 0 : Math.round((item.touchNum / item.dispatchNum) * 100)
            item.transformRate = item.dispatchNum == 0 ? 0 : Math.round((item.transformNum / item.dispatchNum) * 100)
        }
        tableData.value = res.data || []
    })
}

watch(() => formline.staffCode, (value) => {
    loadDetaild()

})

onMounted(() => {
    loadDetaild()
})
</script>

<style lang="scss" scoped>
.card {
    font-family: 'Source Han Sans, Source Han Sans';
    min-height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
    font-family: Source Han Sans, Source Han Sans;

    .searchBox {
        margin-left: auto;
        background-color: #EDF5FF;
        border-radius: 5px;
        height: 40px;
        display: flex;
        align-items: center;
        padding: 3px 10px;

        .value {
            font-weight: 400;
            font-size: 14px;
            color: #72ABFF;
        }
    }

    .itemName {
        font-weight: 700;
        font-size: 20px;
    }

    .itemRoleName {
        font-size: 12px;
        padding: 3px 6px;
        border-radius: 5px;
        background-color: #D8D8D8;
        text-align: center;
        margin-left: 5px;
    }

    .card-header {
        // height: 46px;
        padding: 5px 5px;
        background: #FBFCFF;
        border-radius: 10px 10px 10px 10px;
        box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        row-gap: 10px;

        &>div:first-child {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 16px;
            color: #3D3D3D;
            line-height: 18px;

            img {
                margin-right: 8px;
            }

        }



    }

    .card-body {
        background: #FBFCFF;
        box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
        background: #FBFCFF;
        border-radius: 10px 10px 10px 10px;

        .card-body-header {
            padding: 10px 20px;
            padding-bottom: 0px;
            display: flex;
            align-items: flex-end;
            border-bottom: 1px solid #EEF5FF;
        }

        .myEcharts {

            width: 100%;
            height: 156px;
            padding: 10px 20px;
        }
    }


}
</style>