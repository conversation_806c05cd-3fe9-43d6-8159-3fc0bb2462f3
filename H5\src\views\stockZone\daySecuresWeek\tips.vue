<template>

    <div class="card container_">
        <div class="container__header">
            <div style="display: flex; align-items: center;">
                <img src="@/assets/sendOrdersInfo.svg" alt="" srcset="">
                <span class="ml-6" style="font-weight: 500;">以日保周SOP</span>
            </div>

        </div>
        <div class="container_body ">
            <div class="container_body_header">
                <div class="title">
                    <div>白名单通报</div>
                    <div>（人员执行）</div>
                </div>
            </div>

            <div class="table-container" @touchstart.stop>
                <table>
                    <thead>


                        <tr>
                            <th class="sticky">白名单人员</th>
                            <th class="main-title">派单名称</th>
                            <th class="main-title">接单量</th>
                            <th class="main-title">外呼数</th>
                            <th class="main-title">接单执行率</th>
                            <th class="main-title">呼通数</th>
                            <th class="main-title">呼通率</th>
                            <th class="main-title">派单数</th>
                            <th class="main-title">转化数</th>
                            <th class="main-title">派单转化率</th>
                            <th class="main-title">当日外呼数</th>
                            <th class="main-title">当日呼通数</th>
                            <th class="main-title">当日呼通率</th>
                            <th class="main-title">当日转化数</th>
                            <th class="main-title">当日派单转化率</th>

                        </tr>

                    </thead>
                    <tbody>


                        <tr v-for="(row, index) in tableData" :key="index">
                            <td class="sticky" v-if="shouldShowName(row.staffCode, index)"
                                :rowspan="getRowSpan(row.staffCode)" v-getName="{ item: row }"> {{ row.name }}</td>

                            <td>
                                <div style="width: 200px;max-width: 200px; white-space: normal;">
                                    {{ row.activityName }}
                                </div>
                            </td>
                            <td>{{ row.orderCount }}</td>
                            <td>{{ row.outboundCallCount }}</td>
                            <td :class="row.orderExecutionRate < 100 ? 'yellow' : ''">{{ row.orderExecutionRate }}%
                            </td>


                            <td>{{ row.connectedCount }}</td>
                            <td>{{ row.connectedRate }}%</td>
                            <td>{{ row.dispatchCount }}</td>
                            <td>{{ row.conversionCount }}</td>
                            <td>{{ row.dispatchConversionRate }}%</td>
                            <td>{{ row.currentDayOutboundCallCount }}</td>
                            <td>{{ row.currentDayConnectedCount }}</td>
                            <td>{{ row.currentDayConnectedRate }}%</td>
                            <td>{{ row.currentDayConversionCount }}</td>
                            <td>{{ row.currentDayDispatchConvRate }}%</td>
                        </tr>


                    </tbody>



                </table>

            </div>
            <van-empty description="暂无数据" class="mt-8" style="background-color: #FBFCFF;border-radius: 15px;"
                v-if="tableData.length == 0" />
        </div>

    </div>





</template>

<script setup lang="ts">
import { getRemindListByStaffAPI, getRemindStatusAPI } from "@/api/remind";
import { useRoute } from "vue-router";
import { useUserInfo } from "@/stores/userInfo";
import { getDaySecuresWeek_WLListAPI, } from "@/api/daySecuresWeek";

const route = useRoute()
const userInfoStore = useUserInfo()
const staffCode = computed(() => {
    return userInfoStore.userInfo.staffCode
})









const tableData = ref<any>([])

function loadList() {

    getDaySecuresWeek_WLListAPI({ staffCode: staffCode.value }).then((res: any) => {
        if (res.code == 200) {
            tableData.value = res.data
        }
    })
}



// 合并单元格
function shouldShowName(staffCode: string, index: number): boolean {
    return index === tableData.value.findIndex((row: any) => row.staffCode === staffCode);
}
function getRowSpan(staffCode: string): number {
    return tableData.value.filter((row: any) => row.staffCode === staffCode).length;
}

function deleteRemind() {
    getRemindStatusAPI({ remindId: route.query.id, status: 2 }).then((res: any) => {
        if (res.code == 200) {

        }
    })
}

let timer: any = null

onMounted(() => {
    loadList()
    timer = setTimeout(() => {
        deleteRemind()

        timer = null
    }, 3000)
})
onUnmounted(() => {
    if (timer) {
        clearTimeout(timer)
        timer = null
    }

})
</script>

<style lang="scss" scoped>
.card {
    font-family: 'Source Han Sans, Source Han Sans';
    min-height: 100%;
    background: #fbfcff;
    border-radius: 10px 10px 0px 0px;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);

    .container__header {
        display: block;
        padding-bottom: 5px;
    }

    .container_body_header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            display: flex;
            align-items: flex-end;

            &>div:first-child {
                font-weight: 500;
                font-size: 16px;
                color: #000000;
            }

            &>div:last-child {
                font-weight: 400;
                font-size: 13px;
                color: #999999;
                margin-left: 5px;
            }
        }

    }

    .selectOption-item {
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;
        position: relative;
        padding-right: 25px;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
            position: absolute;
            right: 10px;
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }

    .table-container {
        width: 100%;
        overflow-x: auto;
        margin-top: 10px;

        padding-bottom: 10px;

        table {
            width: 100%;
            border-collapse: collapse;
            text-align: center;
            font-size: 13px;

            th,
            td {
                padding: 8px;
                border: 1px solid #ddd;

                white-space: nowrap; // 禁止换行
            }

            .areaNameL2 {
                width: 100px;
                white-space: normal;
            }

            .sticky {
                /* 固定表格内容的门店列 */
                position: sticky;
                left: 0px;
                background-color: #fff;
                /* 设置背景颜色以覆盖其他列 */
                z-index: 1;
                border: 1px solid #ddd;

                /* 确保门店列在最上层 */
            }

            .green {
                background-color: #92d050;
            }

            .yellow {
                background-color: #ffc000;
            }



            .main-title {
                font-size: 13px;
                font-weight: bold;
                padding: 12px;
                background-color: #2e70ba;
                color: #fff;
                text-align: center;
            }

            thead tr:nth-child(n) th {
                background-color: #2e70ba;
                color: #fff;
                font-weight: bold;
                padding: 10px;
                text-align: center;
            }

            thead tr:nth-child(3) th {
                background-color: #2e70ba;
                color: #fff;
                font-weight: bold;
                padding: 8px;
            }



            tbody tr:nth-child(even) {
                background-color: #f2f2f2;
            }




            tbody tr:hover {
                background-color: #e6f7ff;
            }

            tbody td.highlight {
                background-color: #ffe58f;
                font-weight: bold;
            }
        }


    }


}
</style>