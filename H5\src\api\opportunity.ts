import request from "../utils/request";
import configAxios from './config'

//商机单数据看板查看
export function getBusinessCountAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/business/count`,
        method: "post",
        data,
    });
}
//商机审核查询
export function getAuditListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/sales/queryOfflineSalesBusiPvt`,
        method: "post",
        data,
    });
}


//商机审核
export function reviewBusiAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/sales/reviewBusi`,
        method: "post",
        data,
    });
}


// 基础商机录入API
export function basicOpportunityEntryAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/ZqBusiOpportunity/save`,
        method: "POST",
        data,
    });
}
//根据客户编码查询客户名称
export function queryCustNameAPI(params: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/ZqBusiOpportunity/queryCustName`,
        method: "GET",
        params,
    });
}
//基础商机录入分页查询
export function getZqBusiOpportunityListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/ZqBusiOpportunity/page`,
        method: "POST",
        data,
    });
}
