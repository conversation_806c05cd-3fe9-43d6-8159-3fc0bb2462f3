<template>
    <div>
        <div class="card-header">
            <div class="selectOption-item" @click="calendarIsShow = true">
                <span>{{ time ? time : '请选择时间' }}</span>
                <van-icon name="play" class="play" color="#1A76FF" />
            </div>
            <van-search v-model="searchName" :show-action="false" placeholder="网格名称/ID">
                <template #left-icon>
                    <img class="mt-5" src="@/assets/search.svg" alt="" srcset="">

                </template>
            </van-search>
        </div>
        <div class=' container_ mt-6'>
            <div class="container__header">
                <div>
                    <img src="@/assets/computed.png" alt="" srcset="">
                    <span class="ml-6">宣传检查</span>
                </div>

                <van-popover :actions="gridActivityList" @select="changeGridActivity" placement="bottom"
                    class="custom-van-popover">
                    <template #reference>
                        <div class="selectOption-item ">
                            <span>{{ curGridActivity.text ? curGridActivity.text : '请选择活动类型' }}</span>
                            <van-icon name="play" class="play" color="#1A76FF" />
                        </div>
                    </template>
                </van-popover>

            </div>
            <div class="table-container pb-10">

                <table>
                    <thead>

                        <tr>
                            <th class="sticky">网格名称</th>
                            <th>点检状态</th>
                            <th>姓名</th>
                            <th>图片</th>

                        </tr>

                    </thead>
                    <tbody>


                        <tr v-for="(row, index) in tableData_" :key="row.gridId">
                            <td class="sticky" v-if="shouldShowName(row.gridId, index)"
                                :rowspan="getRowSpan(row.gridId)">
                                <div class="store ">{{ row.gridName }}</div>
                            </td>
                            <td class="text-primary" v-if="row.status == 1">已点检</td>
                            <td class="text-warning" v-else>未点检</td>

                            <td v-getName="{ item: row }">{{ row.staffName }}</td>

                            <td class="detail">
                                <div @click="gotoDetail(row)" v-if="row.status == 1">{{ row.images.length }}</div>

                            </td>


                        </tr>


                    </tbody>
                </table>
            </div>
            <van-empty description="暂无数据" class="mt-8" style="background-color: #fff;border-radius: 15px;"
                v-if="tableData_.length == 0" />
        </div>

        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />
    </div>
</template>

<script setup lang="ts">
import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { getPromoShotsListAPI, getGridDetailAPI } from "@/api/promoShots";
import { getDictInfoAPI } from "@/api/common";
import { useRoute, useRouter } from "vue-router";
import { formatDate } from "@/utils/loadTime";
const route = useRoute()
const router = useRouter()
interface List {
    id: string; //主键ID
    areaIdL2: string; // 二级区域 ID
    areaNameL2: string; // 二级区域名称
    createTime: string; // 创建时间，格式为 "YYYY-MM-DD HH:mm:ss.S"
    description: string; // 详细描述，如具体地址或位置
    gridId: string; // 网格 ID
    gridName: string; // 网格名称
    gridTypeName: string | null; // 网格类型名称，可能为 null
    images: string; // 相关图片的 URL
    status: number; //点检状态
    gridProperty: string
    staffName: string
    staffCode: string
}

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    onConfirm,
} = searchHooks({
    source: 'inspectionLogMangement',
    callback: loadStorePackageList
})

const searchName = ref('')


const tableData = ref<List[]>([]);

const tableData_ = computed<List[]>(() => {
    const input = searchName.value.toLowerCase();
    let data = tableData.value.filter((item: List) => {
        const gridName = item.gridName ? item.gridName.toLowerCase() : '';
        const gridId = item.gridId ? item.gridId.toLowerCase() : '';
        return (
            gridName.includes(input) || gridId.includes(input)
        );
    });

    return data
})


const time = computed(() => {
    if (formline.startTime && formline.endTime) return `${formline.startTime} - ${formline.endTime}`
    else return ``
})

function loadStorePackageList() {
    let params = {
        startTime: formline.startTime ? `${formline.startTime} 00:00:00` : '',
        endTime: formline.endTime ? `${formline.endTime} 23:59:59` : '',
        activityName: curGridActivity.text
    }
    getGridDetailAPI(params).then((res: any) => {
        if (res.code == 200) {

            const groupedData = res.data.reduce((acc: any, item: any) => {

                item.images = item.images ? item.images.split(',') : []
                // 如果 accumulator 中没有该 gridId，初始化一个新的对象
                if (!acc[item.gridId]) {
                    acc[item.gridId] = [item]
                } else {
                    // 如果该 gridId 已经存在，则合并图片并更新照片数量

                    acc[item.gridId].push(item)
                }

                return acc;
            }, {});
            // 将合并后的数据转换回数组
            tableData.value = Object.values(groupedData).flat() as List[]

        }
    })
}


watch(tableData_, async () => {
    await nextTick(); // 等待 DOM 更新完成
    tableData_.value.forEach((row, index) => {
        shouldShowName(row.gridId, index);
        getRowSpan(row.gridId);
    });
});


// 合并单元格
function shouldShowName(gridId: string, index: number): boolean {
    return index === tableData.value.findIndex((row: any) => row.gridId === gridId);
}

function getRowSpan(gridId: string): number {
    return tableData.value.filter((row: any) => row.gridId === gridId).length;
}


function gotoDetail(row: List) {
    router.push({
        path: '/inspectionLog',
        query: {
            id: row.id,
            startTime: formline.startTime,
            endTime: formline.endTime
        }
    })
}












const gridActivityList = ref<any[]>([])
const curGridActivity = reactive({
    text: '',
    value: ''
})

async function initDictInfo() {

    try {
        const res: any = await getDictInfoAPI('grid_activity')
        if (res.code == 200) {
            gridActivityList.value = res.data.map((item: any) => ({ text: item.dictName, value: item.dictCode }))
        }
        // 找到最大的 dictCode，并赋值给 curGridActivity
        if (gridActivityList.value.length > 0) {
            // 假设 dictCode 都是数字字符串
            const maxItem = gridActivityList.value.reduce((prev, curr) => {
                return Number(curr.value) > Number(prev.value) ? curr : prev
            }, gridActivityList.value[0])
            curGridActivity.text = maxItem.text
            curGridActivity.value = maxItem.value
        }

    } catch (error) {


    }

}

function changeGridActivity(params: any) {
    if (curGridActivity.text == params.text) return
    curGridActivity.text = params.text
    curGridActivity.value = params.value
    loadStorePackageList()
}











onMounted(async () => {
    await initDictInfo()
    loadStorePackageList()
})
</script>

<style lang="scss" scoped>
.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }


}

.selectOption-item {
    flex: 1 0 auto;
    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
    font-weight: 400;
    font-size: 14px;
    color: #1A76FF;
    padding: 8px;
    display: flex;
    align-items: center;
    padding-right: 25px;
    position: relative;

    .play {
        transform: rotate(90deg);
        margin-left: 3px;
        position: absolute;
        right: 8px;
    }
}

.selectOption-item:active {
    background: #d8dee6;
}

.table-container {
    width: 100%;
    overflow-x: auto;
    // padding: 8px;

    table {
        width: 100%;
        border-collapse: collapse;
        text-align: center;
        font-size: 13px;

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            white-space: nowrap; // 禁止换行
            // white-space: normal;
            /* 允许换行 */
        }

        .sticky {
            /* 固定表格内容的门店列 */
            position: sticky;
            left: 0;
            background-color: #fff;
            /* 设置背景颜色以覆盖其他列 */
            z-index: 1;
            /* 确保门店列在最上层 */
        }


        .store {
            width: 120px;
            max-width: 120px;
            white-space: normal;
        }

        .detail {
            color: #1A76FF;
            text-decoration: underline;
            text-decoration-thickness: 1px;
            /* 下划线的厚度 */
            text-underline-offset: 2px;
            /* 下划线与文字的距离 */
        }

        .main-title {
            font-size: 13px;
            font-weight: bold;
            padding: 12px;
            background-color: #2e70ba;
            color: #fff;
            text-align: center;
        }

        thead tr:nth-child(1) th {
            background-color: #2e70ba;
            color: #fff;
            font-weight: bold;
            padding: 10px;
            text-align: center;
        }

        thead tr:nth-child(2) th {
            background-color: #2e70ba;
            color: #fff;
            font-weight: bold;
            padding: 8px;
        }

        tbody tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        tbody tr:hover {
            background-color: #e6f7ff;

            .sticky {
                background-color: #e6f7ff !important;

            }
        }

        tbody td.highlight {
            background-color: #ffe58f;
            font-weight: bold;

        }

    }
}

:deep(.van-search) {
    background-color: transparent;
    padding: 0px;
    flex: 0 0 140px;
    margin-left: 5px;
}

:deep(.van-search__action) {
    line-height: 0;
}

:deep(.van-search__content) {
    background-color: #EDF5FF;
    border-radius: 5px;


}



:deep(.van-icon-search) {
    color: #1a76ff;

}


:deep(.van-field__control) {
    font-weight: 400;
    font-size: 14px;
    color: #72ABFF;
}

:deep(.van-field__control::placeholder) {
    color: #72ABFF;
}
</style>