<template>
    <div class="container__header">
        <div>
            <img src="../assets/Contribution.svg" alt="" srcset="">
            <span class="ml-6">今日贡献</span>
        </div>
        <div class="card-header-right" v-if="curSelectType != 3">

            <van-button type="primary" size="small" @click="emits('gotoHome')">回到首页</van-button>
        </div>
    </div>
</template>

<script setup lang="ts">
import { nextTick, ref, computed } from "vue";


const emits = defineEmits<{
    (e: 'gotoHome',): void;
}>();


const props = defineProps({
    curSelectType: {
        type: Number,
        default: 0
    },
})


const { curSelectType } = toRefs(props)












</script>

<style lang="scss" scoped>
.card-header-right {
    position: relative;
    // height: 46px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    touch-action: manipulation;
    gap: 10px;
    /* 提升移动端触摸体验 */

    &>.item {
        width: 45px;
        height: 46px;
        text-align: center;
        line-height: 46px;
        font-weight: 400;
        font-size: 16px;
        color: #919191;
        cursor: pointer;
        position: relative;
        transition: color 0.3s;
        user-select: none;
    }

    &>.item.activeItem {
        font-weight: 500;
        font-size: 16px;
        color: #1A76FF;
    }
}


</style>