

import request from "../utils/request";
import configAxios from './config'

//获取今日头条列表
export function getHeadlinesListAPI() {
    return request({
        url: `${configAxios.taskHubServer}/h5/today_title/development`,
        method: "get",
    });
}
//政企 获取今日头条列表
export function getHeadlinesListGovAPI() {
    return request({
        url: `${configAxios.taskHubServer}/h5/gov/develop/count`,
        method: "get",
    });
}
// 区域领导 + 公司领导 查看今日头条饼图
export function getHeadBarEchartsAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/manager/today_title/index`,
        method: "POST",
        data
    });
}
// 区域领导 + 公司领导 查看今日头条 线图
export function getHeadlinesLineEchartsAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/manager/today_title/line_chart`,
        method: "POST",
        data
    });
}


// 区域领导 + 公司领导 根据条线查看今日头条 list 列表
export function getHeadlinesbyRoleCountListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/manager/today_title/role_count`,
        method: "POST",
        data
    });
}
// 区域领导 + 公司领导 根据区域或者分局查看今日头条 list 列表
export function getHeadlinesbyAreaCountListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/manager/today_title/area_count`,
        method: "POST",
        data
    });
}

// 指标分配设置
export function setTargetStaffAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/day/goal/save`,
        method: "POST",
        data
    });
}
// 政企指标分配设置
export function setTargetStaffGovAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/gov/develop/goal/save`,
        method: "POST",
        data
    });
}
//  指标分配人员列表
export function getsetTargetStaffListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/day/goal/list`,
        method: "POST",
        data,
    });
}

// 政企指标分配人员列表
export function getsetTargetStaffListGovAPI() {
    return request({
        url: `${configAxios.taskHubServer}/h5/gov/develop/goal/list`,
        method: "GET",
    });
}



//我的贡献
export function getContributionDataLeaderAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/contribute/list`,
        method: "post",
        data,
    });
}
export function getContributionDataAPI() {
    return request({
        url: `${configAxios.taskHubServer}/h5/contribute/mine`,
        method: "get",
    });
}



//派单看板数据查看
export function getSendOrderListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/order/count`,
        method: "POST",
        data,
    });
}
//派单看板数据查看
export function getSendOrderTableListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/order/table`,
        method: "POST",
        data,
    });
}
export function getSendOrderTableStaffAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/order/table/staff`,
        method: "POST",
        data,
    });
}



//派单看板数据详情查看

export function getSendOrderDetailAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/order/detail`,
        method: "POST",
        data,
    });
}
//派单看板数据详情查看

export function getSendOrderEveryoneListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/order/count/everyone`,
        method: "POST",
        data,
    });
}
//派单看板类别详情查看

export function getSendOrderEveryoneDetailListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/order/detail/everyone`,
        method: "POST",
        data,
    });
}

//派单查看战区详情数据

export function getSendOrderZoneContactAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/order/warContact`,
        method: "POST",
        data,
    });
}

//派单查看战区详情下钻数据

export function getSendOrderZoneContactByRoleAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/order/warContact/detail`,
        method: "POST",
        data,
    });
}
//部门与全区的派单看板数据查看


export function getSendOrderDepaAndAreaAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/order/count/manager`,
        method: "POST",
        data,
    });
}




//结果保障列表
export function getResultOptimizationListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/resultOptimization/avgHuoliRank`,
        method: "POST",
        data,
    });
}
//获取当前产能数据
export function getcurCapacityAPI(data = {}) {
    return request({
        url: `${configAxios.taskHubServer}/h5/processContro/curCapacity`,
        method: "POST",
        data,
    });
}

//获取当前产能数据 汇总
export function getcurCapacityCountAPI(data = {}) {
    return request({
        url: `${configAxios.taskHubServer}/h5/processContro/curCapacity/count`,
        method: "POST",
        data,
    });
}

//获取当前复盘数据 汇总
export function getReviewCountAPI(data = {}) {
    return request({
        url: `${configAxios.taskHubServer}/h5/processContro/review/count`,
        method: "POST",
        data,
    });
}



//获取存量 七件事 - 控流失
export function getControlLossAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/sevenThings/controlLoss`,
        method: "POST",
        data,
    });
}


//获取存量 七件事 - 提价值
export function getEnhanceValueAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/sevenThings/enhanceValue`,
        method: "POST",
        data,
    });
}

//七件事 详情-筑墙打坝
export function getzqDetailAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/sevenThings/controlLoss/zqDetail`,
        method: "POST",
        data,
    });
}


//七件事 详情-权益
export function getqyDetailAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/sevenThings/enhanceValue/qyDetail`,
        method: "POST",
        data,
    });
}
//以店包片

export function getStorePackageListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/storePackage/channel`,
        method: "POST",
        data,
    });
}



//获取当日登录率
export function getTodayTotalCountAPI() {
    return request({
        url: `${configAxios.taskHubServer}/manager/login/today_count`,
        method: "GET",
    });
}
//以店包片
export function getLoginDetailAPI(data = {}) {
    return request({
        url: `${configAxios.taskHubServer}/manager/login/detail`,
        method: "POST",
        data,
    });
}
//动作积分 -汇总
export function getActionPointsCountAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/dzjf/count`,
        method: "POST",
        data,
    });
}

//动作积分 -列表
export function getActionPointsListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/dzjf/list`,
        method: "POST",
        data,
    });
}


//重点关注
export function getKeyFocusAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/keyFocus/kdcj`,
        method: "POST",
        data,
    });
}

//政企存量列表
export function getClyyReportL2ListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/ClyyReportL2/list`,
        method: "POST",
        data,
    });
}
//政企存量列表
export function getqueryPkgListAPI(params: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/hypotonic/queryPkgList`,
        method: "get",
        params,
    });
}