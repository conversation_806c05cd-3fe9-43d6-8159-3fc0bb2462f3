// store/sidebar.ts
import { defineStore } from 'pinia';
import Cookies from 'js-cookie';

interface SidebarState {
  opened: boolean;
  withoutAnimation: boolean;
}

interface State {
  sidebar: SidebarState;
  device: string;
}

export const useSidebarStore = defineStore('sidebar', {
  state: (): State => ({
    sidebar: {
      opened: (() => {
        const status = Cookies.get('sidebarStatus');
        return status !== undefined ? Boolean(Number(status)) : true;
      })(),
      withoutAnimation: false,
    },
    device: 'desktop',
  }),
  actions: {
    toggleSidebar() {
      this.sidebar.opened = !this.sidebar.opened;
      this.sidebar.withoutAnimation = false;
      Cookies.set('sidebarStatus', this.sidebar.opened ? '1' : '0');
    },
    closeSidebar(withoutAnimation: boolean) {
      Cookies.set('sidebarStatus', '0');
      this.sidebar.opened = false;
      this.sidebar.withoutAnimation = withoutAnimation;
    },
    toggleDevice(device: string) {
      this.device = device;
    },
  },
});
