<template>
    <div id="container"></div>
</template>

<script setup lang="ts">
/**
 * 腾讯地图组件
 * <AUTHOR>
 * @date 2024-12-19
 * 提供腾讯地图定位和标记功能
 */
import { showFailToast } from 'vant';

declare const TMap: any;
declare const qq: any;

// 地图数据
const dataMap = reactive({
    latitude: 32.061515, // 纬度
    longitude: 118.781288, // 经度
});

// 事件发射
const emit = defineEmits(['getBuildingName']);

// 腾讯地图开发者 Key
const KEY = 'SULBZ-D2ER4-HD4US-FQJXQ-G7YYV-CLFS6';

// 腾讯地图实例和标记层实例
let map: any = null;
let markerLayerMap: any = null;
// 初始化地图
async function initMap() {
    try {
        const center = new TMap.LatLng(dataMap.latitude, dataMap.longitude);

        // 创建地图实例
        map = new TMap.Map(document.getElementById('container'), {
            center: center, // 设置地图中心点坐标
            zoom: 16, // 设置地图缩放级别
            viewMode: '2D',
        });

        // 添加标记层
        addMarkerLayer();

        // 获取当前位置
        getMapLocation();
    } catch (error) {
        console.error('地图初始化失败:', error);
        showFailToast('地图加载失败，请刷新重试！');
    }
}

// 工具函数：检查值是否存在
const isNotUndefined = (val: any): string => {
    return val ? val : '';
}
// 获取当前位置信息
function getMapLocation() {
    try {
        console.log('开始腾讯地图定位...');

        /** 获取用户位置——腾讯地图版 */
        const geolocation = new (window as any).qq.maps.Geolocation(
            KEY,
            'map_h5'
        );

        geolocation.getLocation(
            (res: any) => {
                // 监听定位成功事件
                (window as any).listener?.on(
                    'common.geolocation',
                    'success',
                    function (evt: any, loc: any) {
                        const result: any = loc.addr || loc.city;
                        console.log('定位监听成功:', result);
                    }
                );

                // 监听定位失败事件
                (window as any).listener?.on('common.geolocation', 'fail', function () {
                    console.log('定位监听失败');
                    showFailToast('定位失败，请检查权限或网络！');
                });

                // 更新地图数据
                dataMap.latitude = res.lat;
                dataMap.longitude = res.lng;
                console.log('定位成功:', res);

                const { lat, lng } = res;
                // 构建位置名称
                const buildingName =
                    isNotUndefined(res.nation) +
                    isNotUndefined(res.province) +
                    isNotUndefined(res.city) +
                    isNotUndefined(res.district) +
                    isNotUndefined(res.addr);
                const assistName = isNotUndefined(res.district) + isNotUndefined(res.addr);
                // 触发事件传递建筑名称和坐标
                emit('getBuildingName', { buildingName, lng: lng, lat: lat, assistName });
                // 重新加载地图
                reloadMap();
            },
            (err: any) => {
                console.error('定位失败:', err);
                showFailToast('定位失败，请检查权限或网络！');
            },
            {
                enableHighAccuracy: true,
            }
        );
    } catch (error) {
        console.error('获取位置失败:', error);
        showFailToast('获取位置失败，请重试！');
    }
}
// 重新加载地图
function reloadMap() {
    try {
        // 清空容器
        const container = document.getElementById('container');
        if (container) {
            container.innerHTML = '';
        }

        // 重置标记层
        markerLayerMap = null;

        // 创建新的地图中心点
        const center = new TMap.LatLng(dataMap.latitude, dataMap.longitude);

        // 重新创建地图实例
        map = new TMap.Map(document.getElementById('container'), {
            center: center, // 设置地图中心点坐标
            viewMode: '2D',
            zoom: 16, // 设置地图缩放级别
        });

        // 重新添加标记层
        addMarkerLayer();
    } catch (error) {
        console.error('重新加载地图失败:', error);
        showFailToast('地图重新加载失败！');
    }
}

// 添加标记层
function addMarkerLayer() {
    try {
        markerLayerMap = new TMap.MultiMarker({
            map: map, // 指定地图容器
            // 样式定义
            styles: {
                // 创建一个styleId为"myStyle"的样式（styles的子属性名即为styleId）
                myStyle: new TMap.MarkerStyle({
                    width: 25, // 点标记样式宽度（像素）
                    height: 35, // 点标记样式高度（像素）
                    // "src": '../../assets/logo.png',  // 图片路径
                    background: 'pink',
                    // 焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
                    anchor: { x: 16, y: 32 },
                }),
            },
            // 点标记数据数组
            geometries: [
                {
                    id: '1', // 点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
                    styleId: 'myStyle', // 指定样式id
                    position: new TMap.LatLng(dataMap.latitude, dataMap.longitude), // 点标记坐标位置
                },
            ],
        });
    } catch (error) {
        console.error('添加标记层失败:', error);
    }
}
// 销毁地图实例
onUnmounted(() => {
    if (map) {
        map.destroy();
        map = null;
    }
    if (markerLayerMap) {
        markerLayerMap = null;
    }
});

// 组件挂载时初始化地图
onMounted(() => {
    initMap();
});

// 暴露方法供父组件调用
defineExpose({
    getMapLocation
});

</script>

<style scoped lang="scss">
// 隐藏腾讯地图的默认控件和水印
:deep(.tmap-zoom-control) {
    display: none !important;
}

:deep(.tmap-scale-control) {
    display: none !important;
}

:deep(.tmap-logo) {
    display: none !important;
}

:deep(.tmap-copyright) {
    display: none !important;
}

// 隐藏腾讯地图水印和版权信息
:deep(a) {
    img {
        display: none !important;
    }
}

:deep(.logo-text) {
    display: none !important;
}

// 隐藏腾讯地图底部版权文字
:deep(.csssprite) {
    display: none !important;
}

// 隐藏可能的其他水印元素
:deep(.tmap-attribution) {
    display: none !important;
}

:deep(.tmap-watermark) {
    display: none !important;
}

// 隐藏地图底部的版权信息链接
:deep(div[style*="position: absolute"][style*="bottom"]) {
    a {
        display: none !important;
    }
}
</style>
