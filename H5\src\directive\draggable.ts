import { DirectiveBinding } from 'vue';

interface Position {
    x: number;
    y: number;
}

const handlersMap = new WeakMap<HTMLElement, { onMouseDown: (event: MouseEvent) => void, onTouchStart: (event: TouchEvent) => void }>();

const draggable = {
    install: (app: any) => {
        app.directive('draggable', {
            mounted(el: HTMLElement, binding: DirectiveBinding<Position>) {
                const position = binding.value;

                // 设置初始位置
                el.style.position = 'absolute';
                el.style.left = `${position.x}px`;
                el.style.top = `${position.y}px`;
                el.style.zIndex = '9999';

                let startX = 0;
                let startY = 0;
                let initialLeft = 0;
                let initialTop = 0;

                const onMove = (event: MouseEvent | TouchEvent) => {
                    event.preventDefault();

                    let clientX = 0;
                    let clientY = 0;

                    if (event instanceof MouseEvent) {
                        clientX = event.clientX;
                        clientY = event.clientY;
                    } else if (event instanceof TouchEvent) {
                        clientX = event.touches[0].clientX;
                        clientY = event.touches[0].clientY;
                    }

                    const deltaX = clientX - startX;
                    const deltaY = clientY - startY;

                    let newLeft = initialLeft + deltaX;
                    let newTop = initialTop + deltaY;

                    // 限制边界
                    const maxX = document.documentElement.clientWidth - el.offsetWidth;
                    const maxY = document.documentElement.clientHeight - el.offsetHeight;

                    newLeft = Math.max(0, Math.min(maxX, newLeft));
                    newTop = Math.max(0, Math.min(maxY, newTop));

                    // 更新位置
                    el.style.left = `${newLeft}px`;
                    el.style.top = `${newTop}px`;

                    // 更新绑定的值
                    binding.value.x = newLeft;
                    binding.value.y = newTop;
                };

                const onUp = () => {
                    document.body.style.overflow = ''; // 恢复滚动
                    document.removeEventListener('mousemove', onMove);
                    document.removeEventListener('mouseup', onUp);
                    document.removeEventListener('touchmove', onMove);
                    document.removeEventListener('touchend', onUp);
                };

                const onMouseDown = (event: MouseEvent) => {
                    // event.preventDefault();
                    document.body.style.overflow = 'hidden'; // 禁用滚动

                    startX = event.clientX;
                    startY = event.clientY;
                    initialLeft = el.offsetLeft;
                    initialTop = el.offsetTop;

                    document.addEventListener('mousemove', onMove);
                    document.addEventListener('mouseup', onUp);
                };

                const onTouchStart = (event: TouchEvent) => {
                    // event.preventDefault();
                    document.body.style.overflow = 'hidden'; // 禁用滚动

                    startX = event.touches[0].clientX;
                    startY = event.touches[0].clientY;
                    initialLeft = el.offsetLeft;
                    initialTop = el.offsetTop;

                    document.addEventListener('touchmove', onMove, { passive: false });
                    document.addEventListener('touchend', onUp);
                };

                el.addEventListener('mousedown', onMouseDown);
                el.addEventListener('touchstart', onTouchStart, { passive: false });

                // 保存事件处理器
                handlersMap.set(el, { onMouseDown, onTouchStart });
            },
            unmounted(el: HTMLElement) {
                const handlers = handlersMap.get(el);
                if (handlers) {
                    el.removeEventListener('mousedown', handlers.onMouseDown);
                    el.removeEventListener('touchstart', handlers.onTouchStart);
                    handlersMap.delete(el);
                }

                // 还原滚动条状态
                document.body.style.overflow = '';
            }
        });
    }
};

export default draggable;
