<template>
    <div>
        <van-popup v-model:show="show" round position="bottom" teleport="body">
            <van-picker title="人员列表" :columns="staffList" :columns-field-names="{
                text: 'departmentName',
                value: 'departmentId'
            }" @confirm="confirmDepartment" @cancel="show = false">
                <template #title>
                    <van-search class="mt-5 mb-5" v-model="searchDepartmentName" placeholder="请输入搜索关键词" />
                </template>
            </van-picker>
        </van-popup>

    </div>
</template>

<script setup lang="ts">
import { getDepartmentListAPI } from "@/api/dispatch";
const departmentList = ref<{ departmentName: string, departmentId: string }[]>([])

const show = defineModel('show', { type: Boolean, default: false })
const Emits = defineEmits(['changeDepartment',])


const searchDepartmentName = ref('')


const staffList = computed(() => {
    const input = searchDepartmentName.value.toLowerCase();
    const List = [{ departmentName: '全部部门', departmentId: '' }, ...departmentList.value]

    return List.filter(item => {
        const staffName = item.departmentName ? item.departmentName.toLowerCase() : '';

        return (
            staffName.includes(input)
        );
    });
})

const loadDepartmentList = () => {
    getDepartmentListAPI().then((res: any) => {
        if (res.code == 200) {
            departmentList.value = res.data

        }
    })
}
const confirmDepartment = (params: any) => {
    show.value = false
    Emits('changeDepartment', params)

}
onMounted(() => {
    loadDepartmentList()
})

</script>

<style lang="scss" scoped></style>