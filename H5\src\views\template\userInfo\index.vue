<template>
    <div class="userInfoRef" ref="userInfoRef" :style="{
        paddingTop: `${containerHeight}px`
    }">
        <div class="message-scroll" v-if="targetIsVisible && messages.length > 0">
            <van-icon color="#1A76FF" name="volume-o" size="14" />
            <div class="message-container" :style="{ height: containerHeight + 'px' }">

                <transition-group name="message" tag="div" class="message-wrapper">
                    <div v-for="(item, index) in visibleMessages" :key="index" class="message-item" ref="messageRefs"
                        :style="{ transform: isEntering ? `translateY(${containerHeight}px)` : 'translateY(0)' }">
                        {{ item.content }}
                    </div>
                </transition-group>
            </div>
            <div style="flex: 0 0 auto;color: #327FF0 ;" class="fs12" @click="gotoDetail">点击查看<van-icon name="arrow"
                    color="#327FF0" />
            </div>
        </div>



        <div class="home_top">
            <img src="@/assets/defaultAvatar.png" alt="" srcset="">
            <div class="ml-17">
                <div style="display: flex; align-items: center;" class=" mb-8 mt-8">
                    <div class="name">{{ userInfo.staffName }}</div>
                    <div class="tag">{{ userInfo.primaryRoleName }}</div>
                </div>
                <div class="fs14" style="color:#3D3D3D;">部门:{{ userInfo.orgName }}</div>
                <div style="display: flex;align-items: center;" class="bottom">
                    <van-badge :content="0" color="#ED4747" :show-zero="false" v-if="false">
                        <img src="@/assets/home/<USER>" alt="" srcset="">
                    </van-badge>
                    <van-badge :content="messagesLength" color="#ED4747" :show-zero="false" v-if="userInfoConfig[0]">
                        <router-link :to="{ path: `/${userInfoConfig[0].menuCode}` }">
                            <img src="@/assets/home/<USER>" alt="" srcset="">
                        </router-link>

                    </van-badge>
                </div>
            </div>

        </div>
    </div>



</template>

<script setup lang="ts">
import { onMounted, reactive, ref, nextTick, onUnmounted, computed, watch } from "vue";
import { getMessageSummaryBySenderAPI, readMessageAPI } from "@/api/message";
import { useElementVisibility } from '@vueuse/core'
import { useRouter } from "vue-router";
const router = useRouter()
interface MessageItem {
    id: number;
    title: string;
    content: string;
    msgType: number;
    createBy: string; //工号
    msgSource: number; //消息来源 100-人工 200-派单 201-配餐 202-到岗 203-复盘 300-系统
    readFlag: number; // 0: 未读, 1: 已读
    createTime: string;
    portrait?: string
    notice?: string
}


// 定义一个引用，用于存储用户信息元素的引用
const userInfoRef = ref<HTMLElement>();

// 将用户信息元素的引用赋值给目标引用
const target = ref(userInfoRef);

// 使用 useElementVisibility 子函数来检测目标元素是否可见
const targetIsVisible = useElementVisibility(target);


import { useUserInfo } from "@/stores/userInfo";
const userInfoStore = useUserInfo();
const userInfo = computed(() => userInfoStore.getUserInfo)

const messages = ref<MessageItem[]>([]);


const visibleMessages = ref<MessageItem[]>([]);
const containerHeight = ref(0);
const messageRefs = ref<HTMLElement[]>([]);
const isEntering = ref(false);
let currentIndex = 0;

let animationInterval: number | null = null;

// 计算消息元素的高度
const calculateHeight = () => {
    if (messageRefs.value.length > 0) {
        const messageElement = messageRefs.value[0];
        if (messageElement) {
            // return messageElement.offsetHeight ;
            return messageElement.offsetHeight + 20;
        }
    }
    return 30; // 默认高度
};




const userInfoConfig = computed(() => {
    let list = userInfoStore.getMenuList.filter((item: MenuItem) => item.menuCode === 'home')[0]?.children || []
    let userInfolist = list.filter((item: MenuItem) => item.menuCode === 'userInfo')[0]?.children || []
    return userInfolist


})







function gotoDetail() {
    const RouterList: Record<number, { link: string; query?: Record<string, any> }> = {
        202: {
            link: `/dispatchSOP${userInfoConfig.value[0].menuCode.includes('Lead') ? 'Lead' : ''}`,
            query: { type: 0 },
        },
        203: {
            link: `/dispatchSOP${userInfoConfig.value[0].menuCode.includes('Lead') ? 'Lead' : ''}`,
            query: { type: 1 },
        },
        201: {
            link: `/dispatchSOP${userInfoConfig.value[0].menuCode.includes('Lead') ? 'Lead' : ''}`,
            query: { type: 2 },
        },
        200: {
            link: '/tips',
        },
    };
    const row = messages.value[currentIndex]
    readMessageAPI({ msgSource: row.msgSource }).then((res: any) => {
        if (res.code == 200) {
            router.push({ path: RouterList[row.msgSource].link, query: RouterList[row.msgSource].query })
        }
    })
}


const initAnimation = () => {
    visibleMessages.value = [messages.value[currentIndex]];

    animationInterval = window.setInterval(async () => {
        isEntering.value = true;

        // 在切换消息前，先计算新消息的高度
        const nextIndex = (currentIndex + 1) % messages.value.length;
        visibleMessages.value = [messages.value[nextIndex]];

        // 等待 DOM 更新
        await nextTick();

        // 更新当前索引
        currentIndex = nextIndex;

        // 延迟设置 isEntering 为 false，让滚动动画完成
        setTimeout(() => {
            isEntering.value = false;

            // 滚动动画完成后，再调整容器高度
            setTimeout(() => {
                const newHeight = calculateHeight();
                containerHeight.value = newHeight;
            }, 50);
        }, 500);
    }, 3000);
}

const messagesLength = ref(0)
// 获取消息列表
const getMessageList = () => {
    getMessageSummaryBySenderAPI({ readFlag: 0  }).then((res: any) => {
        if (res.code == 200) {
            messagesLength.value = res.data.length
            messages.value = res.data.filter((item: MessageItem) => item.msgType == 2)
            if (messages.value.length > 0) {
                initAnimation()
                containerHeight.value = 30
            }
        }
    })
}
watch(() => userInfoConfig.value, (newVal) => {
    if (newVal.length > 0 && newVal.some((item: MenuItem) => item.menuCode.includes('message'))) {
        getMessageList()
    }

}, { immediate: true })

onMounted(() => {

});

onUnmounted(() => {
    if (animationInterval) {
        clearInterval(animationInterval);
        animationInterval = null;
    }
});





</script>

<style lang="scss" scoped>
.home_top {
    display: flex;
    padding: 20px 10px;

    img {
        width: 59px;
        height: 59px;
        border-radius: 50%;
        // border: 1.5px solid rgb(255, 255, 255);
    }

    .name {
        font-family: 'Source Han Sans, Source Han Sans';
        font-weight: 700;
        font-size: 18px;
        color: #3D3D3D;
    }

    .tag {
        background: linear-gradient(270deg, #FFC062 0%, #F46B08 100%);
        border-radius: 5px 5px 5px 5px;
        text-align: center;
        padding: 2px 3px;
        color: #fff;
        font-size: 14px;
        margin-left: 8px;

    }

    .bottom {
        margin-top: 18px;

        img {
            width: 30px;
            height: 30px;
        }
    }

}

:deep {
    .van-badge--top-right {
        transform: translate(35%, -35%);
    }
}

.message-scroll {
    position: fixed;
    left: 0px;
    top: 0px;
    width: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #EDF5FF;
    padding-right: 10px;
    padding-left: 10px;
}

.message-container {
    flex: 1;
    min-height: 30px;
    padding: 10px 0;
    transition: height 0.5s ease;
    position: relative;
}

.message-wrapper {
    position: relative;
}

.message-item {
    font-weight: 400;
    font-size: 12px;
    color: #FF5D44;
    line-height: 13px;

    position: absolute;
    width: 100%;
    transition: all 0.5s ease;
    padding: 0px 5px;
}

.message-enter-active,
.message-leave-active {
    transition: all 0.5s ease;
}

.message-enter-from {
    transform: translateY(100%);
    opacity: 0;
}

.message-leave-to {
    transform: translateY(-100%);
    opacity: 0;
}
</style>