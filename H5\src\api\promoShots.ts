
import request from "../utils/request";
import configAxios from './config'

//根据经纬度获取网格id
export function getPromoShotsGridAPI(params: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/communityBattle/grid`,
        method: "GET",
        params,
    });
}

// 保存宣传照片
export function savePromoShotsAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/communityBattle/PromotionalPhotography/save`,
        method: "post",
        data,
    });
}


// 获取宣传照片列表
export function getPromoShotsListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/communityBattle/PromotionalPhotography/queryPage`,
        method: "post",
        data,
    });
}

// 网格汇总
export function getGridTypeCountAPI() {
    return request({
        // url: `${configAxios.taskHubServer}/h5/communityBattle/gridType/count`,
        url: `${configAxios.taskHubServer}/h5/communityBattle/gridType/checkingCount`,

        method: "post",

    });
}

// 网格明细
export function getGridTypeDetailAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/communityBattle/gridType/detail`,
        method: "post",
        data,
    });
}
// 网格明细
export function updateGridTypeDetail(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/communityBattle/gridType/updateDetail`,
        method: "post",
        data,
    });
}


// 获取管理端战区攻防总览数据
export function getAllAreaRateAPI(data = {}) {
    return request({
        url: `${configAxios.taskHubServer}/manager/communityBattle/allAreaRate`,
        method: "post",
        data
    });
}

// 获取管理端 战区攻防管理详情数据
export function getAreaDetailAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/manager/communityBattle/areaDetail`,
        method: "post",
        data,
    });
}
// 获取管理端 战区攻防管理详情数据
export function getAreaDetailListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/manager/communityBattle/areaDetailList`,
        method: "post",
        data,
    });
}
// 获取管理端 战区攻防分局统计数据
export function getAggAreaL2API(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/manager/communityBattle/aggAreaL2`,
        method: "post",
        data,
    });
}
// 获取管理端 宣传检查统计 已检查网格 数据
export function getPromotionEvaluationAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/manager/communityBattle/promotionEvaluation`,
        method: "post",
        data,
    });
}





// 获取触点端 战区攻防分局统计数据
export function getGridDetailAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/communityBattle/gridDetail`,
        method: "post",
        data,
    });
}

export function getAreaL2DetailAPI(data = {}) {
    return request({
        url: `${configAxios.taskHubServer}/h5/communityBattle/areaDetail`,
        method: "post",
        data,
    });
}



// 获取管理端 战区攻防分局统计数据
export function getManagementGridDetailAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/manager/communityBattle/gridDetail`,
        method: "post",
        data,
    });
}
// 获取管理端 根据主键ID获取查询详情
export function getGridDetailByIdAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/manager/communityBattle/gridDetailById`,
        method: "post",
        data,
    });
}