<template>
    <div class='mt-16 container_' v-loading="loadingConfig">
        <div class="container__header">
            <div>
                <img src="./assets/development.svg" alt="" srcset="">
                <span class="ml-6">发展情况</span>

                <div class="checkbox-wrapper-10  ml-4" style="display: flex;align-items: center;"
                    v-if="userInfoStore.userInfo.orgDept == UserType.Enterprise || userInfoStore.userInfo.orgDept == UserType.Other">
                    <input :checked="isGov" type="checkbox" id="cb5" @change="changeIsGov" class="tgl tgl-flip">
                    <label for="cb5" data-tg-on="政企" data-tg-off="公众" class="tgl-btn"></label>

                    <i class="icon-shouzhixuanzhong-copy left mb-4" style="color: #1A76FF;"></i>
                </div>
            </div>
            <cusTabs :tabList="tabList" v-model:curSelectTab="curSelectTab" @changeSelectTab="changeSelectTab">
            </cusTabs>

        </div>

        <div class=" container_body ">
            <div class="activity-container" ref="activityContainer">
                <van-row gutter="5" class="table-header" align="center">
                    <van-col class="row-item" :class="item.isStatic ? 'static-col-item' : ''" :span="item.span"
                        v-for="item in rowList">{{ item.name }}</van-col>

                    <div v-if="showRightArrow" class="right-arrow" @click="scrollToRight">
                        <van-icon name="arrow-double-right" size="16" color="#A3CFFF" />
                    </div>
                </van-row>
                <van-row gutter="5" class="table-body" align="center" v-for="col, index in tableData" :key="index">
                    <van-col class="col-item" :class="item.isStatic ? 'static-col-item' : ''"
                        :style="{ color: item.props == 'staffName' ? '#1a76ffab' : '' }"
                        v-getName="{ item: col, props: item.props }" :span="item.span" v-for="item in rowList">{{
                            col[item.props]  }}</van-col>
                </van-row>
            </div>
            <van-empty description="暂无数据" v-if="tableData.length == 0" />

        </div>
    </div>
</template>

<script setup lang='ts'>
import { ref, reactive, onMounted, onUnmounted, computed, toRefs } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { getDevelopmentAPI, getGovDevelopmentAPI } from "@/api/userData";
import cusTabs from "@/components/cusTabs/index.vue";
import { useUserInfo } from "@/stores/userInfo";
const userInfoStore = useUserInfo()
enum UserType {
    Other = 0,  // 其他
    Public = 1, // 公众
    Enterprise = 2 // 政企
}
interface RowItem {
    span: number;
    name: string;
    props: string;
    default: any;
    [key: string]: any
}

const loadingConfig = reactive<LoadingDirectiveOptions>({
    visible: false, // 控制加载状态
    text: '请稍等...', // 自定义加载文本
    iconSize: '36',
    iconColor: '#fff',
    borderRadius: '10px',
});
const isGov = ref(false)
const rowList = computed<RowItem[]>(() => {
    let List = [
        {
            span: 4,
            name: '姓名',
            props: 'staffName',
            default: '',
            isStatic: true
        },

        {
            span: 4,
            name: '角色',
            props: 'primaryRoleName',
            default: ''

        }
    ]
    let PublicList = [
        {
            span: 4,
            name: '积分',
            props: 'points',
            default: 0

        },


        {
            span: 4,
            name: '天翼',
            props: 'tianYi',
            default: 0

        },
        {
            span: 4,
            name: '宽带',
            props: 'kuanDai',
            default: 0

        },
        {
            span: 4,
            name: '融合',
            props: 'ronghe',
            default: 0

        },
        {
            span: 4,
            name: '新装高套',
            props: 'xinliangZgz',
            default: 0

        },
        {
            span: 4,
            name: '商机',
            props: 'businessRecords',
            default: 0

        },
    ]
    let EnterpriseList = [
        {
            span: 4,
            name: '政企积分',
            props: 'govQudaoJifen',
            default: 0
        },
        {
            span: 4,
            name: '政企天翼',
            props: 'govTianyi',
            default: 0
        },
        {
            span: 4,
            name: '5G主卡',
            props: 'gov5g',
            default: 0
        },
        {
            span: 4,
            name: 'FTTR-B',
            props: 'govFttrb',
            default: 0
        },
        {
            span: 4,
            name: '商专',
            props: 'govShangZhuan',
            default: 0
        },
        {
            span: 4,
            name: '商专',
            props: 'govShangZhuan',
            default: 0
        },
        {
            span: 4,
            name: '天翼视联',
            props: 'govTianyiShilian',
            default: 0
        },
        {
            span: 4,
            name: '离网积分',
            props: 'govLiwang',
            default: 0
        },

    ]


    if (isGov.value) {
        return [...List, ...EnterpriseList]
    } else {
        return [...List, ...PublicList]
    }
})

//当前 默认选择 当日
const curSelectTab = ref(0)
// tab 列表
const tabList = ref([
    { text: '当日', value: 0 },
    { text: '当月', value: 1 },

])


const tableData = ref<RowItem[]>([])



function loadDevelopmentLeader() {
    loadingConfig.visible = true

    const API = isGov.value ? getGovDevelopmentAPI : getDevelopmentAPI
    API({ type: curSelectTab.value }).then(res => {


        const defaultMap = new Map<string, any>(rowList.value.map(({ props, default: defaultValue }) => [props, defaultValue]));

        // 定义角色优先级映射，数字越小优先级越高
        const rolePriorityMap: { [role: string]: number } = {
            '分局长': 1,
            '副分局长': 2,
            '客经': 3,
            '片区长': 4,
            '门店经理': 5,
            '店长': 6,
            '值班经理': 7,
            '营业员': 8,
            '装维班长': 9,
            '装维': 10,
            '行销': 11,
            '网格': 12,
            '客户经理': 13
        };


        tableData.value = res.data.map((item: RowItem) => ({
            ...item,
            ...Object.fromEntries(
                Object.entries(item).map(([key, value]) =>
                    [key, value === null ? defaultMap.get(key) : value] // 如果值为 null，则从 defaultMap 获取默认值
                )
            )
        }));
        // 排序：先按照角色优先级排序，再按照积分排序
        tableData.value.sort((item1: RowItem, item2: RowItem) => {
            const rolePriority1 = rolePriorityMap[item1.primaryRoleName] || 99; // 99表示其他角色优先级最低
            const rolePriority2 = rolePriorityMap[item2.primaryRoleName] || 99;

            if (rolePriority1 !== rolePriority2) {
                return rolePriority1 - rolePriority2; // 先比较角色优先级
            }

            return item2.points - item1.points; // 如果角色优先级相同，则按积分从高到低排序
        });
    }).finally(() => {
        loadingConfig.visible = false

    })
}

function changeSelectTab() {
    loadDevelopmentLeader()
}


const activityContainer = ref<HTMLElement | null>(null); // 用来引用容器
const showRightArrow = ref<boolean>(false); // 控制箭头显示的变量


// 处理滚动事件
const handleScroll = () => {
    if (!activityContainer.value) return;
    const container = activityContainer.value;
    // 判断是否能继续向右滚动
    const isScrolledToRight = container.scrollWidth > container.clientWidth && container.scrollLeft < container.scrollWidth - container.clientWidth;




    showRightArrow.value = isScrolledToRight;
};

// 滚动到右侧
const scrollToRight = () => {
    if (activityContainer.value) {
        activityContainer.value.scrollTo({ left: activityContainer.value.scrollWidth, behavior: "smooth" });
    }
};

function changeIsGov() {
    isGov.value = !isGov.value
    loadDevelopmentLeader()
}

watch(() => userInfoStore.userInfo.orgDept, (newValue) => {

    switch (newValue) {
        case UserType.Public:
            isGov.value = false
            break;
        case UserType.Enterprise:
            isGov.value = true
            break;
        case UserType.Other:
            isGov.value = true
            break;
        default:
            isGov.value = false

            break;
    }
}, { deep: true, immediate: true })


// 生命周期钩子
onMounted(() => {

    if (activityContainer.value) {
        activityContainer.value.addEventListener("scroll", handleScroll);
    }
    handleScroll()
    loadDevelopmentLeader()
});

onBeforeUnmount(() => {
    if (activityContainer.value) {
        activityContainer.value.removeEventListener("scroll", handleScroll);
    }
});
</script>
<style lang="scss" src="@/views/template/Contribution/switch.scss" scoped></style>
<style lang='scss' scoped>
.container_body {
    padding: 15px;

    .overview {
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        padding: 15px 20px 10px 13px;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(45px, 1fr));
        gap: 10px;
    }



    .icon-item span {
        font-size: 12px;
        color: #3D3D3D;
        text-align: center;
    }

    .overview-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 10px;
        border-radius: 8px;
        color: #3D3D3D;

        &>span:first-child {
            text-align: center;
            font-weight: 500;
            font-size: 12px;
            color: #666666;
            line-height: 13px;
            margin-bottom: 9px;
        }

        &>span:last-child {
            text-align: center;
            font-weight: 700;
            font-size: 16px;
            color: #333333;
            line-height: 18px;
        }

    }
}

.activity-container {
    overflow-x: auto;
    padding-left: 0px;
    padding-right: 0px;
    position: relative;
}

.static-col-item {
    position: sticky;

    left: 0px;
    z-index: 2;
    background-color: #FBFCFF;

}

.right-arrow {
    position: sticky;
    right: 0px;
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 20px;
    background-color: #1A76FF;
    border-radius: 0px 5px 5px 0px;
}


.row-item {
    font-weight: 400;
    font-size: 12px;
    color: #2079FF;
    text-align: center;
}

.table-header {
    border-bottom: 1px solid #EDF5FF;
    padding-bottom: 10px;
}

.table-body {
    border-bottom: 1px solid #EDF5FF;
    padding: 10px 0px;
}

.col-item {
    font-weight: 400;
    font-size: 12px;
    color: #3D3D3D;
    text-align: center;
}



:deep(.van-row) {
    flex-wrap: nowrap;
}

.left {
    position: relative;
    /* 或者 absolute，确保能看到位置变化 */
    transform: rotate(-90deg);
    animation: identifier_ 1.5s infinite;
}
</style>