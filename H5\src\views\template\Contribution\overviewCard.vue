<template>
    <div class="container_body activity-container">
        <div class="overview_box">
            <div class="overview_header">
                <div class="identification">
                    <span class="van-multi-ellipsis--l2">{{ curTitle }}</span>
                    <div class="time ml-8" @click="calendarIsShow = true">
                        <span>历史贡献</span>
                        <img class="imgSize" src="./assets/historyTime.png" alt="" srcset="">
                    </div>
                </div>
                <div class="time" style="display: flex;align-items: center;" @click="emits('changeAllIsShow')">
                    <span>{{ allIsShow ? '关闭所有' : '展开所有' }}</span>
                    <div class="playBox ml-5">

                        <van-icon name="play" class="play" :class="allIsShow ? '' : 'playHide'" color="#1A76FF" />
                    </div>
                </div>

            </div>
            <div class="overview">
                <div class="overview-item" v-for="item, index in data" :key="index">
                    <span>{{ item.name }}</span>
                    <span> {{ item.denominator ? `${ContributionMenu[item.props]}/${ContributionMenu[item.denominator]}`
                        :
                        ContributionMenu[item.props] }}</span>
                </div>

            </div>

        </div>
        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" :min-date="minDate"
            first-day-of-week="1" switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false"
            :max-date="maxDate" @confirm="onConfirm" />
    </div>

</template>

<script setup lang="ts">
import { computed, reactive, ref, toRefs } from "vue";
import { searchHooks } from "@/hooks_modules/changeTimeHooks.ts";


const props = defineProps({
    curSelectType: {
        type: Number,
        default: 0
    },
    curSelectItem: {
        type: Object,
        default: () => { }
    },
    allIsShow: {
        type: Boolean,
        default: false
    },
    ContributionMenu: {
        type: Object,
        default: (): ContributionLeader => {
            return {
                type: 1,
                points: 100,
                tianYi: 50,
                kuanDai: 30,
                outCall: 20,
                signCount: 0,
                repairOrder: 5,
                businessRecords: 3,
                xinliangZgz: 15,
                repairOrderTotal: 0,
                ronghe: 25,
                tianyiXuyue: 8,
                kuandaiXuyue: 12,

                orgDept: 1,
                gov5g: 0,
                govCloudComputer: 0,
                govFttrb: 0,
                govHuZhuan: 0,
                govShangZhuan: 0,
                govTianyi: 0,
                govTianyiShilian: 0,
                govXiaowei: 0,
                govQudaoJifen: 0,
                bizRecord: 0,
                visitCount: 0,
            }
        }
    },
    isGov: {
        type: Boolean,
        default: false
    }
})
const emits = defineEmits(['changeTime', 'changeAllIsShow'])

const { curSelectType, curSelectItem, allIsShow, ContributionMenu, isGov } = toRefs(props)

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    onConfirm,
} = searchHooks({
    source: 'overviewCard',
    callback: changeTime,
})

const minDate = new Date(2024, 9, 30)


const data = computed(() => {


    if (isGov.value) {


        return [
            { name: '政企积分', props: 'govQudaoJifen' },
            { name: '政企天翼', props: 'govTianyi' },
            { name: '5G主卡', props: 'gov5g' },
            { name: 'FTTR-B', props: 'govFttrb' },
            { name: '商专', props: 'govShangZhuan' },
            { name: '互专', props: 'govHuZhuan' },
            { name: '商机录入', props: 'bizRecord' },
            { name: '客户拜访', props: 'visitCount' },


        ]
    } else {
        return [
            { name: '积分', props: 'points' },
            { name: '天翼', props: 'tianYi' },
            { name: '宽带', props: 'kuanDai' },
            { name: '外呼', props: 'outCall' },
            { name: '工作写实', props: 'signCount' },
            { name: '商机录入', props: 'businessRecords' },
            { name: '主推融合', props: 'xinliangZgz' },
            { name: '天翼续约', props: 'tianyiXuyue' },
            { name: '宽带续约', props: 'kuandaiXuyue' },
            { name: '装维工单', props: 'repairOrder', denominator: 'repairOrderTotal' },
        ]
    }
})




const curTitle = computed(() => {
    let curTypeTitle: string
    switch (curSelectType.value) {
        case 0:
            curTypeTitle = '全部片区'
            break;
        case 1:
            curTypeTitle = '全部门店'

            break;
        case 2:
            curTypeTitle = '全部人员'
            break;
        default:
            curTypeTitle = '';
    }
    return curSelectItem.value.name || curTypeTitle

})

function changeTime() {
    emits('changeTime', formline)
}


</script>
<style lang="scss" scoped>
.container_ .container_body {
    padding: 5px 15px;
    font-family: 'Source Han Sans, Source Han Sans';
}

.overview_box {
    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
    padding: 10px 10px 10px 10px;

    .overview_header {
        display: grid;
        grid-template-columns: auto max-content;
        align-items: center;

        .identification {
            font-family: "YouSheBiaoTiHei, YouSheBiaoTiHei";
            font-size: 20px;
            color: #1A76FF;
            font-weight: bold;
            margin-bottom: 5px;
            display: flex;
            // align-items: flex-end;
            align-items: center;
        }

        .time {
            flex: 0 0 auto;
            font-weight: 400;
            font-size: 14px;
            color: #1A76FF;
            line-height: 16px;
            text-align: center;
            font-style: normal;
            display: flex;
            align-items: center;

            .imgSize {
                width: 16px;
                height: 16px;
                margin-left: 3px;
            }
        }

        .playBox {
            flex: 0 0 auto;
            display: grid;
            place-items: center;
            width: 25px;
            height: 25px;
            border-radius: 10px;
            margin-left: 3px;
            background: #d4e0ff70;

            .play {
                transform: rotate(90deg);

                transition: transform 0.3s;


            }

            .playHide {
                transform: rotate(-90deg);
            }
        }


    }


    .overview {

        display: grid;
        grid-template-columns: repeat(5, minmax(5px, 1fr));
        gap: 0px 5px;
        justify-content: space-between;

        .overview-item {
            display: flex;
            min-width: 100%;
            flex-direction: column;
            border-radius: 8px;
            color: #3D3D3D;
            margin: 10px auto;

            &>span:first-child {
                width: 100%;
                white-space: nowrap;
                text-align: center;
                font-weight: 500;
                font-size: 12px;
                color: #666666;
                line-height: 13px;
                margin-bottom: 9px;
            }

            &>span:last-child {
                text-align: center;
                font-weight: 700;
                font-size: 16px;
                color: #333333;
                line-height: 18px;
            }



        }
    }
}
</style>