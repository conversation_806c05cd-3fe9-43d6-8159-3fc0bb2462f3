<template>
    <div>
        <div class="card-header">
            <div class="selectOption-item" @click="calendarIsShow = true">
                <span>{{ time ? time : '请选择时间' }}</span>
                <van-icon name="play" class="play" color="#1A76FF" />
            </div>
        </div>
        <div class=' container_ mt-6'>
            <div class="container__header">
                <div style="flex: 1 1 auto;">
                    <img src="@/assets/computed.png" alt="" srcset="">
                    <span class="ml-6">{{ showOrgName }}动作积分</span>
                </div>

                <cusTabs class="ml-5" style="flex: 1 0 auto;" :tabList="tabList" v-model:curSelectTab="curSelectType"
                    @changeSelectTab="changeSelectTab">
                </cusTabs>
            </div>
            <div class="container_body  ">
                <div v-for="(item, index) in tableData" :key="index" class="item">
                    <div style="display: flex;align-items: center;justify-content: space-between;">
                        <div class="label" v-getName="{ item: item }">{{ item.staffName }}</div>
                    </div>
                    <div class="numbers">
                        <span class="jf">
                            <div>{{ item.totalPoints }}</div>
                            <div>总积分</div>
                        </span>
                        <span class="ty">
                            <div>{{ item.noAppointmentAdd }}</div>
                            <div>无约加约</div>
                        </span>
                        <span class="kd">
                            <div>{{ item.arrearsWinBack }}</div>
                            <div>欠费赢回</div>
                        </span>
                        <span class="rh">
                            <div>{{ item.dismantleOrder }}</div>
                            <div>拆机录单</div>
                        </span>
                        <span class="zgz">
                            <div>{{ item.dismantleRetention }}</div>
                            <div>拆机挽留</div>
                        </span>

                    </div>


                </div>
                <van-empty description="暂无数据" v-if="tableData.length == 0" />
            </div>

        </div>

        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />
    </div>
</template>

<script setup lang="ts">
import cusTabs from "@/components/cusTabs/index.vue";
import { getActionPointsListAPI } from "@/api/home";
import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { useUserInfo } from "@/stores/userInfo";
import { useRoute } from "vue-router";

const route = useRoute()
const userInfoStore = useUserInfo();
interface List {
    staffName: string;
    staffCode: string
    totalPoints: number; // 总积分
    noAppointmentAdd: number;
    arrearsWinBack: number;
    dismantleOrder: number;
    dismantleRetention: number;
}

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    onConfirm,
} = searchHooks({
    source: 'actionPointsList',
    callback: loadActionPointsList
})





const tabList = ref([
    { text: '结果类', value: 1 },
    // { text: '动作类', value: 0 },
])


const curSelectType = ref<number>(1)   //1  结果类  //     0  动作类    



function changeSelectTab(params: any) {

}








const showOrgName = computed(() => {
    return userInfoStore.userInfo.orgName
})




const tableData = ref<List[]>([]);




const time = computed(() => {
    if (formline.startTime && formline.endTime) return `${formline.startTime} ~ ${formline.endTime}`
    else return ``
})
function loadActionPointsList() {
    let params = {
        startTime: formline.startTime ? `${formline.startTime} 00:00:00` : undefined,
        endTime: formline.endTime ? `${formline.endTime} 23:59:59` : undefined,
    }
    getActionPointsListAPI(params).then((res: any) => {
        if (res.code == 200) {
            tableData.value = res.data || []
        }


    })
}


onMounted(() => {
    defaultDate.value = [
        route.query.startTime ? new Date(String(route.query.startTime)) : new Date(),
        route.query.endTime ? new Date(String(route.query.endTime)) : new Date()
    ]
    formline.startTime = String(route.query.startTime ?? '')
    formline.endTime = String(route.query.endTime ?? '')

    loadActionPointsList()

})
</script>

<style lang="scss" scoped>
.container_body {
    padding-left: 10px;
    padding-right: 10px;
}

.container__header {
    flex-wrap: wrap;
}

.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

    .selectOption-item {
        flex: 1 0 auto;
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
            position: absolute;
            right: 10px;
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }
}


.item {
    padding: 7px 10px;
    background: #EDF5FF;
    border-radius: 5px;
    margin-bottom: 8px;

    &:last-child {
        border-bottom: none;
    }

    .label {
        font-weight: 700;
        font-size: 14px;
        color: #3D3D3D;
    }

    .numbers {
        display: flex;
        align-items: center;
        justify-content: space-around;
        margin-top: 8px;

        span {
            display: flex;
            flex-direction: column;
            align-items: center;

            &>div:first-child {
                font-weight: 700;
                font-size: 18px;

            }

            &>div:last-child {
                margin-top: 2px;
                font-weight: 400;
                font-size: 12px;

            }
        }

        .jf {
            color: #34C759;
        }

        .ty {
            color: #FF6A1A;
        }

        .kd {
            color: #477BF3;
        }

        .rh {
            color: #FFC34A;
        }

        .zgz {
            color: #E950E1;
        }

    }
}



:deep(.van-popover__wrapper) {
    flex: 1 0 auto;
}

:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
    width: auto;
}
</style>