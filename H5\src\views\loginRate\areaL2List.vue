<template>
    <div>
        <div class="card-header1">
            <van-popover :actions="areaList" @select="changeActionType" placement="bottom">
                <template #reference>
                    <div class="selectOption-item">
                        <span>{{ curArea.areaName }}</span>
                        <van-icon name="play" class="play" color="#1A76FF" />
                    </div>
                </template>
            </van-popover>
        </div>

        <div class=' container_ mt-6'>
            <div class="container__header">
                <div>
                    <img src="@/assets/resultOptimization.svg" alt="" srcset="">
                    <span class="ml-6">{{ title }}</span>
                </div>
            </div>


            <div class="container_body ">
                <div class="card-list ">
                    <div class="card" v-for="item, index in tabList" :key="index">
                        <div class="card-header">
                            <span class="role-name">{{ item.areaNameL2 }}</span>
                            <div class="go-button" @click="GO(item)">
                                <span>GO</span>
                                <van-icon name="arrow" color="#fff" />
                            </div>
                        </div>
                        <div class="card overvirwCard" style="margin-bottom: 6px">

                            <div class="card-body" style="background-color: #E9FDF2;">
                                <div class="status-block">
                                    <img src="@/assets/login_Y.svg" alt="" srcset="">

                                    <div class="info">
                                        <p class="status-row">
                                            <span>已登录人数</span>
                                            <span class="color2">{{ item.loginCount }}</span>
                                        </p>
                                        <p class="status-row">
                                            <span>占比</span>
                                            <span class="color2">{{ item.loginRate }}%</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body" style="background: #FFF3F2;">
                                <div class="status-block">
                                    <img src="@/assets/login_N.svg" alt="" srcset="">

                                    <div class="info">
                                        <p class="status-row">
                                            <span>未登录人数</span>
                                            <span class="color0">{{ item.unLoginCount }}</span>
                                        </p>
                                        <p class="status-row">
                                            <span>占比</span>
                                            <span class="color0">{{ item.unLoginRate }}%</span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <van-empty description="暂无数据" v-if="tabList.length == 0" />
            </div>




        </div>
    </div>
</template>

<script setup lang="ts">
import { getlistDeptOrSubOptions } from "@/hooks_modules/getlistDeptOrSubOptions";
import { useRoute, useRouter } from "vue-router";
import {  getLoginDetailAPI } from "@/api/home";

interface LoginData {
    loginCount: number; // 登录次数
    loginRate: number;  // 登录率（百分比）
    totalCount: number; // 总人数
    unLoginCount: number; // 未登录人数
    unLoginRate: number; // 未登录率（百分比）
    [key: string]: any
}
interface AreaData extends LoginData {
    areaId: string | null;     // 区域 ID（可能为空）
    areaIdL2: string | null;          // 二级区域 ID
    areaName: string | null;   // 区域名称（可能为空）
    areaNameL2: string;        // 二级区域名称
    staffName: string | null;  // 员工姓名（可能为空）
    staffRole: string | null;  // 员工角色（可能为空）
    status: string | null;     // 状态（可能为空）
}


const router = useRouter()
const route = useRoute()
const { areaList } = getlistDeptOrSubOptions(
    {
        isPushAll: true,
    })

const tabList = ref<AreaData[]>([])
const curArea = reactive({
    areaId: route.query.areaId,
    areaName: route.query.areaName,
    areaL2Name: String(route.query.areaL2Name),
    areaNameL2: String(route.query.areaNameL2),
})



const title = computed(() => {
    return '南京分公司' + curArea.areaName + '区登录情况'
})

function GO(row: any) {
    router.push({
        path: '/loginRateAreaL2Staff', query: {
            areaId: curArea.areaId,
            areaName: curArea.areaName,
            areaL2Id: row.areaIdL2,
            areaL2Name: row.areaNameL2
        }

    })
}



//获取今日登录列表
async function loadLoginDetail() {
    const res: any = await getLoginDetailAPI({ areaId: curArea.areaId })
    if (res.code == 200) {
        tabList.value = res.data
    }
}


function changeActionType(params: any) {
    curArea.areaId = params.value
    curArea.areaName = params.text
    loadLoginDetail()
}
onMounted(() => {
    loadLoginDetail()
})

</script>

<style lang="scss" scoped src="./index.scss"></style>
<style lang="scss" scoped>
.card-header1 {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 700;
        font-size: 16px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

}

.selectOption-item {
    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
    font-weight: 400;
    font-size: 14px;
    color: #1A76FF;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .play {
        transform: rotate(90deg);
        margin-left: 3px;
        position: absolute;
        right: 10px;
    }
}

.selectOption-item:active {
    background: #d8dee6;
}

:deep(.van-popover__wrapper) {
    flex: 1 0 auto;
}

:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}
</style>