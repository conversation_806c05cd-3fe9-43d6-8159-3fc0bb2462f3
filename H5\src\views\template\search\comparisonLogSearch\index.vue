<template>
    <div class="box">
        <div class="box_top">
            <div class="Branch_name">
                {{ curTitle }}
            </div>
            <div class="history">
                <router-link to="/comparativeCalculationAudit">
                    <van-button type="primary" size="small">比算审核</van-button>
                </router-link>

            </div>
        </div>
        <div class="selectOptionList">
            <div @click="changeSelete(item.value)" class="item" v-for="item in selectOptionList" :key="item.value"
                :class="item.value == curSelect ? 'activeItem' : ''">
                {{ item.text }}
            </div>
        </div>
        <div class="selectTime" @click="selectTime">
            <div>
                <div class="startTime">起始日期</div>
                <div class="timeValue" :class="formline.startTime ? 'activeTime' : ''">{{ formline.startTime || '选择日期'
                    }}
                </div>
            </div>
            <div>
                <img src="@/assets/arrows_right.svg" alt="" srcset="">
            </div>
            <div>
                <div class="startTime">截止日期</div>
                <div class="timeValue" :class="formline.endTime ? 'activeTime' : ''">{{ formline.endTime || '选择日期' }}
                </div>
            </div>
        </div>

        <van-search class="search" background="#FBFCFF" v-model="searchName" @click="popoverIsShow = true" readonly
            placeholder="请选择查询对象">
        </van-search>



        <staffList v-model:searchStaffName="searchName" v-model:popoverIsShow="popoverIsShow"
            @confirmStaffCode="confirmStaffCode"></staffList>

        <van-button type="primary" @click="viewComparativeCalculationDetail" size="large" class="searchButtomn"
            color="linear-gradient( 270deg, #73ACFF 0%, #237CFF 100%)" block>查询</van-button>

        <Teleport to="body">
            <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
                switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
                @confirm="onConfirm" />
        </Teleport>

    </div>
</template>

<script setup lang="ts">
import staffList from "@/components/staffList/index.vue";
import { useRouter } from "vue-router";
import { searchHooks } from "@/hooks_modules/changeTimeHooks.ts";
import { useUserInfo } from "@/stores/userInfo";
const userInfoStore = useUserInfo()
const curTitle = computed(() => {
    return userInfoStore.userInfo.orgName
})

const router = useRouter()

function viewComparativeCalculationDetail() {
    router.push({
        path: '/comparativeCalculationDetail', query: {
            ...formline,
            staffCode: formline.staffCode[0],
        }
    })
}

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    curSelect,
    selectOptionList,
    searchName,
    popoverIsShow,
    changeSelete,
    selectTime,
    onConfirm,
    confirmStaffCode,

} = searchHooks()

onMounted(() => {
})

</script>

<style lang="scss" src="../index.scss" scoped></style>