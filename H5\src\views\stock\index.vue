<template>
    <div class="box container_">

        <!-- <div class="box_top">
            <div class="Branch_name">
                {{ showOrgName }}
            </div>

        </div> -->
        <div class="selectOptionList">
            <div @click="changeSelete(item.value)" class="item" v-for="item in selectOptionList" :key="item.value"
                :class="item.value == curSelect ? 'activeItem' : ''">
                {{ item.text }}
            </div>
        </div>
        <div class="selectTime pb-20" @click="selectTime">
            <div>
                <div class="startTime">起始日期</div>
                <div class="timeValue" :class="formline.startTime ? 'activeTime' : ''">{{ formline.startTime || '选择日期'
                    }}
                </div>
            </div>
            <div>
                <img src="@/assets/arrows_right.svg" alt="" srcset="">
            </div>
            <div>
                <div class="startTime">截止日期</div>
                <div class="timeValue" :class="formline.endTime ? 'activeTime' : ''">{{ formline.endTime || '选择日期' }}
                </div>
            </div>
        </div>

        <div class="container__header" style="border-bottom: 0px;">
            <div>
                <van-popover placement="right-start">
                    <div class="popoverBody">
                        <li>派单总量：所有框架下的所有单量</li>
                        <li>已执行量：外呼时长大于0的数量</li>
                        <li>外呼量：外呼时长大于30秒的数量</li>
                        <li>待处理量：派单量-已执行量</li>
                        <li>以上数据均来自智慧营销，最终口径以业务为准</li>

                    </div>
                    <template #reference>
                        <img src="@/assets/sendOrdersInfo.svg" alt="" srcset="">
                        <span class="ml-6" style="font-weight: 500;">派单情况</span>
                        <van-icon class="ml-3" name="question" color="#1A76FF" />
                    </template>
                </van-popover>


            </div>

            <!-- <router-link :to="{
                path: '/sendOrdersZoneContact',
                query: {
                    startTime: formline.startTime,
                    endTime: formline.endTime
                }
            }">
                <div class="zoneContact van-haptics-feedback">
                    战区触点
                </div>
            </router-link>

            <div class="viewDetail" @click="">
                <router-link class="van-haptics-feedback" :to="{
                    path: '/sendOrdersDetailLead',
                    query: {
                        startTime: formline.startTime,
                        endTime: formline.endTime
                    }
                }">
                    查看重点派单 <van-icon name="arrow" />

                </router-link>
            </div> -->
        </div>
        <div class="container_body ">
            <myEcharts class="myEcharts" :options="options"></myEcharts>
            <router-link :to="{
                path: '/stockList',
                query: {
                    startTime: formline.startTime,
                    endTime: formline.endTime
                }
            }">
                <div class="optionList">
                    <div class="item" v-for="item in optionList" :key="item.text">
                        <div class="value" :style="{ color: item.color }">{{ item.value }}</div>
                        <div class="text">{{ item.text }}</div>

                    </div>
                </div>
            </router-link>
        </div>




        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />
    </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { searchHooks } from "@/hooks_modules/changeTimeHooks.ts";
import myEcharts from "@/components/charts/myCharts.vue";
import { getSendOrderListAPI } from "@/api/home";
import { useUserInfo } from "@/stores/userInfo";
import { sendOrdersHooks } from "@/views/sendOrders/hooks/sendOrdersHooks";



const {
    showOrgName,
    options,
    optionList,
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    curSelect,
    selectOptionList,
    changeSelete,
    selectTime,
    onConfirm
} = sendOrdersHooks()




</script>

<style lang="scss" src="@/views/template/search/index.scss" scoped></style>
<style lang="scss" scoped>
.viewDetail {
    font-weight: 500;
    font-size: 12px;
    color: #1A76FF;
    line-height: 13px;
}

.zoneContact {
    font-weight: 500;
    font-size: 14px;
    color: #3D3D3D;
    line-height: 16px;
}

.optionList {

    background: #EDF5FF;
    border-radius: 5px;
    // display: grid;
    // grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    display: flex;
    // justify-content: space-around;
    flex-wrap: wrap;
    // gap: 10px;
    margin-top: 50px;
    margin-bottom: 50px;

    .item {
        flex: 1 0 33.3333%;
        max-width: 33.33333%;
        display: flex;
        height: 100%;
        padding: 15px 5px;
        flex-direction: column;
        place-content: center;
        text-align: center;

        .text {
            font-weight: 500;
            font-size: 12px;
            color: #3D3D3D;
        }

        .value {
            font-family: 'DIN, DIN';
            font-weight: 700;
            font-size: 23px;
            color: #6282FD;
            margin-bottom: 7px;
        }
    }
}

.myEcharts {
    width: 100%;
    height: 250px;
}

.popoverBody {
    font-size: 13px;
    padding: 5px 10px;
}
</style>