<template>
    <div class='card container_'>
        <div class="container__header">
            <div>
                <img src="./assets/communityBattle.png" alt="" srcset="">
                <span class="ml-6">{{ route.query.gridTypeName }}</span>
            </div>

        </div>





        <div class="table-container">




            <table>
                <thead>

                    <tr>
                        <th class="sticky">网格ID<br>网格名</th>
                        <th>网格属性</th>
                        <th>图片数量</th>
                        <th>宣传评价</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>


                    <tr v-for="(row, index) in tableData" :key="row.gridId">
                        <td class="sticky">
                            <div class="store ">
                                <div class=" fs8" style="font-style: italic;">{{ row.gridId }}</div>
                                <div class="mt-1">{{ row.gridName }}</div>

                            </div>
                        </td>
                        <td class="text-primary">
                            <div>{{ row.gridProperty }}</div>
                        </td>

                        <td v-imgPreview="[row.images, 0]">{{ row.images.length }}</td>

                        <td @click="evaluate(row)">
                            <div class="py-5 px-1 fs12" style="border-radius: 5px;"
                                :class="getItemClass(row.publicityEvaluation)">
                                {{ curRowPublicityEvaluation(row.publicityEvaluation) ?? '未评价' }}
                            </div>
            
                        </td>
                        <td>
                            <div>
                                <div v-if="row.remark" class="store">
                                    {{ row.remark }}
                                </div>
                                <div class="active-col-item" v-else @click="setRemark(row)">备注</div>
                            </div>

                        </td>

                    </tr>


                </tbody>
            </table>
            <van-empty description="暂无数据" class="mt-8" style="background-color: #fff;border-radius: 15px;"
                v-if="tableData.length == 0" />










        </div>








        <van-dialog v-model:show="dialogIsShow" title="宣传评价" show-cancel-button @confirm="confirm">
            <div class="container_body dictList" v-if="dialogType == DialogType.evaluate">
                <div class="dictItem"
                    :class="[curSelectGrid?.publicityEvaluation == item.value ? getItemClass(item.value) : '',]"
                    v-for="item in tabList" :key="item.value" @click="changeTab(item)">{{ item.text }}</div>

            </div>

            <van-field v-if="dialogType == DialogType.remark" v-model="curSelectGrid.remark" rows="2" label-width="45"
                autosize label="备注" type="textarea" maxlength="100" placeholder="请输入备注" show-word-limit />
        </van-dialog>
    </div>
</template>

<script setup lang="ts">
import { getDictInfoAPI } from "@/api/common";
import { getGridTypeDetailAPI, updateGridTypeDetail } from "@/api/promoShots";
import { useRoute } from "vue-router";
import { showFailToast, showSuccessToast } from "vant";
enum DialogType {
    remark = '备注',
    evaluate = '评价'
}

interface RowItem {
    span: number;
    name: string;
    props: string;
    default: any;
    [key: string]: any
}

type MyObject = {
    gridId: string;
    gridName: number;
    gridTypeName: number;
    images: string[];
    photoCount: number
    publicityEvaluation: string
    remark: string
    [key: string]: any; // 允许使用任意的字符串键
};
const route = useRoute()
const dialogIsShow = ref(false)

const tableData = ref<MyObject[]>([])






let gridTypeDetailList: MyObject[] = []
function loadGridTypeDetail() {
    getGridTypeDetailAPI({ gridTypeName: route.query.gridTypeName }).then((res: any) => {
        if (res.code == 200) {

        
            gridTypeDetailList = res.data
            const groupedData = res.data.reduce((acc: any, item: any) => {
                // 如果 accumulator 中没有该 gridId，初始化一个新的对象
                if (!acc[item.gridId]) {
                    acc[item.gridId] = {
                        ...item,
                        images: item.images ? item.images.split(',') : [],
                    };
                } else {
                    // 如果该 gridId 已经存在，则合并图片并更新照片数量
                    const existingItem = acc[item.gridId];
                    existingItem.images = [...existingItem.images, ...item.images.split(',')];
                }

                return acc;
            }, {});

            tableData.value = Object.values(groupedData);
            tableData.value.sort((a: MyObject, b: MyObject) => {
                return b.images.length - a.images.length
            })
        }
    })
}


let curSelectGrid = reactive<Partial<MyObject>>({
    publicityEvaluation: '',
    remark: "",
    gridId: ''
})
const dialogType = ref('')
//添加备注
function setRemark(row: MyObject) {
    curSelectGrid.publicityEvaluation = row.publicityEvaluation
    curSelectGrid.remark = row.remark
    curSelectGrid.gridId = row.gridId

    dialogType.value = DialogType.remark
    dialogIsShow.value = true

}




const curRowPublicityEvaluation = (publicityEvaluation: string) => {
    return tabList.value.find(item => item.value == publicityEvaluation)?.text
}

async function initDictInfo() {
    await getDictInfoAPI('promotion_evaluation').then((res: any) => {
        if (res.code == 200) {
            tabList.value = res.data.map((item: any) => ({ text: item.dictName, value: item.dictCode }))
        }

    })
}

const tabList = ref<{ text: string, value: string }[]>([])

function changeTab(row: any) {
    curSelectGrid.publicityEvaluation = row.value

}


function getItemClass(value: string) {
    switch (value) {
        case '1':
            return 'active-green '
        case '2':
            return 'active-light-green'
        case '3':
            return 'active-yellow'
        case '4':
            return 'active-red'
        case '5':
            return 'active-blue'
        default:
            return 'active-default'
    }
}


function evaluate(row: MyObject) {
    curSelectGrid.publicityEvaluation = row.publicityEvaluation
    curSelectGrid.remark = row.remark
    curSelectGrid.gridId = row.gridId

    dialogType.value = DialogType.evaluate
    dialogIsShow.value = true
}

function confirm() {
    let params = {
        gridDetail: gridTypeDetailList.filter(item => item.gridId == curSelectGrid.gridId).map((item: any) => ({ ...item, publicityEvaluation: curSelectGrid.publicityEvaluation, remark: curSelectGrid.remark }))
    }

    updateGridTypeDetail(params).then((res: any) => {
        if (res.code == 200) {
            showSuccessToast('更新成功')
            loadGridTypeDetail()

        } else {
            showFailToast('更新失败')
        }


    }).catch()


}

onMounted(() => {
    loadGridTypeDetail()
    initDictInfo()
})
</script>

<style lang="scss" scoped>
.card {
    min-height: 100%;
    background: #fff;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px;
}





.table-container {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 8px;

    table {
        width: 100%;
        border-collapse: collapse;
        text-align: center;
        font-size: 13px;

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            white-space: nowrap; // 禁止换行
            // white-space: normal;
            /* 允许换行 */
        }

        .sticky {
            /* 固定表格内容的门店列 */
            position: sticky;
            left: 0;
            background-color: #fff;
            /* 设置背景颜色以覆盖其他列 */
            z-index: 1;
            /* 确保门店列在最上层 */
        }


        .store {
            width: 120px;
            max-width: 120px;
            white-space: normal;
        }

        .detail {
            color: #1A76FF;
            text-decoration: underline;
            text-decoration-thickness: 1px;
            /* 下划线的厚度 */
            text-underline-offset: 2px;
            /* 下划线与文字的距离 */
        }

        .main-title {
            font-size: 13px;
            font-weight: bold;
            padding: 12px;
            background-color: #2e70ba;
            color: #fff;
            text-align: center;
        }

        thead tr:nth-child(1) th {
            background-color: #2e70ba;
            color: #fff;
            font-weight: bold;
            padding: 10px;
            text-align: center;
        }

        thead tr:nth-child(2) th {
            background-color: #2e70ba;
            color: #fff;
            font-weight: bold;
            padding: 8px;
        }

        tbody tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        tbody tr:hover {
            background-color: #e6f7ff;

            .sticky {
                background-color: #e6f7ff !important;

            }
        }

        tbody td.highlight {
            background-color: #ffe58f;
            font-weight: bold;

        }

    }
}








/* 选中状态颜色定义 */
.active-green {
    background-color: #4caf50;
    color: #ffffff;
    border-color: #388e3c;
}

.active-light-green {
    background-color: #91cc75b5;
    color: #1b5e20;
    border-color: #81c784;
}

.active-yellow {
    background-color: #FAC857;
    color: #ffffff;
    border-color: #fbc02d;
}

.active-red {
    background-color: #EE6666;
    color: #ffffff;
    border-color: #d32f2f;
}

.active-blue {
    background-color: #2196f3;
    color: #ffffff;
    border-color: #1976d2;
}

.active-default {
    background-color: #a8a8a8;
    color: #fbfcff;
    border-color: #8f8f8f;
}
</style>