<template>
    <div class="card">

        <headlinesHeader :actionType="actionType" :tabList="tabList" v-model:curSelectTab="curSelectType"
            v-model:curSelectActionType="curSelectActionType" @changeSelectActionType="changeSelectActionType"
            @changeSelectTab=changeSelectTab>
        </headlinesHeader>
        <charts :options="option" :title="`南京分公司${curSelectType == 2 ? '全岗' : '全区'}积分${curSelectTypeText}分布`"></charts>
        <List :title="`南京分公司各${curSelectType == 2 ? '条线' : '区域'}积分完成情况`" @GO="GO" :is-show-overview-card="true"
            :tabList="tabData"></List>
    </div>
</template>

<script setup lang="ts">
import headlinesHeader from "@/views/Headlines/modules_/header.vue";
import charts from "@/views/Headlines/modules_/charts.vue";
import List from "@/views/Headlines/modules_/list.vue";
import { getHeadlinesLineEchartsAPI, getHeadlinesbyRoleCountListAPI, getHeadlinesbyAreaCountListAPI } from "@/api/home";
import { useRoute, useRouter } from "vue-router";
import { mapSort } from "@/utils/mapSort";
interface LineTableData {
    count: number
    name: string
    rate: number
    type: number
    [key: string]: any
}
const route = useRoute()
const router = useRouter()
const tabList = ref([
    { text: '条线', value: 2 },
    { text: '区域', value: 0 },
])

const actionType = ref([
    { text: '预警', value: 0 },
    { text: '良好', value: 1 },
    { text: '优秀', value: 2 },

])
const curSelectType = ref(2) //当前选中的区域 条线 等类型
const curSelectActionType = ref(Number(route.query.type) || 0)  //当前选中的积分类型  预警 良好 优秀


function changeSelectActionType(item: any) {
    loadHeadlinesLineEcharts()
}
function changeSelectTab(item: any) {
    loadHeadlinesLineEcharts()
}





const option = computed(() => {
    return {
        legend: {
            show: false,
        },

        tooltip: {
            trigger: "axis",
            backgroundColor: "rgba(0, 0, 0, 0.63)", //设置背景颜色
            textStyle: {
                fontFamily: "proud",
                color: "#fff",
            },
            borderColor: "rgba(255,255,255, .5)",
            formatter: function (params: any) {

                params = params[0]
                console.log(params);
                return `<span style="color:${params.color}">${params.marker}${params.name}: ${params.value}%</span>`;
            },
        },

        grid: {
            borderColor: "#ECF0F3",
            borderWidth: 1,
            top: "10%",
            left: "0%",
            right: "6%",
            bottom: "-3%",
            containLabel: true,
        },
        xAxis: {
            type: "category",
            data: lineTableData.value.map(item => item.name),
            boundaryGap: false,
            axisLine: {
                show: true,
                lineStyle: {
                    color: "#71A9FF", // 设置 x 轴轴线颜色
                },
            },
            axisTick: {
                show: false,
            },
            axisLabel: {
                color: "#3D3D3D",
                fontSize: 12,
                fontFamily: "Source Han Sans-Regula",
                rotate: 45, // 设置刻度标签的旋转角度，以度为单位
            },
        },
        yAxis: {
            type: "value",
            minInterval: 1,
            axisLine: {
                show: false,

            },
            axisTick: {
                show: false,
            },
            axisLabel: {
                color: "#71A9FF",
                fontSize: 12,
                fontWeight: 500,
                fontFamily: "proud",
                formatter: function (value: any) {
                    return value === 0 ? value : `${value}%`;
                },
                margin: 15, // 调整轴线和刻度标签的距离
            },
            splitLine: {
                show: true,
                lineStyle: {
                    type: "dashed",
                    color: "#ECF0F3",
                },
            },
        },
        series: [
            {
                name: "积分",
                data: lineTableData.value.map(item => item.rate),
                type: "line",
                smooth: true,
                symbolSize: 6,
                areaStyle: {
                    opacity: 0.3,
                    color: {
                        type: "linear",
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: "#679FFF", // 渐变起始颜色
                            },
                            {
                                offset: 1,
                                color: "#F0F2F5", // 渐变结束颜色
                            },
                        ],
                    },
                },
                itemStyle: {
                    color: "#1A76FF",
                    borderColor: "#1A76FF", // 设置边框颜色为蓝色，与圆心一致
                    borderWidth: 2, // 可选：设置边框宽度，确保填充效果明显
                },
                lineStyle: {
                    color: "#6993FF",
                    width: 4,
                },
                label: {
                    show: true,
                    fontSize: 10,
                    color: "#3D3D3D",
                    formatter: '{c}', // 曲线上的数值标签添加百分号
                },
            },
        ],
    };
})


const lineTableData = ref<LineTableData[]>([])
function loadHeadlinesLineEcharts() {
    let params = {
        type: curSelectType.value,
        selectedPosition: curSelectActionType.value,
        startTime: route.query.startTime ? route.query.startTime : undefined,
        endTime: route.query.endTime ? route.query.endTime : undefined,
    }
    getHeadlinesLineEchartsAPI(params).then((res: any) => {
        if (res.code == 200) {
            // const name = curSelectType.value == 2 ? 'roleName' : 'name'
            res.data = mapSort(res.data)
            lineTableData.value = res.data
        }


    })
}


function GO(params: any) {
    router.push({
        path: '/headlinesList', query: {
            roleID: params.roleCode,
            areaId: params.areaId,
            type: params.roleCode ? 0 : 1,  // 区域
            startTime: route.query.startTime ? route.query.startTime : '',
            endTime: route.query.endTime ? route.query.endTime : '',

        }
    })
}




watch(() => curSelectType.value, (value) => {
    if (value == 2) {
        loadHeadlinesbyRoleCountList()
    } else {
        loadHeadlinesbyAreaCountListAPI()
    }
    console.log(value);
}, { immediate: true })




const tabData = ref([])
//按条线查询列表
function loadHeadlinesbyRoleCountList() {

    getHeadlinesbyRoleCountListAPI({
        startTime: route.query.startTime ? route.query.startTime : undefined,
        endTime: route.query.endTime ? route.query.endTime : undefined,
    }).then((res: any) => {
        if (res.code == 200) {
            const name = curSelectType.value == 2 ? 'roleName' : 'name'
            res.data = mapSort(res.data, name)
            tabData.value = res.data || []
        }

    })

}

//按区域查询列表
function loadHeadlinesbyAreaCountListAPI() {
    let params = {
        type: 0,
        startTime: route.query.startTime ? route.query.startTime : undefined,
        endTime: route.query.endTime ? route.query.endTime : undefined,
    }
    getHeadlinesbyAreaCountListAPI(params).then((res: any) => {
        if (res.code == 200) {

            res.data = mapSort(res.data)
            tabData.value = res.data || []
        }

    })

}

const curSelectTypeText = computed(() => {
    return actionType.value.find((item: any) => item.value == curSelectActionType.value)?.text
})
onMounted(() => {
    loadHeadlinesLineEcharts()
})



</script>

<style lang="scss" scoped></style>