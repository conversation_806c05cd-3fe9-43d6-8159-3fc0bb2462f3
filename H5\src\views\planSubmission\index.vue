<template>
    <div class="container_ ">
        <div class="card ">
            <div class="container__header">
                <div>
                    <img src="./assets/planSubmission.svg" alt="" srcset="" class="card_icon" />
                    <span class="ml-6">方案递单</span>
                </div>
            </div>
            <div class="container_body dictList">
                <div class="dictItem" :class="curSelectType.value == item.value ? 'activeItem' : ''"
                    v-for="item in tabList" :key="item.value" @click="changeTab(item)">{{ item.text }}</div>

            </div>
            <div class="container_body" style="padding: 0px 0px;">

                <van-form required="auto" ref="formField">

                    <van-field v-model="formLine_.customerName" name="number" label="客户名称" placeholder="请输入客户名称"
                        label-align="left" input-align="right" :rules="[{ required: true, message: '请填写客户名称' }]">

                    </van-field>
                    <van-field v-model="formLine_.customerTel" name="number" label="联系方式" placeholder="请输入联系方式"
                        label-align="left" input-align="right" type="digit"
                        :rules="[{ required: true, message: '请填写正确联系方式', pattern: /^(1[3-9]\d{9}|(\d{3,4}-)?\d{6,8})$/ }]">
                    </van-field>
                    <van-field v-model="formLine_.diffNetText" label="异网属性" placeholder="请选择异网属性" center
                        :rules="[{ required: true, message: '请选择异网属性' }]">
                        <template #input>
                            <div style="width: 100%;text-align: right;">
                                <van-popover
                                    :actions="[{ text: '移动', value: '移动' }, { text: '联通', value: '联通' }, { text: '广电', value: '广电' }]"
                                    @select="(params) => { formLine_.diffNetText = params.value }" placement="left">
                                    <template #reference>
                                        <div class="selectOption-item">
                                            <span style="height: 17px;line-height: 17px;">{{
                                                formLine_.diffNetText }}</span>
                                            <van-icon name="play" class="play" color="#1A76FF" />
                                        </div>
                                    </template>
                                </van-popover>
                            </div>
                        </template>
                    </van-field>
                    <van-field v-model="formLine_.diffNetText" label="重要性" placeholder="请选择重要性" center
                        :rules="[{ required: true, message: '请选择重要性' }]">
                        <template #input>
                            <div style="width: 100%;text-align: right;">
                                <van-popover
                                    :actions="[{ text: '普通跟踪', value: '普通跟踪' }, { text: '重点跟踪', value: '重点跟踪' }, { text: '有望签约', value: '有望签约' }]"
                                    @select="(params) => { formLine_.importantText = params.value }" placement="bottom">
                                    <template #reference>
                                        <div class="selectOption-item">
                                            <span style="height: 17px;line-height: 17px;">{{
                                                formLine_.importantText }}</span>
                                            <van-icon name="play" class="play" color="#1A76FF" />
                                        </div>
                                    </template>
                                </van-popover>
                            </div>
                        </template>
                    </van-field>
                    <van-field v-model.trim="formData.description"
                        :rules="[{ required: formLine.images.length > 0 ? false : true, message: '请输入方案描述' }]"
                        label-align="left" input-align="right" autosize label="方案描述" type="textarea" rows="2"
                        maxlength="1000" show-word-limit placeholder="请输入方案描述" />

                    <van-field name="fileList" label="上传照片" label-align="left" input-align="right"
                        :rules="[{ required: formData.description?.trim() ? false : true, message: '请拍摄照片' }]">
                        <template #input>
                            <div class="right_align">
                                <van-uploader :after-read="handleAfterRead" :before-read="handleBeforeRead"
                                    v-model="formData.fileList" preview-size="89" :before-delete="deleteFile">
                                    <template #default>
                                        <div class="form_card_camera">
                                            <van-image width="28px" height="23px" :src="camera" />
                                        </div>
                                    </template>
                                </van-uploader>
                            </div>
                        </template>
                    </van-field>
                </van-form>
            </div>

        </div>

        <div class="card  mt-12">
            <div class="container__header">
                <div>
                    <img src="./assets/history.png" alt="" srcset="" class="card_icon" />
                    <span class="ml-6">历史方案记录</span>
                </div>
                <div class="fs12" style="color:#2079FF" @click="goToDetail">查看详情 <van-icon name="arrow"
                        color="#2079FF" />
                </div>
            </div>
            <div class="container_body" style="padding: 0px;">
                <div class="solution-card" v-for="solution in solutions" :key="solution.id">
                    <div class="company-name">
                        <span class="name van-multi-ellipsis--l2"> {{ solution.customerName }}</span>
                        <span class="date">{{ solution.createTime }}</span>
                    </div>
                    <div class="solution-title">{{ solution.proposalType }}</div>
                    <div class="solution-description">{{ solution.proposalInfo }}</div>
                </div>
                <van-empty description="暂无数据" v-if="solutions.length == 0" />

            </div>


        </div>
        <div class="sure_btn">
            <van-button type="primary" color=" linear-gradient( 90deg, #73ACFF 0%, #237CFF 100%)" block
                @click="submit">提交方案</van-button>
        </div>
    </div>



</template>

<script setup lang="ts">
interface Solution {
    id: number;
    customerName: string;
    proposalType: string
    createTime: string;

    proposalInfo: string;
}
import { getDictInfoAPI } from "@/api/common";
import { proposalSaveAPI, getProposalListAPI } from "@/api/planSubmission";
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast, showSuccessToast, showFailToast } from "vant";
import camera from "@/views/template/MyDay/assets/images/camera.png";
import { uploadFileHooks } from "@/hooks_modules/upload_hooks";
import { debounce } from "@/utils/debounce";
const router = useRouter();
let formField = ref();




const {
    handleAfterRead,
    handleBeforeRead,
    deleteFile,
    formLine,
    formData,
} = uploadFileHooks()

const formLine_ = reactive({
    customerName: '',
    customerTel: '',
    diffNetText: "广电",
    importantText: '普通跟踪',
})


const submit = debounce(() => {
    formField.value
        .validate()
        .then(() => {
            let params = {
                images: formLine.images.join(','),
                proposalInfo: formData.description,
                customerName: formLine_.customerName,
                customerTel: formLine_.customerTel,
                proposalTypeCode: curSelectType.value,
                proposalType: curSelectType.text
            }
            proposalSaveAPI(params).then((res: any) => {
                if (res.code == 200) {
                    showSuccessToast("提交成功");
                    setTimeout(() => {
                        router.push({
                            path: '/solutionRecord',
                            query: { value: curSelectType.value, text: curSelectType.text }
                        });
                    }, 1500);
                }
            })







        })
        .catch((err: any) => {
            showFailToast("请完善信息");
        });
}, 300, true)


function goToDetail() {
    router.push({
        path: '/solutionRecord',
        query: { value: curSelectType.value, text: curSelectType.text }
    });
}

const solutions = ref<Solution[]>([])


const tabList = ref<{ text: string, value: string }[]>([])
const curSelectType = reactive<{ text: string, value: string }>({
    text: '递交专线方案',
    value: '1'
})

function changeTab(item: any) {
    curSelectType.value = item.value
    curSelectType.text = item.text
    loadList()
}
function loadList() {
    let params = {
        proposalTypeCode: curSelectType.value,
        pageNum: 1,
        pageSize: 3,
    }
    getProposalListAPI(params).then((res: any) => {
        if (res.code == 200) {
            solutions.value = res.data.records
        }
    })
}

async function initDictInfo() {
    await getDictInfoAPI('planSubmission').then((res: any) => {
        if (res.code == 200) {
            tabList.value = res.data.map((item: any) => ({ text: item.dictName, value: item.dictCode }))
            curSelectType.value = tabList.value[0].value ?? ''
            curSelectType.text = tabList.value[0].text ?? ''
        }

    })
}


onMounted(async () => {
    await initDictInfo()
    loadList()
})

</script>


<style lang="scss" scoped>
.container_ {
    background-color: transparent;
    margin-bottom: 80px;
}

.card {

    background: #fff;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px;
    padding-bottom: 20px;
}

.selectOption-item {
    flex: 1 0 auto;
    background: #EDF5FF;
    border-radius: 5px;
    font-weight: 400;
    font-size: 14px;
    color: #1A76FF;
    padding: 8px 25px 8px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .play {
        transform: rotate(90deg);
        margin-left: 3px;
        position: absolute;
        right: 10px;
    }

    &:active {
        background: #d8dee6;
    }
}

.form_card_camera {
    width: 89px;
    height: 89px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(103, 159, 255, 0.15);
}

.sure_btn {
    width: calc(100% - 18px - 18px);

    position: absolute;
    left: 50%;
    bottom: 100px;
    transform: translateX(-50%);
}

.sign_list {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 12px;
    color: #1a76ff;
    line-height: 13px;
    font-style: normal;
    text-transform: none;
}

.right_align {
    text-align: right;

    ::v-deep .van-uploader__wrapper {
        justify-content: flex-end !important;
    }
}

.card_icon {
    object-fit: contain;
}








.solution-card {
    border-bottom: 1px solid #EDF5FF;
    padding: 11px 15px 15px;
    margin-bottom: 10px;

}

.company-name {
    font-weight: 700;
    font-size: 14px;
    color: #3D3D3D;
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.date {
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    flex: 0 0 auto;
}

.solution-title {
    font-weight: 500;
    font-size: 12px;
    color: #3D3D3D;
    margin: 7px 0;
}

.solution-description {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 13px;
}
</style>
