# 任务中心H5项目

本项目是任务中心的H5端实现，包含以下功能：

1. DMC存储相关功能
   - 支付DMC代币获取存储空间
   - 部署存储合约
   - 其他存储相关功能

![design](./readmePicture1.jpg)
![design](./readmePicture2.jpg)

## 项目进度

### DES3工具类TypeScript转换 (2024-12-19)

- 将JavaScript版本的DES3加密工具转换为TypeScript
- 添加了类型声明和接口定义
- 修复了编译错误，包括：
  - 负数位移操作问题（将 `right >>> -16` 修改为 `right << 16`）
  - 循环结构问题（修复了if-else嵌套和循环逻辑）
  - 正向和反向循环的处理逻辑
- 添加了详细的JSDoc注释
- 使用了现代TypeScript语法（const/let替代var）
- 创建了测试文件验证功能

## 如何运行

1. 安装依赖：`pnpm install`
2. 开发模式：`npm run dev`
3. 构建项目：`npm run build`

## 技术栈

- TypeScript
- Vue 3
- Vite
- 其他相关技术

## 作者

周建豪
