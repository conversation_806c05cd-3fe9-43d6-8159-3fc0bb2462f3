<template>
    <div class="box">
        <div class="box_header">常用功能</div>
        <div class="box_body">
            <div class="item" v-for="item in componentOption" :key="item.menuCode" @click="navTo(item)">
                <img :src="item.icon_" alt="">
                <div class="mt-5">
                    <span>{{ item.menuName }}</span>
                </div>
            </div>
            <div class="item" @click="navTo({ menuCode: 'contractorHelper' })">
                <img src="./assets/contractorHelper.svg" alt="">
                <div class="mt-5">
                    <span>承包助手</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import componentList from "@/views/template/config";
import { useUserInfo } from "@/stores/userInfo";
import { EventsHooks, ComponentOptionProps } from "@/hooks_modules/Eventhooks";
import DES3 from "@/utils/DES3_.ts";
const { generateEventHandlers, emitEventsListGenerateEventHandlers, mergePropsAndVModelBindings } = EventsHooks()
const userInfoStore = useUserInfo();
const refsArray = ref<{ [key: string]: HTMLDivElement | null }>({});
const componentOption = computed(() => {
    let list = userInfoStore.getMenuList.filter((item: MenuItem) => item.menuCode === 'userData')[0]?.children || []
    let MyDaylist = list.filter((item: MenuItem) => item.menuCode === 'commonlyUsed')[0]?.children || []
    MyDaylist.forEach((item, index) => {
        item.icon_ = new URL(`./assets/${item.icon}.svg`, import.meta.url).href
        item.isLink = item.type == 3 ? true : false
    });
    return MyDaylist
})

console.log(componentOption);

const navTo = (item: any) => {
    if (item.isLink) {
        if (item.menuCode == 'servicePointsArea') {
            window.location.href = 'http://wechat.jlonline.com/qxb/sp-front'
        }
    } else {
        switch (item.menuCode) {
            case 'winnersList':
                userInfoStore.isShowLh = true
                break;
            case 'contractorHelper':
                const secretKey = "INbSvyvOXkSkcWNSc8HpHIa4";
                const paramDes = DES3.encrypt(secretKey, '15335170203');
                const paramEscape = encodeURIComponent(paramDes);
                window.location.href = `https://qwnjszzt.jlonline.com/web-service-front/?from=taskhub&code=${paramEscape}#/login`
                break;
            default:
                break;
        }
    }
};

</script>

<style lang="scss" scoped>
.box {
    font-family: 'Source Han Sans, Source Han Sans';

    .box_header {
        font-weight: 700;
        font-size: 18px;
        color: #3D3D3D;
    }

    .box_body {
        padding: 0px 10px;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
        gap: 10px;
    }

    .item {
        margin-top: 15px;
        margin-bottom: 5px;
        text-align: center;
        font-weight: 500;
        font-size: 12px;
        color: #3D3D3D;
        line-height: 13px;

        img {
            width: 38px;
            height: 38px;
        }
    }
}
</style>