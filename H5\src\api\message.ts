
import request from "../utils/request";
import configAxios from './config'

// 保存草稿
export function saveDraftAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/message/saveDraft`,
        method: "post",
        data,
    });
}

// 删除草稿
export function deleteDraftAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/message/deleteDraft`,
        method: "post",
        data,
    });
}

// 保存并发送
export function saveAndSendAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/message/saveAndSend`,
        method: "post",
        data,
    });
}

// 获取消息详情
export function getMessageDetailAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/message/detail`,
        method: "post",
        data,
    });
}

// 编辑草稿
export function updateDraftAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/message/updateDraft`,
        method: "post",
        data,
    });
}

// 消息分页列表
export function getMessageListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/message/myPage`,
        method: "post",
        data,
    });
}

// 按发件人汇总消息记录
export function getMessageSummaryBySenderAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/messageRecord/count`,
        method: "post",
        data,
    });
}

// 获取指定发件人消息列表
export function getMessageListBySenderAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/messageRecord/listBySender`,
        method: "post",
        data,
    });
}

// 读取消息
export function readMessageDetailAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/messageRecord/read`,
        method: "post",
        data,
    });
}
// 标记消息为已读
export function readMessageAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/messageRecord/readSysMsg`,
        method: "post",
        data,
    });
}
