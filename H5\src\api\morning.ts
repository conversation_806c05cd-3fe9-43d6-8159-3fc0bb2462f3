import request from "../utils/request";
import configAxios from './config'

//打卡保存
export function signSaveAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/sign/save`,
        method: "post",
        data,
    });
}

//打卡查询
export function getSignListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/sign/page`,
        method: "POST",
        data,
    });
}


export function getSignCountRoleAPI(data = {}) {
    return request({
        url: `${configAxios.taskHubServer}/manager/sign/count_role`,
        method: "POST",
        data,
    });
}

//晨会出征 过程管控home页面板
export function getSignCountAreaAPI(data = {}) {
    return request({
        url: `${configAxios.taskHubServer}/manager/sign/count_area`,
        method: "POST",
        data,
    });
}

//晨会出征 根据分局id查询当天是否打卡列表
export function getSignByAreaL2ListAPI(data = {}) {
    return request({
        url: `${configAxios.taskHubServer}/manager/sign/list`,
        method: "POST",
        data,
    });
}
