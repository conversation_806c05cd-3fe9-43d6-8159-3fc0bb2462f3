<template>
    <div class="container_">
        <!-- <div class="container__header">

        </div> -->

        <van-form @submit="onSubmit" required="auto" ref="formRef">
            <!-- 消息名称 -->
            <van-cell-group>
                <van-field label-align="top" v-model="formData.title" name="title" label="消息名称" placeholder="请输入消息名称"
                    :rules="[{ required: true, message: '请输入消息名称' }, { pattern: /^.{0,6}$/, message: '消息名称不得超过6个字', trigger: 'onChange' }]"
                    label-class="required-label" />
            </van-cell-group>

            <!-- 内容 -->
            <van-cell-group class="content-group">
                <van-field label-align="top" v-model="formData.content" name="content" label="内容" type="textarea"
                    placeholder="请输入消息内容" :rules="[{ required: true, message: '请输入消息内容' }]"
                    :autosize="{ minHeight: 100, maxHeight: 200 }" label-class="required-label" show-word-limit
                    maxlength="200" />
            </van-cell-group>

            <!-- 附件 -->
            <van-cell-group>
                <van-field name="附件" label="附件" label-align="top">
                    <template #input>
                        <van-uploader v-model="formData.attachments" accept="image/*," :max-count="3"
                            :max-size="2 * 1024 * 1024" @oversize="onOversize" :after-read="handleAfterRead"
                            :before-read="handleBeforeRead" upload-icon="plus" upload-text="">
                            <template #upload>
                                <div class="upload-btn">
                                    <van-icon name="plus" size="20" />
                                    <span>上传附件</span>
                                </div>
                            </template>
                        </van-uploader>
                    </template>
                </van-field>
                <div class="upload-tip">单个附件不超过2M，最多上传3个</div>
            </van-cell-group>

            <!-- 发送给谁 -->
            <van-cell-group>
                <van-field v-model="formData.receivers" label-align="top" is-link readonly name="receivers" label="发送给谁"
                    placeholder="请选择发送对象" @click="showReceiverPicker = true"
                    :rules="[{ required: true, message: '请选择发送对象' }]" label-class="required-label">

                </van-field>
            </van-cell-group>

            <!-- 发送时间 -->
            <van-cell-group>
                <van-field v-model="formData.sendTime" is-link readonly name="sendTime" label="发送时间"
                    @click="showTimePicker = true" :rules="[{ required: true, message: '请选择发送时间' }]"
                    label-class="required-label">
                    <template #right-icon>
                        <span class="send-now" @click.prevent.stop="setNowTime">立即发送</span>

                    </template>
                </van-field>
            </van-cell-group>

            <!-- 同步推送选项 -->
            <!-- <van-cell-group>
                <van-field name="syncOptions" label="企微同步推送" input-align="right" label-class="required-label">
                    <template #input>
                        <van-switch v-model="formData.wxPush" size="20" />
                    </template>
                </van-field>
            </van-cell-group>

            <van-cell-group>
                <van-field name="smsSyncOptions" label="短信同步推送" input-align="right" label-class="required-label">
                    <template #input>
                        <van-switch v-model="formData.smsPush" size="20" />
                    </template>
                </van-field>
            </van-cell-group> -->

            <!-- 底部按钮区域 -->
            <div class="bottom-buttons">
                <van-button plain type="primary" block @click="saveAsDraft">保存（草稿箱）</van-button>
                <van-button type="primary" block native-type="submit">发送</van-button>
            </div>
        </van-form>

        <!-- 接收人选择弹出层 -->
        <van-popup v-model:show="showReceiverPicker" position="right" :style="{ width: '100%', height: '100%' }">
            <div class="popup-header">
                <van-nav-bar title="选择发送对象" left-arrow @click-left="showReceiverPicker = false">
                    <template #right>
                        <span class="confirm-btn" @click="confirmReceivers">确定</span>
                    </template>
                </van-nav-bar>
            </div>
            <div class="popup-content">
                <van-search v-model="searchName" placeholder="请选择查询对象" class="searchUserName">

                    <template #left-icon>
                        <img class="mt-5" src="@/assets/search.svg" alt="" srcset="">

                    </template>
                </van-search>
                <div class="checkbox">
                    <van-checkbox v-model="isCheckAll" class="checkall" :indeterminate="isIndeterminate"
                        @change="checkAllChange">
                        全选
                    </van-checkbox>

                    <van-checkbox-group v-model="checkedUserList" @change="checkedResultChange">
                        <van-checkbox v-for="item in staffList" :key="item.staffCode" :name="item.staffCode">
                            <div class="checkbox-content">
                                <img src="@/assets/defaultAvatar.png" />
                                <div style="display: flex;flex-direction: column;justify-content: space-around;">
                                    <span>{{ item.staffName }}</span>
                                    <span>{{ item.staffCode }}</span>
                                </div>
                            </div>
                        </van-checkbox>
                    </van-checkbox-group>

                </div>
                <div>

                </div>

            </div>


            <div class="popup-bottom">
                <div class="popup-bottom-inner">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span style="color: #1A76FF;" @click="showSelectedUsers = true">已选: {{ checkedUserList?.length
                            || 0 }}
                            <van-icon name="arrow-up" class="ml-3" size="17" /></span>
                        <van-button type="primary" style="width: 160px;" @click="confirmReceivers">确定</van-button>
                    </div>

                </div>
            </div>

            <!-- 已选人员弹出层 -->
            <van-popup v-model:show="showSelectedUsers" position="bottom" round class="selected-users-popup"
                :style="{ width: '100%', height: '75%' }">
                <div class="selected-users-header">

                    <div class="title-text">已选</div>
                    <div class="title-subheading">
                        已选：用户（{{ checkedUserList?.length }}/{{ userInfoStore.staffList.length }})</div>

                </div>
                <div class="selected-users-content">
                    <div class="selected-users-list">
                        <div v-for="code in checkedUserList" :key="code" class="selected-user-item">
                            <div class="user-info">
                                <van-image width="36" height="36" round fit="cover"
                                    src="@/assets/defaultAvatar.png" />
                                <div class="user-detail">
                                    <span class="user-name">{{staffList.find(item => item.staffCode ===
                                        code)?.staffName || ''
                                        }}</span>
                                    <span class="user-code">{{ code }}</span>
                                </div>
                            </div>
                            <van-icon name="delete-o" size="20" class="remove-icon" @click="removeUser(code)" />
                        </div>
                    </div>
                </div>
            </van-popup>

        </van-popup>

        <!-- 时间选择弹出层 -->
        <van-popup v-model:show="showTimePicker" position="bottom" round>
            <van-picker-group v-model:active-tab="activeTimeTab" title="选择发送时间" :tabs="['选择日期', '选择时间']"
                @confirm="onTimeConfirm" @cancel="showTimePicker = false">
                <van-date-picker v-model="currentDate" :min-date="minDate" :max-date="maxDate" />
                <van-time-picker v-model="currentTime" :min-hour="minHour" :max-hour="maxHour"
                    :min-minute="minMinute" />
            </van-picker-group>
        </van-popup>

    </div>
</template>

<script setup lang='ts'>
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant';
import { useUserInfo } from "@/stores/userInfo";
import { saveDraftAPI, updateDraftAPI, saveAndSendAPI, getMessageDetailAPI } from '@/api/message';
import { debounce } from "@/utils/debounce";
import { formatDate } from "@/utils/loadTime";
import { uploadFileHooks } from "@/hooks_modules/upload_hooks";
const router = useRouter();
const route = useRoute()
const userInfoStore = useUserInfo()

const {
    handleAfterRead,
    handleBeforeRead,
    formLine
} = uploadFileHooks(
    { enableAddWatermarkToImage: false }
)


const staffList = computed(() => {
    const input = searchName.value.toLowerCase();
    const staffCodeSelf = userInfoStore.userInfo.staffCode
    const staffList_ = userInfoStore.staffList.filter(item => item.staffCode != staffCodeSelf)
    return staffList_.map(item => ({ staffCode: item.staffCode, staffName: item.staffName })).filter((item) => {
        const staffName = item.staffName ? item.staffName.toLowerCase() : '';
        const staffCode = item.staffCode ? item.staffCode.toLowerCase() : '';
        return (
            staffName.includes(input) ||
            staffCode.includes(input)
        );
    })
})

const searchName = ref('')
const checkedUserList = ref<string[]>([])  // 改为存储 staffCode 的数组
const isCheckAll = ref(false) // 是否全选
const isIndeterminate = ref(false) //是否为不确定状态

// 在文件上传组件定义处修改类型
interface Attachment {
    url: string;
    isImage: boolean;
}

// 修改表单数据定义
const formData = reactive({
    title: '',
    content: '',
    attachments: [] as Attachment[],
    receivers: '',
    sendTime: '立即发送',
    wxPush: false,
    smsPush: false
});

// 时间选择器数据
const showTimePicker = ref(false);
const activeTimeTab = ref(0);
const currentDate = ref(['2023', '12', '15']);
const currentTime = ref(['08', '00']);
const minDate = ref(new Date());
const maxDate = ref(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)); // 一月后
const minHour = ref(0);
const maxHour = ref(23);
const minMinute = ref(0);

// 接收人选择器数据
const showReceiverPicker = ref(false);
//已选人 弹窗
const showSelectedUsers = ref(false);


// 移除已选用户方法
const removeUser = (code: string): void => {
    if (checkedUserList.value && code) {
        checkedUserList.value = checkedUserList.value.filter(item => item !== code);
        checkedResultChange(checkedUserList.value);
    }
};

// 上传文件大小超出限制时触发
function onOversize(file: File): void {
    showFailToast('文件大小不能超过2M');
}

// 确认接收人选择
function confirmReceivers(): void {
    let text = ''
    const selectedUsers = staffList.value.filter(item => item.staffCode && checkedUserList.value.includes(item.staffCode))
    if (selectedUsers.length > 4) {
        text = selectedUsers.slice(0, 4).map(item => item.staffName).join('、') + `等${selectedUsers.length}人`
    } else {
        text = selectedUsers.map(item => item.staffName).join('、')
    }
    formData.receivers = text;
    showReceiverPicker.value = false;
}

// 设置立即发送
function setNowTime(event: Event): void {
    formData.sendTime = '立即发送';
    event?.stopPropagation();
}



// 确认选择时间
function onTimeConfirm(): void {
    const year = currentDate.value[0];
    const month = currentDate.value[1].padStart(2, '0');
    const day = currentDate.value[2].padStart(2, '0');
    const hour = currentTime.value[0].padStart(2, '0');
    const minute = currentTime.value[1].padStart(2, '0');

    formData.sendTime = `${year}-${month}-${day} ${hour}:${minute}`;
    showTimePicker.value = false;
}

const formRef = ref()

// 保存为草稿
const saveAsDraft = debounce(() => {

    formRef.value
        .validate()
        .then(() => {
            let params = {
                id: route.query.id,
                title: formData.title,
                content: formData.content,
                annexUrl: formLine.images.join(','),
                msgType: "1",
                timed: formData.sendTime == '立即发送' ? 0 : 1,
                sendTime: formData.sendTime == '立即发送' ? undefined : formatDate(formData.sendTime, 'yyyy-MM-dd hh:mm:ss'),
                staffCodes: checkedUserList.value

            }
            const API = route.query.id ? updateDraftAPI : saveDraftAPI
            API(params).then((res: any) => {
                if (res.code == 200) {
                    showToast('已保存至草稿箱');
                    router.push({
                        path: '/messageLead',
                        query: {
                            messageType: '发送消息',
                            seedMessageType: '草稿箱',
                        }
                    })
                }

            })


        })
        .catch((err: any) => {
            showFailToast("请完善信息");
        });

    console.log(1111);



}, 600, true)

// 表单提交
const onSubmit = debounce((): void => {

    formRef.value
        .validate()
        .then(() => {
            let params = {
                id: route.query.id,
                title: formData.title,
                content: formData.content,
                annexUrl: formLine.images.join(','),
                msgType: "1",
                timed: formData.sendTime == '立即发送' ? 0 : 1,
                sendTime: formData.sendTime == '立即发送' ? undefined : formatDate(formData.sendTime, 'yyyy-MM-dd hh:mm:ss'),
                staffCodes: checkedUserList.value

            }

            saveAndSendAPI(params).then((res: any) => {
                if (res.code == 200) {
                    showToast('已发送');
                    router.push({
                        path: '/messageLead',
                        query: {
                            messageType: '发送消息',
                            seedMessageType: formData.sendTime == '立即发送' ? '已发送' : '发送中',
                        }
                    })
                }

            })


        })
        .catch((err: any) => {
            showFailToast("请完善信息");
        });

    console.log(1111);



}, 600, true)



//选择人员 
const checkedResultChange = (value: string[]) => {
    const checkedCount = value.length
    isCheckAll.value = checkedCount === staffList.value.length
    isIndeterminate.value = checkedCount > 0 && checkedCount < staffList.value.length
}


//全选 全不选 
function checkAllChange(value: boolean) {
    checkedUserList.value = value ? staffList.value
        .filter(item => item.staffCode)
        .map(item => item.staffCode as string) : []
    isIndeterminate.value = false
}
// 获取草稿箱回显
function getMessageDetail() {
    getMessageDetailAPI({ id: route.query.id }).then((res: any) => {
        if (res.code == 200) {
            formData.title = res.data.title
            formData.content = res.data.content
            formData.sendTime = res.data.sendTime
            formLine.images = res.data.annexUrl ? res.data.annexUrl.split(',') : []
            // 设置文件上传组件的回显数据
            formData.attachments = formLine.images.map(url => ({
                url: url,
                isImage: true
            }))
            const now = new Date(res.data.sendTime);
            currentDate.value = [
                String(now.getFullYear()),
                String(now.getMonth() + 1),
                String(now.getDate())
            ];
            currentTime.value = [
                String(now.getHours()),
                String(now.getMinutes())
            ];
            checkedUserList.value = res.data.unReadUsers
                .map((item: any) => item.staffCode)
                .filter((code: string | undefined): code is string => code !== undefined)
            confirmReceivers()
        }
    })
}
watch(() => route.query.id, (newVal: any) => {
    if (newVal) {
        getMessageDetail()
    } else {
        // 初始化当前日期和时间
        const now = new Date();
        currentDate.value = [
            String(now.getFullYear()),
            String(now.getMonth() + 1),
            String(now.getDate())
        ];
        currentTime.value = [
            String(now.getHours()),
            String(now.getMinutes())
        ];
    }
}, { immediate: true })

// 生命周期钩子
onMounted(() => {

});
</script>

<style lang='scss' scoped>
.container_ {
    min-height: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 10px;
    overflow: hidden;
}

.container__header {
    flex-shrink: 0;
}

.container__body {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 20px;
}

.content-group {
    margin-top: 10px;
}

.required-label::before {
    content: '*';
    color: #ee0a24;
    margin-right: 4px;
}

.upload-tip {
    color: #999;
    font-size: 12px;
    padding: 5px 16px 10px;
}

.bottom-buttons {
    display: flex;
    margin: 20px 16px;
    gap: 10px;
}

.send-now {
    color: #1989fa;
    font-size: 14px;
    margin-right: 5px;
}

.upload-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    color: #1989fa;
    border: 1px solid #1989fa;
    border-radius: 4px;
    padding: 5px 10px;
}

.upload-btn span {
    margin-left: 5px;
}

.popup-header {
    position: sticky;
    top: 0;
    z-index: 1;
}

.confirm-btn {
    font-size: 14px;
    color: #1989fa;
}

:deep(.van-cell-group--) {
    margin: 10px 16px;
}

:deep(.van-button--block) {
    flex: 1;
}

$popup-bottom-inner-height: 65px;

.popup-content {
    padding: 5px;
    height: calc(100% - var(--van-nav-bar-height) - #{$popup-bottom-inner-height});
    overflow-y: auto;
    position: relative;

    .searchUserName {
        :deep(.van-search__action) {
            line-height: 0;
        }

        :deep(.van-search__content) {
            background-color: #EDF5FF;
            border-radius: 5px;
        }



        :deep(.van-icon-search) {
            color: #1a76ff;
        }

        :deep(.van-search__field) {
            height: 40px;
        }


        :deep(.van-field__value) {
            height: 36px;
            line-height: 36px;
            font-weight: 400;
            font-size: 14px;
            color: #72ABFF;
        }

        :deep(.van-field__control) {
            font-weight: 400;
            font-size: 14px;
            color: #72ABFF;
        }

        :deep(.van-field__control::placeholder) {
            color: #72ABFF;
        }
    }

    .checkbox {
        padding: 15px;
        padding-bottom: 0px;
        font-size: 12px;

        :deep(.van-checkbox:not(:last-child)) {
            border-bottom: 1px solid #EEF5FF;
            // border-top: 1px solid #EEF5FF;
        }

        .checkall {
            padding: 10px 0px;
            font-weight: 400;
            font-size: 12px;
            color: #333333;
            line-height: 22px;
        }

        .checkbox-content {
            margin-bottom: 5px;
            margin-top: 5px;
            display: grid;
            grid-template-columns: 45px auto;
            font-size: 12px;
            align-items: center;
            padding: 10px;
            padding-left: 5px;
            font-weight: 400;
            color: #333333;
            line-height: 22px;

            img {
                width: 40px;
                height: 40px;
                border-radius: 50%;
            }

        }


    }

}


.popup-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 999;
    border-top: 1px solid #EEF5FF;
    background-color: #fbfbfb;

    .popup-bottom-inner {
        background-color: #fbfbfb;
        line-height: #{$popup-bottom-inner-height};
        height: #{$popup-bottom-inner-height};
        padding: 0px 17px;
    }
}

.selected-users-popup {
    .selected-users-header {
        background: #FFFFFF;
        border-bottom: 1px solid #EEF5FF;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        height: 68px;

        .title-text {
            font-weight: 500;
            font-size: 16px;
            color: #333333;
            line-height: 22px;

        }

        .title-subheading {
            margin-top: 3px;
            font-weight: 400;
            font-size: 12px;
            color: #999999;
        }
    }


    .selected-users-content {
        height: calc(100% - 68px);
        overflow-y: auto;
        padding: 0 16px;
        background: #FFFFFF;
    }

    .selected-users-list {
        padding: 8px 0;
    }

    .selected-user-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid #EEF5FF;
    }

    .user-info {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .user-detail {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .user-name {
        font-weight: 400;
        font-size: 14px;
        color: #333333;
        line-height: 20px;
    }

    .user-code {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        line-height: 17px;
    }

    .remove-icon {
        color: #CCCCCC;
        font-size: 16px;
    }
}
</style>