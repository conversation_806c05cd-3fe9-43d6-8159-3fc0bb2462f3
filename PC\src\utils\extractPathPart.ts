// 定义一个函数来匹配并提取特定路径部分
export function extractPathPart(paths:Array<string>, str:string) {
 // 移除协议头和主机名，保留路径部分
 const pathOnly = str.replace(/^[^\/]+/, '');
 
  for (const path of paths) {
    // 同样去除路径中的协议头和主机名
    const pathToMatch = path.replace(/^[^\/]+/, '');
    
    if (pathOnly.startsWith(pathToMatch)) {
      // 如果字符串以数组中的路径开始，提取剩余部分
      const remainingPath = pathOnly.substring(pathToMatch.length);
      return remainingPath;
    }
  }
  return null; // 如果没有找到匹配的路径，则返回 null
}