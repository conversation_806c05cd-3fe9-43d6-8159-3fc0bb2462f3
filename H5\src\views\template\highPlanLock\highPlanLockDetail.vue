<template>
    <div class='highPlanLock container_'>
        <div class="container__header">
            <div style="position: relative;flex: 1;">
                <img src="@/views/template/processContro/assets/processContro.svg" alt="" srcset="">
                <span class="ml-6">高套封闭详情 ({{ route.query.time }})</span>
                <div class="tips">
                    数据统计日期为：{{ route.query.timesSection }}
                </div>
            </div>
            <div style="display: flex;align-items: center;justify-content: space-between;">
                <van-popover :actions="actionsList" @select="changeSelete" placement="bottom-end">
                    <template #reference>
                        <div class="selectOption-item">
                            <span>{{ curSelectType.text }}</span>
                            <van-icon name="play" class="play" color="#1A76FF" />
                        </div>
                    </template>
                </van-popover>
            </div>
        </div>

        <div class="container_body">
            <div class="container_body_header">
                <div class="container_body_header_item" v-for="item in tableHeader" :key="item.prop"
                    @click="sortTable(item)"
                    :class="[item.TableClass, manageType == '分局' ? 'container_body_content_L2Item' : '']">
                    <span v-html="item.label"></span>
                    <div v-if="item.prop == sortProp"
                        style="display: flex;flex-direction: column; align-items: center;gap: 0px;">
                        <i class="icon-paixu-shang fs11"
                            :style="{ color: sortType == 'asc' ? '#ffa800' : '#fff', height: '11px' }"></i>
                        <i class="icon-paixu-xia fs11"
                            :style="{ color: sortType == 'desc' ? '#ffa800' : '#fff', height: '11px' }"></i>
                    </div>

                </div>
            </div>
            <div class="container_body_content">
                <div class="container_body_content_row" v-for="(item, index) in tableData" :key="index">
                    <template v-for="(col, colIndex) in tableHeader" :key="colIndex">
                        <div class="container_body_content_item" :class="[
                            col.TableClass,
                            col.prop === 'name' && item.type != 2 ? 'active_item' : '',
                            manageType == '分局' ? 'container_body_content_L2Item' : ''
                        ]" @click="col.prop === 'name' ? changeArea(item) : null" :style="getColumnStyle(item, col)">
                            <template v-if="col.prop === 'name'">
                                <div v-getName="{ item: item }">
                                    {{ item[col.prop] }}
                                </div>
                            </template>
                            <template v-else-if="isRateColumn(col.prop)">
                                {{ (item[col.prop] * 100).toFixed(2) }}%
                            </template>
                            <template v-else>
                                {{ item[col.prop] }}
                            </template>
                        </div>
                    </template>
                </div>

                <van-empty description="暂无数据" v-if="tableData.length == 0" style="background-color: #fff;" />
            </div>
        </div>
    </div>
</template>

<script setup lang='ts'>
import { useRouter, useRoute } from "vue-router";
import { ref, reactive, computed, onMounted } from 'vue';
import { getHighValueAreaCountListAPI, getHighValueAreaL2CountListAPI, getHighValueAreaL2StaffListAPI, getHighValueManagerAreaL2CountAPI, type HighPlanLockItem } from '@/api/highPlanLock';

import { useUserInfo } from "@/stores/userInfo";
import { mapSort } from "@/utils/mapSort";
const userInfoStore = useUserInfo()

interface TableColumn {
    label: string;
    prop: keyof HighPlanLockItem;
    TableClass?: string[];
    TableColumn?: string;
    isRank?: boolean;
}

const router = useRouter();
const route = useRoute();


const manageType = ref('')
const curSelectType = reactive({
    text: '',
    value: ''
})

const actionsList = computed(() => {
    let list = [
        { text: '新装', value: '新装' },
        { text: '存量', value: '存量' }
    ]
    if (manageType.value == '分局') {
        return list
    } else {
        return [
            ...list,
            { text: '拆降', value: '拆降' },
        ]
    }
})

const tableData = ref<HighPlanLockItem[]>([])

const changeSelete = async (item: any) => {
    curSelectType.text = item.text
    curSelectType.value = item.value
    sortType.value = "asc"

    switch (item.value) {
        case '新装':
            sortProp.value = 'xzgtRate'
            break;
        case '存量':
            sortProp.value = 'clgtRate'
            break;
        case '拆降':
            sortProp.value = 'gttj'
            break;

    }

    // await loadHighValueList()
}

const tableHeader = computed<TableColumn[]>(() => {
    const baseColumns: TableColumn[] = [
        { label: manageType.value === '分局' ? '人员' : '区域/分局', prop: 'name' as keyof HighPlanLockItem, TableClass: ['tableHeader'] }
    ]

    if (curSelectType.value === '新装') {
        const columns = [
            ...baseColumns,
            { label: '新装高套', prop: 'xzgt' },
            { label: '新装高套<br/>(折算)', prop: 'xzgtzk' },
        ] as TableColumn[]

        if ((manageType.value == '分局' && route.query.time == '当月') || manageType.value != '分局') {
            columns.push({ label: '完成率', prop: 'xzgtRate', isRank: true })
        }

        return columns
    } else if (curSelectType.value === '存量') {
        const columns = [
            ...baseColumns,
            { label: '存量高套', prop: 'clgt' },
            { label: '存量高套<br/>(折算)', prop: 'clgtzk' },
        ] as TableColumn[]

        if ((manageType.value == '分局' && route.query.time == '当月') || manageType.value != '分局') {
            columns.push({ label: '完成率', prop: 'clgtRate', isRank: true })
        }

        return columns
    } else if (curSelectType.value === '拆降') {
        return [
            ...baseColumns,
            { label: '高套拆机', prop: 'gtcj' },
            { label: '高套降档', prop: 'gtjd' },
            { label: '欠停离网', prop: 'gttj', isRank: true }
        ] as TableColumn[]
    }
    return baseColumns
})

const getRankColor = (value: number, prop: keyof HighPlanLockItem) => {
    // 获取当前列的所有值并排序
    const allValues = tableData.value.map(item => item[prop] as number)
    console.log(prop);

    const uniqueSortedValues = [...new Set(allValues)].sort((a, b) => b - a)

    // 获取当前值的排名（考虑并列情况）
    const rank = uniqueSortedValues.indexOf(value) + 1
    // 前三名显示绿色，后三名显示红色
    if (rank <= 3) {
        return '#67C23A'
    } else if (rank > uniqueSortedValues.length - 3) {
        return '#F56C6C'
    }
    return '#303133' // 默认颜色
}

// 添加新的辅助方法
const isRateColumn = (prop: keyof HighPlanLockItem) => {
    return prop === 'xzgtRate' || prop === 'clgtRate'
}

const getColumnStyle = (item: HighPlanLockItem, col: TableColumn) => {
    if (col.isRank) {
        const rankProps: Record<string, keyof HighPlanLockItem> = {
            '新装': 'xzgtRate',
            '存量': 'clgtRate',
            '拆降': 'gttj'
        }
        const prop = rankProps[curSelectType.value]
        if (prop) {
            return { color: getRankColor(item[prop] as number, prop) }
        }
    }
    return {}
}

const sortType = ref("asc")
const sortProp = ref<keyof HighPlanLockItem>('')
const sortTable = (item: TableColumn) => {
    if (item.label == '人员' || item.label == '区域/分局') return
    if (item.prop == sortProp.value) {
        sortType.value = sortType.value === "asc" ? "desc" : "asc"
    } else {
        sortType.value = "desc"
        sortProp.value = item.prop as keyof HighPlanLockItem
    }
    sortProp.value = item.prop as keyof HighPlanLockItem
    tableData.value.sort((a: HighPlanLockItem, b: HighPlanLockItem) => {
        if (sortType.value === "asc") {
            return (a[sortProp.value as keyof HighPlanLockItem] as number) - (b[sortProp.value as keyof HighPlanLockItem] as number)
        } else {
            return (b[sortProp.value as keyof HighPlanLockItem] as number) - (a[sortProp.value as keyof HighPlanLockItem] as number)
        }
    })
}


const areaId = ref('')  //区域ID
const areaIdL2 = ref('') //分局ID
const loadHighValueList = async () => {
    let params = {
        type: route.query.timeType,
        areaId: areaId.value,
        areaIdL2: areaIdL2.value,
    }

    if (manageType.value === '分局') {
        const API = areaIdL2.value ? getHighValueManagerAreaL2CountAPI : getHighValueAreaL2StaffListAPI
        API(params).then((res: any) => {
            if (res.code == 200) {
                res.data = res.data.map((item: HighPlanLockItem) => {
                    return {
                        ...item,
                        type: 2,
                        name: item.assStaffNm,
                        staffCode: item.assStaffNumber
                    }
                })
                tableData.value = res.data
                console.log(tableData.value);
            }
        })

    } else if (manageType.value === 'area') {
        getHighValueAreaCountListAPI(params).then((res: any) => {
            if (res.code == 200) {
                res.data = res.data.map((item: HighPlanLockItem) => {
                    return {
                        ...item,
                        type: 0,
                        name: item.assStaffNm,
                        areaId: item.assStaffNumber
                    }
                })
                tableData.value = mapSort(res.data, 'name')
                console.log(tableData.value, 'tableData.value');
            }
        })

    } else if (manageType.value === 'dept') {

        getHighValueAreaL2CountListAPI(params).then((res: any) => {
            if (res.code == 200) {
                res.data = res.data.map((item: HighPlanLockItem) => {
                    return {
                        ...item,
                        type: 1,
                        name: item.assStaffNm,
                        areaIdL2: item.assStaffNumber
                    }
                })
                tableData.value = res.data
                console.log(tableData.value, 'tableData.value');
            }
        })
    }
}

function changeArea(row: HighPlanLockItem) {
    if (row.type == 0) {
        areaId.value = row.assStaffNumber
        manageType.value = 'dept'
    } else if (row.type == 1) {
        if (curSelectType.text == '拆降') {
            showToast('拆降暂不支持下钻至人员 ')
            return
        }
        areaId.value = ''
        areaIdL2.value = row.areaIdL2
        manageType.value = '分局'

    } else {
        return
    }
    tableData.value = []
    loadHighValueList()
}

onMounted(async () => {
    curSelectType.text = route.query.type as string || '新装'
    curSelectType.value = route.query.type as string || '新装'
    manageType.value = route.query.manageType as string
    loadHighValueList()
})
</script>

<style lang="scss" scoped>
$containerHeaderHeight: 60px;

.highPlanLock {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #fff;
    border-radius: 10px;
    overflow: hidden;

    .container__header {
        padding: 12px 16px;
        background: #FFFFFF;
        display: flex;
        justify-content: space-between;
        align-items: center;

        height: #{$containerHeaderHeight};
        box-sizing: border-box;

    }

    .container_body {
        flex: 1;
        overflow-y: auto;
        padding: 0px 0px 10px 0px;

        &_header {
            display: flex;
            height: #{$containerHeaderHeight};
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: #3064E7;

            &_item {
                flex: 1 1 25%;
                max-width: 25%;
                padding: 0px 3px;
                display: flex;
                align-items: center;
                justify-content: center;

                text-align: center;
                font-weight: 500;
                color: #fff;
                font-size: 14px;
                border-right: 1px solid #EBEEF5;
                border-bottom: 1px solid #EBEEF5;


                &:first-child {
                    flex: 0 0 80px;
                }

                &:last-child {
                    border-right: none;
                }
            }

            &_L2Item {
                flex: 1 1 33.33333%;
                max-width: 33.33333%;
            }

        }

        &_content {
            background: #FFFFFF;
            border-radius: 8px;

            &_row {
                display: flex;
                border-bottom: 1px solid #EBEEF5;

                &:last-child {
                    border-bottom: none;
                }
            }

            &_item {
                flex: 1 1 25%;
                max-width: 25%;
                padding: 12px 3px;
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                font-size: 14px;
                color: #303133;
                border-right: 1px solid #EBEEF5;
                /* 普通文字截断换行 */
                word-break: break-all;
                white-space: normal;

                &:first-child {
                    flex: 0 0 80px;
                }

                &:last-child {
                    border-right: none;
                }
            }

            &_L2Item {
                flex: 1 1 33.33333%;
                max-width: 33.33333%;
            }
        }
    }
}

.tableHeader {
    flex: 0 0 115px !important;
    max-width: 115px !important;
}

.active_item {
    color: #4487eb !important;
    text-decoration: underline !important;
    text-underline-offset: 2px !important;

}

.tips {
    position: absolute;
    left: 24px;
    bottom: -18px;
    font-size: 10px;
    color: #464646;
}

.selectOption-item {
    background: #EDF5FF;
    border-radius: 5px;
    font-weight: 400;
    font-size: 14px;
    color: #1A76FF;
    padding: 8px;
    display: flex;
    align-items: center;

    .play {
        transform: rotate(90deg);
        margin-left: 3px;
    }

    &:active {
        background: #d8dee6;
    }
}
</style>