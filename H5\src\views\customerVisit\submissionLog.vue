<template>
    <div class="card container_">

        <van-sticky>
            <div class="container__header" ref="container__header">
                <div>
                    <img src="./assets/history.png" alt="" srcset="">
                    <span class="ml-6" style="font-weight: 500;">本周人员提交记录</span>
                </div>

            </div>
        </van-sticky>

        <div class="container_body">

            <div class="pb-5">
                <van-search v-model="searchName" class="searchInput" placeholder="请选择查询对象/条线/工号">
                    <template #left-icon>
                        <img class="mt-5" src="@/assets/search.svg" alt="" srcset="">
                    </template>
                </van-search>
            </div>


            <div class="optionList">

                <div class="list-item" v-for="item in staffList_" :key="item.id">
                    <img src="@/assets/defaultAvatar.png" alt="">
                    <div class="ml-6">
                        <div class="name">
                            <span v-getName="{ item: item }">{{ item.staffName }}</span>
                            <span class="fs10 ml-3">{{ item.staffCode }} </span>
                        </div>
                        <div class="staffRole">{{ item.roleName }}</div>
                    </div>
                    <div style="text-align: right;">
                        <div class="fs14" style="color: #333333; letter-spacing: 2px;">
                            {{ item.weekLeaderConnect }}/{{ item.weekSubLeaderConnect }}/{{ item.weekDevelopTianyiCount }}
                        </div>
                        <div class="fs10 mt-3" style="color: #999999;">一把手/分管领导/天翼数</div>
                    </div>
                </div>

                <van-empty description="暂无数据" class="mt-8" style="background-color: #FBFCFF;border-radius: 15px;"
                    v-if="tableList.length == 0" />


            </div>
        </div>





    </div>
</template>

<script setup lang="ts">
import { getVisitDataListAPI } from "@/api/customerVisit";


import { mapSort } from "@/utils/mapSort";


interface List {
    id: string,
    staffName: string,
    staffCode: string,
    points: number,
    ronghe: number,
    xinliangZgz: number
    [key: string]: any
}




const searchName = ref('')



const tableList = ref([])

const staffList_: ComputedRef<List[]> = computed(() => {
    const input = searchName.value.toLowerCase();

    return tableList.value.filter((item: any) => {
        const staffName = item.staffName ? item.staffName.toLowerCase() : '';
        const staffRoleName = item.roleName ? item.roleName.toLowerCase() : '';
        const staffCode = item.staffCode ? item.staffCode.toLowerCase() : '';
        return (
            staffName.includes(input) ||
            staffRoleName.includes(input) ||
            staffCode.includes(input)
        );
    });
})




function loadList() {
    let params = {
        pageNum: 1,
        pageSize: 3,
    }
    getVisitDataListAPI(params).then((res: any) => {

    })


    getVisitDataListAPI(params).then((res: any) => {
        if (res.code == 200) {

            tableList.value = res.data
        }
    })
}



onMounted(() => {
    loadList()
})
</script>



<style lang="scss" scoped>
.card {
    font-family: 'Source Han Sans, Source Han Sans';
    height: 100%;
    background: #fbfcff;
    overflow-y: scroll;
    overflow-x: hidden;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px 10px 0px 0px;


}

.container__header {
    background-color: #fbfcff;
}

.container_body {
    padding-bottom: 70px;
}







.optionList {

    .list-item {
        display: grid;
        grid-template-columns: 43px 1fr max-content;
        align-items: center;
        border-bottom: 1px solid #EEF5FF;
        padding: 16px 0px;

        img {
            width: 43px;
            height: 43px;

        }

        .name {
            font-weight: 500;
            font-size: 14px;
            color: #3D3D3D;
        }

        .staffRole {
            font-weight: 400;
            margin-top: 3px;
            font-size: 12px;
            color: #999999;
        }
    }

}




:deep(.van-search) {
    padding: 0px;
}

:deep(.van-search__action) {
    line-height: 0;
}

:deep(.van-search__content) {
    background-color: #EDF5FF;
    border-radius: 5px;
}

:deep(.van-field__control::placeholder) {
    color: #72ABFF;
}

:deep(.van-field__control) {
    color: #1a76ff;
}

:deep(.van-search__field) {
    height: 44px;
}

:deep(.van-icon-search) {
    color: #1a76ff;
}
</style>