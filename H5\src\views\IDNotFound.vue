<template>
	<div class="box">
		<img class="header" src="@/assets/IDNotFound/header.png" alt="" srcset="">
		<div class="hint">{{ route.query.phoneNumber ? `当前企微绑定手机号为 ${route.query.phoneNumber}` : '暂未查询到您的工号' }}</div>
		<div class="box-content">
			<img class="contentImg" src="@/assets/IDNotFound/content.png" alt="" srcset="">
			<div class="contentIndex">
				<div class="index">01</div>
				<div class="content">
					请核实当前企微姓名、绑定的手机号和统一工号中的信息是否一致。
				</div>
			</div>
			<div class="contentIndex">
				<div class="index" style="top: 0px;">02</div>
				<div class="content">
					如不一致，请修改企微的绑定手机号，确保和统一工号中一致，修改入口为:点击企微首页左上角-姓名或头像，进入个人信息页-手机即可更换手机号。
				</div>
			</div>
			<div class="contentIndex">
				<div class="index">03</div>
				<div class="content">
					如您不会操作或操作后仍无法进入，请联系本地企微管理员。
				</div>
			</div>

		</div>
	</div>
</template>

<script lang="ts" setup>
import { useRoute } from "vue-router";
const route = useRoute();

</script>

<style lang="scss">
.box {
	background: #F7F7F7;
	min-height: 100vh;
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 0px 48px;

	.header {
		margin: 30px auto;
		width: 75%;
	}

	.hint {
		font-family: 'PingFangSC-Medium, PingFangSC-Medium';
		font-weight: 400;
		font-size: 14px;
		color: #666666;
	}

	.box-content {
		position: relative;
		width: 100%;
		background: #FFFFFF;
		border-radius: 10px 10px 10px 10px;
		padding: 43px 23px 28px 23px;
		margin-top: 44px;

		.contentImg {
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			width: 168px;
			height: 33px;
			top: -15px;
		}

		.contentIndex {
			width: 100%;
			margin-top: 19px;
			background: url('@/assets/IDNotFound/contentIndex.svg') no-repeat;
			background-position: 0px 0px;
			padding: 21px 20px 20px 48px;
			box-sizing: border-box;
			position: relative;
			background-color: #F4F7FE;
			border-radius: 5px;

			.index {
				font-family: DIN, DIN;
				font-weight: 700;
				font-size: 18px;
				color: #FFFFFF;
				position: absolute;
				left: 5px;
				top: -2px;
			}

			.content {
				font-family: 'Source Han Sans, Source Han Sans';
				font-weight: 400;
				font-size: 12px;
				color: #666666;
			}
		}
	}
}
</style>
