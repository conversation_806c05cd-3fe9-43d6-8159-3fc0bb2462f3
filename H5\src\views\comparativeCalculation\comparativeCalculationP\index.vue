<template>
  <div class="card container_">
    <div class="container__header">
      <div>
        <img src="../../template/MyDay/assets/images/bsIcon.png" alt="" srcset="" class="card_icon" />
        <span class="ml-6">比算提交</span>
      </div>
      <van-popover :actions="modelList" @select="onSelect" placement="bottom" class="cusPopover">
        <template #reference>
          <div class="selectOption-item  ml-25">
            <span>{{ curSelectType.text }}</span>
            <van-icon name="play" class="play" color="#1A76FF" />
          </div>
        </template>
      </van-popover>

    </div>
    <div class="form_card">
      <van-form required="auto" ref="formField" :show-error-message="true">
        <van-cell-group>

          <van-field name="uploader" label="上传照片" label-align="left" input-align="right"
            :rules="[{ required: true, message: '请拍摄照片' }]">
            <template #input>
              <!-- capture="camera" -->
              <div class="right_align">
                <van-uploader :after-read="handleAfterRead" :before-read="handleBeforeRead" result-type="file"
                  v-model="formData.fileList" preview-size="89" capture="camera" :before-delete="deleteFile"
                  :max-count="3">
                  <template #default>
                    <div class="form_card_camera">
                      <van-image width="28px" height="23px" :src="camera" />
                    </div>
                  </template>
                </van-uploader>
              </div>
            </template>
          </van-field>
          <van-field v-model="formData.description" :rules="[{ required: true, message: '请输入留言' }]" label-align="top"
            autosize label="备注" type="textarea" maxlength="200" placeholder="请输入留言" clearable>
            <template #label>
              <div style="width: 100%; display: flex;align-items: center;justify-content: space-between;">
                <span style="font-weight: 700;">备注</span>
                <i v-if="text" class="icon-niantie fs16" @touchstart="handleTouchStart" @touchend="handleTouchEnd"
                  @click="pasteText"></i>
              </div>

            </template>
          </van-field>
        </van-cell-group>
      </van-form>
    </div>
    <div class="container_body">
      <van-field v-for="(item, index) in imageTransitionTextList" :readonly="true" label-align="top"
        :label="`识别结果${index + 1}`" :key="item.url" v-loading="item"
        style="background-color: #f3f6f9c2; border-radius: 10px;" v-model="item.ImageText" rows="3" autosize
        type="textarea" class="mb-20">
        <template #label>
          <!-- icon-niantie -->
          <div style="display: flex;align-items: center;justify-content: space-between;width: 100%;">
            <span style="color: black;font-weight: 600;" class="fs14">{{ `识别结果${index + 1}` }}</span>
            <span class="icon-fuzhi fs16 copy" @touchstart="handleTouchStart" @touchend="handleTouchEnd"
              @click="copyText(item.ImageText)"></span>
          </div>

        </template>
      </van-field>
    </div>
    <router-link to="/comparativeCalculationDetail">
      <van-field input-align="right">
        <template #input>
          <span class="sign_list">比算记录</span>
        </template>
      </van-field>
    </router-link>

    <div class="sure_btn">
      <van-button type="primary" :disabled="submitLoading" color="  linear-gradient( 90deg, #73ACFF 0%, #237CFF 100%)"
        block @click="submit">确认</van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { uploadVoiceAPI } from "@/api/common";
import camera from "../../template/MyDay/assets/images/camera.png";
import { UploaderFileListItem } from "vant";
import { useRoute, useRouter } from "vue-router";
import { compareSaveAPI, getCompareListAPI, compareAuditAPI } from "@/api/comparative";
// import { uploadFileAPI, deleteFile } from "@/api/common";
import { debounce } from "@/utils/debounce";
import { uploadFileHooks } from "@/hooks_modules/upload_hooks";
import { useClipboard } from '@vueuse/core'

const { text, copy, copied, isSupported } = useClipboard()
const {
  handleAfterRead,
  handleBeforeRead,
  deleteFile,
  onSelect,
  // inputFile,
  uploadLoading,
  formLine,
  formData,
  imageTransitionTextList,
  curSelectType,
  modelList
} = uploadFileHooks({
  enableTextRecognition: true,
  enableAddWatermarkToImage: false
})
const router = useRouter();
let formField = ref();



const submitLoading = computed(() => {
  return imageTransitionTextList.value.some(item => item.visible == true) || uploadLoading.value
})



const submit = debounce(() => {
  formField.value
    .validate()
    .then(() => {
      console.log(formLine);
      let params: any = {
        images: formLine.images.join(','),
        description: formData.description,
      }
      imageTransitionTextList.value.forEach((item, index) => {
        params[`imagesDescription${index + 1}`] = item.ImageText
      })

      compareSaveAPI(params).then(res => {
        showSuccessToast("提交成功");
        router.push({ path: '/comparativeCalculationDetail' })
      })
    })
    .catch(() => {
      showFailToast("请完善信息");

    });
}, 300, true)

const handleTouchStart = (event: TouchEvent) => {
  const target = event.currentTarget as HTMLElement;
  target.style.color = '#689CFD';
  target.style.fontSize = '18px';

}

const handleTouchEnd = (event: TouchEvent) => {
  const target = event.currentTarget as HTMLElement;
  target.style.color = '';
  target.style.fontSize = '16px';
}

async function pasteText() {
  if (!isSupported.value) {
    showFailToast("浏览器不支持剪贴板功能");
    return;
  }

  try {
    // 使用 useClipboard 的 text 属性获取剪贴板内容
    if (text.value) {
      formData.description += text.value;
      showSuccessToast("粘贴成功!");
    } else {
      showFailToast("剪贴板为空");
    }
  } catch (error) {
    showFailToast("粘贴失败，尝试使用兼容方法");
    fallbackPasteText();
  }
}

// 兼容方法：创建隐藏的输入框，然后执行 paste 操作
function fallbackPasteText() {
  const textArea = document.createElement("textarea");
  document.body.appendChild(textArea);
  textArea.focus();
  const successful = document.execCommand("paste"); // 仅在 HTTPS 或 WebView 内可用
  if (successful && textArea.value) {
    formData.description += textArea.value;
    showSuccessToast("粘贴成功!");
  } else {
    showFailToast("浏览器不支持粘贴功能或剪贴板为空");
  }
  document.body.removeChild(textArea);
}

const copyText = async (text: string) => {
  await copy(text)
  showSuccessToast('复制成功!');
}
</script>

<style lang="scss" scoped src="../formCard.scss">
.copy {
  -webkit-tap-highlight-color: transparent;

  &:active {
    color: #237CFF;
    opacity: 0.8;
  }
}
</style>

<style lang="scss" scoped>
.copy {
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  display: inline-block;
  padding: 4px;
  transition: color 0.2s;
}



.selectOption-item {
  background: #EDF5FF;
  border-radius: 5px 5px 5px 5px;
  font-weight: 400;
  font-size: 14px;
  color: #1A76FF;
  padding: 8px;
  display: flex;
  align-items: center;

  .play {
    transform: rotate(90deg);
    margin-left: 3px;
  }
}

.selectOption-item:active {
  background: #d8dee6;
}



:deep(.van-popover__content) {
  overflow-y: auto;
  max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
  width: auto;
}
</style>
