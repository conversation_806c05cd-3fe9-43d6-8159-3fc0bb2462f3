<template>
    <div class="card container_">
        <div class="container__header">
            <div>
                <img src="../../template/MyDay/assets/images/bsIcon.png" alt="" srcset="" />
                <span class="ml-6">查看比算</span>
            </div>
        </div>
        <van-list v-model:loading="loading" :finished="finished" :finished-text="tableData.length > 0 ? '没有更多了' : ''"
            @load="onLoad">
            <div class="content" v-for="(item, index) in tableData" :key="index">
                <van-row>
                    <div class="font font_date">{{ item.date }}</div>
                </van-row>
                <div class="font_row">
                    <div class="font font_label">{{ item.roleName }}</div>
                    <div class="font font_title">
                        <span v-getName="{ item: item }">{{ item.name }}</span>
                        <span class="ml-5 fs10">{{ item.staffCode }}</span>
                    </div>
                </div>
                <div class="font_row">
                    <div class="font font_label">比算时间</div>
                    <div class="font font_title">{{ item.time }}</div>
                </div>
                <div class="imgList" v-if="item.images.length > 0">
                    <div class="font font_label">比算照片</div>
                    <div class="font font_value">
                        <van-image width="90" :radius="5" v-imgPreview="[item.images, indexE]" lazy-load height="90"
                            :src="el" v-for="el, indexE in item.images" />
                    </div>
                </div>
                <div class="imgList">
                    <div class="font font_label">比算备注</div>
                    <div class="font font_label font_title" style="padding-top: 0px;padding-bottom: 0px;">
                        <van-text-ellipsis :content="item.description" expand-text="展开" collapse-text="收起" />
                    </div>
                </div>
                <div class="imgList" v-if="item.images.length > 0" v-for="el in item.images.length">
                    <div class="font font_label">识别文本{{ el }}</div>
                    <div class="font font_title pl-15 pr-15">
                        <van-text-ellipsis :content="item[`imagesDescription${el}`]" expand-text="展开"
                            collapse-text="收起" />
                    </div>
                </div>

            </div>
        </van-list>
        <van-empty description="暂无数据" v-if="tableData.length == 0" style="background-color: #fff;" />
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { comparativeListHooks } from "../listHooks";



const {
    loadList,
    onLoad,
    tableData,
    loading,
    finished,
} = comparativeListHooks()



onMounted(() => {
    loadList()
})

</script>

<style lang="scss" scoped>
.card {
    min-height: 100%;
    background: #fbfcff;
    // overflow-x: hidden;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px 10px 0px 0px;
}




.content {
    background: #EDF5FF;
    border-radius: 10px 10px 10px 10px;
    margin: 13px 15px;
    border-bottom: 1px solid #EEF5FF;

    .van-row {
        padding: 15px 18px;
        border-bottom: 1px solid #edf5ff;
    }
}

.content:not(:first-child) {
    border-radius: 10px;
}

.font_value {
    text-align: right;
}



.font {
    font-family: Source Han Sans, Source Han Sans;
    font-style: normal;
    text-transform: none;

}

.font_date {
    font-size: 18px;
    line-height: 20px;
    color: #1a76ff;
    text-align: center;
    font-weight: 500;
}



.font_label {
    padding: 15px 18px;
    font-weight: 500;
    font-size: 14px;
    color: #3D3D3D;
    line-height: 16px;
}

.imgList {
    border-bottom: 1px solid #EEF5FF;
    padding-bottom: 10px;
}

.font_value {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    place-items: center;
    padding: 15px 18px;
    padding-top: 0px;

    img {
        border-radius: 5px;
    }
}


.font_row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 18px;
    border-bottom: 1px solid #edf5ff;
}

.font_title {
    font-weight: 400;
    font-size: 14px;
    color: #A8A8A8;
}
</style>