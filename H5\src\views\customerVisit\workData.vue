<template>
    <div class="container_ ">
        <div class="card">
            <div class="container__header">
                <div>
                    <img src="./assets/customerVisit.svg" alt="" srcset="" class="card_icon" />
                    <span class="ml-6">工作数据收集</span>
                </div>
            </div>

            <div class="container_body" style="padding: 0px 0px; ">

                <van-form required="auto" ref="formField" label-width="200">

                    <van-field v-model="formLine_.weekLeaderConnect" name="number" label="周一把手建立联系客户数"
                        placeholder="请输入数量" label-align="left" input-align="right" type="digit"
                        :rules="[{ required: true, message: '请输入数量' }]">

                    </van-field>
                    <van-field v-model="formLine_.weekSubLeaderConnect" name="number" label="周分管领导建立联系客户数"
                        placeholder="请输入数量" label-align="left" input-align="right" type="digit"
                        :rules="[{ required: true, message: '请输入数量', }]">

                    </van-field>
                    <van-field v-model="formLine_.weekDevelopTianyiCount" name="number" label="周产数/创新反哺发展天翼数"
                        placeholder="请输入数量" label-align="left" input-align="right" type="digit"
                        :rules="[{ required: true, message: '请输入数量', }]">
                    </van-field>
                </van-form>
                <div class="sure_btn">
                    <van-button type="primary" color=" linear-gradient( 90deg, #73ACFF 0%, #237CFF 100%)" block
                        @click="submit">提交方案</van-button>
                </div>
            </div>

        </div>
        <div class="card mt-12">
            <div class="container__header">
                <div>
                    <img src="./assets/history.png" alt="" srcset="" class="card_icon" />
                    <span class="ml-6">本周人员提交记录</span>
                </div>
                <div class="fs12" style="color:#2079FF" @click="goToDetail">查看详情 <van-icon name="arrow"
                        color="#2079FF" />
                </div>
            </div>
            <div class="container_body" style="padding: 0px;">
                <div class="container_body_list" v-for="(item, index) in tabList">
                    <img src="@/assets/defaultAvatar.png" alt="" srcset="">
                    <div class="ml-8">
                        <div class="name">{{ item.staffName }}</div>
                        <div class="role">{{ item.roleName }}</div>
                    </div>
                    <div>
                        <div class="name" style="letter-spacing: 2px;"> {{ item.weekLeaderConnect }}/{{
                            item.weekSubLeaderConnect }}/{{ item.weekDevelopTianyiCount }}</div>
                        <div class="role">一把手/分管领导/天翼数</div>
                    </div>
                </div>
                <van-empty description="暂无数据" v-if="tabList.length == 0" />

            </div>


        </div>




    </div>



</template>

<script setup lang="ts">
interface UserEntry {
    staffName: string;
    roleName: string;
    avatarUrl: string;
    score: string;
    weekLeaderConnect: string;
    weekSubLeaderConnect: string;
    weekDevelopTianyiCount: string;

}
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast, showSuccessToast, showFailToast } from "vant";
import { visitDataSaveAPI, getVisitDataListAPI } from "@/api/customerVisit";
import { debounce } from "@/utils/debounce";
const router = useRouter();
let formField = ref();





const formLine_ = reactive({
    weekLeaderConnect: '',
    weekSubLeaderConnect: '',
    weekDevelopTianyiCount: '',


})


const submit = debounce(() => {
    formField.value
        .validate()
        .then(() => {
            let params = {
                weekLeaderConnect: formLine_.weekLeaderConnect,
                weekSubLeaderConnect: formLine_.weekSubLeaderConnect,
                weekDevelopTianyiCount: formLine_.weekDevelopTianyiCount,
            }
            visitDataSaveAPI(params).then((res: any) => {
                if (res.code == 200) {
                    showSuccessToast("提交成功");
                    loadList()
                }
            })

        })
        .catch((err: any) => {
            showFailToast("请完善信息");
        });
}, 300, true)


function goToDetail() {
    router.push({
        path: '/submissionLog',
    });
}




const tabList = ref<UserEntry[]>([])

function loadList() {
    const today = new Date();
    const lastWeek = new Date(today);
    lastWeek.setDate(today.getDate() - 7);
    let params = {
        startTime: `${formatDate(lastWeek)} 00:00:00`,
        endTime: `${formatDate(today)} 23:59:59`,
    }
    getVisitDataListAPI(params).then((res: any) => {
        if (res.code == 200) {
            tabList.value = res.data.sort((a: UserEntry, b: UserEntry) => {
                const totalA = parseInt(a.weekLeaderConnect) + parseInt(a.weekSubLeaderConnect) + parseInt(a.weekDevelopTianyiCount);
                const totalB = parseInt(b.weekLeaderConnect) + parseInt(b.weekSubLeaderConnect) + parseInt(b.weekDevelopTianyiCount);
                return totalB - totalA;
            });
        }
    })
}

const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

onMounted(async () => {

    loadList()
})

</script>


<style lang="scss" scoped>
.container_ {
    background-color: transparent;
    margin-bottom: 80px;
}

.card {

    background: #fff;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px;
    padding-bottom: 20px;
}



.form_card_camera {
    width: 89px;
    height: 89px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(103, 159, 255, 0.15);
}

.sure_btn {
    width: calc(100% - 18px - 18px);
    margin: 30px auto;
    margin-bottom: 0px;
}



.container_body_list {
    display: grid;
    grid-template-columns: 40px auto max-content;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #EDF5FF;

    &>img {
        width: 100%;
    }

    &>div:last-child {
        text-align: right;
    }

    .name {
        font-weight: 500;
        font-size: 14px;
        color: #333333;
    }

    .role {
        font-weight: 400;
        font-size: 10px;
        color: #999999;
        margin-top: 5px;
    }
}
</style>
