<template>


    <div class=" container_body activity-container">
        <div class="person-section " v-for="item, index in dataItem " :key="index">
            <div class="header">
                <span class="name">{{ item.name }}</span>


                <van-popover>


                    <div clareaass="popoverBody">
                        <li>100积分=5活力值</li>
                        <li>宽带和主推融合=2活力值</li>
                        <li>其它=1活力值</li>
                        <li>其中装维工单为竣工工单数/当日预约工单数，按竣工数计算活力值</li>
                        <li>商机录入为商机助手采集的商机数量</li>
                    </div>
                    <template #reference>
                        <span class="activity-score">
                            <span>{{ item.avHuoli }}</span>
                            <span class="post">人均活力</span>
                            <van-icon class="ml-3" name="question" color="#1A76FF" />
                        </span>
                    </template>
                </van-popover>
                <div class="playBox">
                    <van-icon name="play" @click="item.isShow = !item.isShow" class="play"
                        :class="item.isShow ? '' : 'playHide'" color="#1A76FF" />
                </div>
            </div>
            <div class="tips" @click="viewStores(item)">
                <span style="display: flex;align-items: center;">

                    <img src="../assets/tips.svg" alt="" class="mr-4">
                    <span> 点击查看分局贡献详情~</span>
                </span>
                <div class="button">
                    立刻前往
                </div>

            </div>
            <collapseTransition>
                <div class="icon-grid" v-show="item.isShow">
                    <div class="icon-item">
                        <van-badge :content="item.points" color="#FF9760">
                            <img src="../assets/Points.svg" alt="" srcset="">
                        </van-badge>
                        <span>积分</span>
                    </div>
                    <div class="icon-item">
                        <van-badge :content="item.tianYi" color="#FF9760">
                            <img src="../assets/Tianyi.svg" alt="" srcset="">
                        </van-badge>
                        <span>天翼</span>
                    </div>
                    <div class="icon-item">
                        <van-badge :content="item.kuanDai" color="#FF9760">
                            <img src="../assets/Broadband.svg" alt="" srcset="">
                        </van-badge>
                        <span>宽带</span>
                    </div>
                    <div class="icon-item">
                        <van-badge :content="item.outCall" color="#FF9760">
                            <img src="../assets/outboundCall.svg" alt="" srcset="">
                        </van-badge>
                        <span>外呼</span>
                    </div>
                    <div class="icon-item">
                        <van-badge :content="item.signCount" color="#FF9760">
                            <img src="../assets/checkIn.svg" alt="" srcset="">
                        </van-badge>
                        <span>工作写实</span>
                    </div>

                    <div class="icon-item">
                        <van-badge :content="item.businessRecords" color="#FF9760">
                            <img src="../assets/opportunityEntry.svg" alt="" srcset="">
                        </van-badge>
                        <span>商机录入</span>
                    </div>
                    <div class="icon-item">
                        <van-badge :content="item.xinliangZgz" color="#FF9760">
                            <img src="../assets/mainPromotion.svg" alt="" srcset="">
                        </van-badge>
                        <span>主推融合</span>
                    </div>
                    <div class="icon-item">
                        <van-badge :content="item.tianyiXuyue" color="#FF9760">
                            <img src="../assets/tianyiRenewal.svg" alt="" srcset="">
                        </van-badge>
                        <span>天翼续约</span>
                    </div>
                    <div class="icon-item">
                        <van-badge :content="item.kuandaiXuyue" color="#FF9760">
                            <img src="../assets/broadbandRenewal.svg" alt="" srcset="">
                        </van-badge>
                        <span>宽带续约</span>
                    </div>
                    <div class="icon-item">
                        <van-badge color="#FF9760">
                            <img src="../assets/maintenanceOrder.svg" alt="" srcset="">
                            <template #content>
                                {{ item.repairOrder }}/{{ item.repairOrderTotal }}
                            </template>
                        </van-badge>
                        <span>装维工单</span>
                    </div>
                </div>
            </collapseTransition>
        </div>
        <van-empty description="暂无数据" v-if="dataItem.length == 0" />



    </div>

</template>

<script setup lang='ts'>
import collapseTransition from "@/components/collapse-transition/index.vue";
import { ref, reactive, onMounted, onUnmounted, computed, toRefs, PropType } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const props = defineProps({
    dataItem: {
        type: Array as PropType<ContributionItem[]>,
        default: (): ContributionItem[] => ([])
    }

});
const emits = defineEmits(['selectRow', 'viewStores']);
const { dataItem } = toRefs(props);



function viewStores(row: ContributionItem) {
    emits('viewStores', row)
}



</script>

<style lang='scss' scoped>
.popoverBody {
    font-size: 13px;
    padding: 5px 10px;
}

.person-section {
    background-color: #EDF5FF;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

    .tips {
        background: linear-gradient(90deg, #DBEBFF 0%, #D4E0FF 100%);
        border-radius: 5px;
        padding: 3px 5px;
        display: flex;
        justify-content: space-between;
        font-weight: 400;
        font-size: 12px;
        color: #3D3D3D;
        text-align: center;
        font-style: normal;
        text-transform: none;
        margin-top: 5px;

        img {
            width: 18px;
            height: 18px
        }

        .button {
            background: #4174FF;
            border-radius: 5px;
            padding: 3px 5px;
            border: 1px solid #4174FF;
            font-family: 'Source Han Sans, Source Han Sans';
            font-weight: 300;
            font-size: 10px;
            color: #FFFFFF;
            line-height: 11px;
            text-align: center;
            font-style: normal;
            text-transform: none;

        }
    }

}

.header {
    display: grid;
    grid-template-columns: auto max-content 30px;
    // display: flex;
    // justify-content: space-between;
    align-items: center;
    column-gap: 10px;
}

.playBox {
    flex: 0 0 auto;
    display: grid;
    place-items: center;
    width: 25px;
    height: 25px;
    border-radius: 10px;
    margin-left: 10px;
    background: #d4e0ff70;

    .play {
        transform: rotate(90deg);

        transition: transform 0.3s;


    }

    .playHide {
        transform: rotate(-90deg);
    }
}

.name {
    font-size: 18px;
    font-weight: bold;
    color: #3D3D3D;
    line-height: 20px;
}

.post {

    font-weight: 400;
    font-size: 13px;
    color: #3D3D3D;
    line-height: 16px;
    // margin-left: 15px;
}

.activity-score {
    &>span:first-child {
        font-family: 'YouSheBiaoTiHei, YouSheBiaoTiHei';
        font-weight: 400;
        font-size: 24px;
        color: #1A76FF;
        line-height: 27px;
        font-weight: bold;
    }

    &>span:last-child {
        font-weight: 500;
        font-size: 10px;
        color: #3D3D3D;
        margin-left: 3px;
    }
}

.icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(45px, 1fr));
    gap: 0px 10px;
    margin-top: 15px;

}

.icon-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    border-radius: 8px;
    color: #3D3D3D;
    position: relative;

    img {
        width: 34px;
        height: 34px;
        margin-bottom: 4px;
    }
}



.icon-item span {
    font-size: 12px;
    color: #3D3D3D;
    text-align: center;
}

</style>