import vue from "@vitejs/plugin-vue";
import requireTransform from "vite-plugin-require-transform";
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
// import viteImagemin from "vite-plugin-imagemin";
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import { defineConfig, loadEnv, ConfigEnv } from "vite";
import path from "path";
import commonjs from "vite-plugin-commonjs";
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const viteConfig = defineConfig((mode: ConfigEnv) => {

  const env = loadEnv(mode.mode, process.cwd());
  const isBuild = mode.command === 'build' // 是否为生产模式

  // const buildType = env.VITE_BUILD_TYPE;
  return {
    plugins: [
      vue(),
      // 添加更多实用插件

     

      // CommonJS 模块转换插件，用于处理 require 语法
      // 主要用于兼容一些使用 CommonJS 模块的第三方库
      commonjs(),
      // basicSsl(), // 配置证书
      // compression({
      //   algorithm: 'gzip', // or 'gzip'
      //   ext: '.gz', // or '.gz'
      // }),

      // require 转换插件，用于处理 TypeScript 和 Vue 文件中的 require 语句
      requireTransform({
        fileRegex: /.ts$|.tsx$|.vue$/,
      }),
      AutoImport({
        include: [
          /\.[tj]sx?$/,  // 匹配 .ts, .tsx, .js, .jsx 文件
          /\.vue$/,      // 匹配 .vue 文件
          /\.vue\?vue/,  // 匹配 Vue 单文件组件
        ],
        imports: [
          'vue',          // Vue 核心 API (ref, reactive, computed 等)
          'vue-router',   // 路由相关 (useRoute, useRouter 等)
          'pinia',        // 状态管理 (defineStore, storeToRefs 等)
          '@vueuse/core', // Vue 组合式 API 工具集
          // 按需导入特定库的函数
          {
            'lodash-es': [
              'debounce',  // 防抖函数
              'throttle',  // 节流函数
              'cloneDeep'  // 深拷贝函数
            ],
            'axios': ['default']  // HTTP 请求库
          }
        ],
        // 类型声明文件位置
        dts: 'types/auto-imports.d.ts',
        // 需要自动导入的目录
        dirs: [
          'src/views/**',      // 页面组件目录
          'src/utils/**',      // 工具函数目录
          'src/store/**',      // 状态管理目录
        ],
        // Element Plus 组件自动导入解析器
        resolvers: [ElementPlusResolver()],
      }),
      Components({
        // 组件类型声明文件位置
        dts: 'types/components.d.ts',
        dirs: ['src/components'], // 组件目录配置
        resolvers: [ElementPlusResolver()], // Element Plus 组件解析器
        types: [
          {
            from: 'vue-router',
            names: ['RouterLink', 'RouterView'],
          },
        ],
        // 添加以下配置
        deep: true,  // 是否递归扫描子目录
        directoryAsNamespace: true,  // 使用目录名作为命名空间
        extensions: ['vue', 'tsx'],  // 支持的文件类型
      })
    ],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),

        "@components": path.resolve(__dirname, "./src/components"),
        "@views": path.resolve(__dirname, "./src/views"),
        "@utils": path.resolve(__dirname, "./src/utils"),
        "@assets": path.resolve(__dirname, "./src/assets"),
        "@api": path.resolve(__dirname, "./src/api"),
        "@store": path.resolve(__dirname, "./src/store"),
        "@types": path.resolve(__dirname, "./src/types"),
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern', // or 'modern'
          additionalData: `@use "@/styles/variables.scss" as *; @use "@/styles/mixin.scss" as *;`  // 只注入变量文件避免循环依赖 每个 SCSS 文件编译时都会注入variables.scss
        },
      },
      // // 添加以下配置
      // modules: {
      //   localsConvention: 'camelCase',  // 类名驼峰转换
      // },
    },

    base: env.VITE_BASE_ASSEST, // 打包路径


    server: {
      host: true,
      // port: env.VITE_PORT as unknown as number, // 服务端口号
      open: false, // 服务启动时是否自动打开浏览器
      // https: false,
      hmr: true, // 开启热更新
      // cors: true, // 允许跨域
      proxy: {
        [env.VITE_APP_BASE_API]: {
          target: env.VITE_BASE_URL,
          changeOrigin: true,
          secure: false,
          // configure: (proxy) => {
          //   proxy.on('proxyReq', (proxyReq, req, res) => {
          //     // 移除 'X-Forwarded-For' 头
          //     proxyReq.removeHeader('X-Forwarded-For');
          //   });
          // },

          rewrite: (path) =>
            path.replace(new RegExp("^" + env.VITE_APP_BASE_API), env.VITE_MODE == 'localhost' ? '' : env.VITE_APP_BASE_API),

          // bypass(req, res, options) {
          //   console.log(req);

          //   const proxyURL = options.target + options.rewrite(req.url);
          //   res.setHeader('x-req-proxyURL', proxyURL) // 将真实请求地址设置到响应头中
          // },

        },


      },
    },
    build: {
      sourcemap: mode.command === "serve",
      // minify: mode.command === "build" ? "esbuild" : false,
      minify: "terser",
      chunkSizeWarningLimit: 1500,
      emptyOutDir: true,
      outDir: 'dist',
      reportCompressedSize: false,
      assetsDir: 'assets',
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
        },
      },

      // esbuild: {
      //   drop: mode.command === "build" ? ['console', 'debugger'] : [], //去除console和debugger
      //   pure: mode.command === "build" ? ['console.log'] : []
      // },
      rollupOptions: {
        commonjsOptions: {
          transformMixedEsModules: true,
          // 添加更多 CommonJS 转换选项
          include: [/node_modules/],
          exclude: ['src/**'],
        },
        // output: {
        //   chunkFileNames: 'assets/js/[name]-[hash].js',
        //   entryFileNames: 'assets/js/[name]-[hash].js',
        //   assetFileNames: (assetInfo) => {
        //     const info = assetInfo.name.split('.');
        //     const ext = info[info.length - 1];
        //     if (/\.(woff2?|eot|ttf|otf)$/.test(assetInfo.name)) {
        //       return `assets/fonts/[name]-[hash][extname]`;
        //     }
        //     if (/\.(png|jpe?g|gif|svg|webp)$/.test(assetInfo.name)) {
        //       return `assets/images/[name]-[hash][extname]`;
        //     }
        //     if (/\.css$/.test(assetInfo.name)) {
        //       return `assets/css/[name]-[hash][extname]`;
        //     }
        //     return `assets/[name]-[hash][extname]`;
        //   },
        // },
        output: {
          chunkFileNames: 'js/[name]-[hash].js', // 引入文件名的名称
          entryFileNames: 'js/[name]-[hash].js', // 包的入口文件名称

          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split('.');
            const ext = info[info.length - 1];
            if (/\.(woff2?|eot|ttf|otf)$/.test(assetInfo.name)) {
              return `font/[name]-[hash][extname]`;
            }
            return '[ext]/[name]-[hash].[ext]';
          },          // 将 node_modules 三方依赖包最小化拆分
          manualChunks(id) {

            if (id.includes('utils/crypto/index')) {
              return 'crypto_util'; // 将该文件单独打包为 `crypto` chunk
            }

            if (id.includes('node_modules')) {
              const paths = id.toString().split('node_modules/')
              if (paths[2]) {
                return paths[2].split('/')[0].toString()
              }

              return paths[1].split('/')[0].toString()
            }

          },
        },
      },


    },
    define: {
      // enable hydration mismatch details in production build
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'true'
    },

    optimizeDeps: {
      /**
       * 依赖预构建，vite 启动时会将下面 include 里的模块，编译成 esm 格式并缓存到 node_modules/.vite 文件夹，
       * 页面加载到对应模块时如果浏览器有缓存就读取浏览器缓存，如果没有会读取本地缓存并按需加载
       * 尤其当您禁用浏览器缓存时（这种情况只应该发生在调试阶段）必须将对应模块加入到 include 里，
       * 否则会遇到开发环境切换页面卡顿的问题（vite 会认为它是一个新的依赖包会重新加载并强制刷新页面），
       * 因为它既无法使用浏览器缓存，又没有在本地 node_modules/.vite 里缓存
       * 温馨提示：如果你使用的第三方库是全局引入，也就是引入到 src/main.ts 文件里，
       * 就不需要再添加到 include 里了，因为 vite 会自动将它们缓存到 node_modules/.vite
       */
      include: ['vue', 'vue-router', 'pinia', '@vueuse/core', 'lodash-es', 'axios'],
      // 打包时强制排除的依赖项
      exclude: [
        '@vue/devtools-api',
        'element-plus/lib',
      ],
    },
  };
});

export default viteConfig;
