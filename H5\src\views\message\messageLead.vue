<template>
    <div class="container_">
        <div class="container__header">
            <div style="width: 100%;">
                <div v-for="item in tabList" class="head_item" :class="curTab == item.value ? 'active-item' : ''"
                    :key="item.value" @click="switchTab(item)">{{ item.text }}</div>
            </div>

        </div>
        <div class="container_body" style="padding-top: 0px; ;">
            <Message v-if="curTab == '接收消息'" userType="Lead"> </Message>

            <SeedMessage v-else
                :seedMessageType="route.query.seedMessageType ? String(route.query.seedMessageType) : '已发送'">
            </SeedMessage>

        </div>
    </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from "vue-router";
import Message from "./index.vue";
import SeedMessage from "./seedMessage.vue";

const route = useRoute()
const curTab = ref<string>('接收消息')
const tabList = ref([
    { text: '接收消息', value: '接收消息', },
    { text: '发送消息', value: '发送消息' },
])
function switchTab({ text, value }: { text: string, value: string }) {
    curTab.value = value
}
onMounted(() => {
    curTab.value = route.query.messageType ? String(route.query.messageType) : '接收消息'
})


</script>

<style lang="scss" scoped>
.container_ {
    min-height: 100%;
    background: #fff;
}

.container__header {
    &>div:first-child {
        display: flex;
        justify-content: center !important;
        align-items: flex-end;
        gap: 30px;
    }


    .head_item {
        font-weight: 500;
        font-size: 14px;
        color: #919191;
        line-height: 16px;
    }

    .active-item {
        font-weight: 500;
        font-size: 16px;
        color: #3D3D3D;
        line-height: 18px;
    }
}


:deep(.container_body) {

    .container__header,
    .message-item {
        padding-left: 0px;
        padding-right: 0px;
    }

}
</style>