import { ComponentPropsOptions, ComponentOptions, ref, reactive } from "vue";
import { formatDate, constgetBeforeDayDate } from "@/utils/loadTime";
import { getAllStaffListAPI } from "@/api/common";
import { useUserInfo } from "@/stores/userInfo";
interface SearchHooksReturn {
    formline: any; // 根据实际情况定义
    defaultDate: any; // 根据实际情况定义
    maxDate: any; // 根据实际情况定义
    calendarIsShow: Ref<boolean>;
    curSelect: any; // 根据实际情况定义
    selectOptionList: Ref<any[]>; // 根据实际情况定义,
    searchName: Ref<string>;
    listData: ComputedRef<StaffVO[]>;
    popoverIsShow: Ref<boolean>
    changeSelete: (value: string) => void;
    selectTime: () => void;
    onConfirm: (params: any) => void;
    confirmStaffCode: (params: any) => void,
}

type ParamsProps = {
    source: string,
    callback: (data?: any) => void;

}

interface StaffVO {
    staffCode?: string;
    staffName?: string;
    staffRoleName?: string;
    staffId?: string;
    orgId?: string;
}


export const searchHooks = function (params: Partial<ParamsProps> = {}): SearchHooksReturn {
    const { source, callback } = params
    const userInfoStore = useUserInfo()
    const formline = reactive({
        startTime: '',
        endTime: '',
        staffCode: [],
    })
    const popoverIsShow = ref<boolean>(false)
    const searchName = ref('')

    const defaultDate = ref<Date[] | null>([])
    // const calendarType = ref<'single'|'multiple'|'range'>('range')  //日期类型默认选择时间范围
    const maxDate = ref<Date>(new Date()) // 日历最大可选择天 默认是今天
    const calendarIsShow = ref<boolean>(false)  //日历组件是否显示
    const curSelect = ref<string>('0') //当前选择日期标签类型
    const selectOptionList = ref([
        { text: '全部', value: '0' },
        // { text: '当月', value: '4' },
        { text: '今天', value: '1' },
        { text: '昨天', value: '2' },
        { text: '近一周', value: '3' },
    ])
    const listData = computed(() => {

        let list = [{ staffCode: '全部', staffName: '全部人员', staffId: '', orgId: '' }, ...userInfoStore.getStaffList]
        let index = list.findIndex(item => item.staffCode == userInfoStore.userInfo.staffCode)
        const myself = list[index]
        list.splice(index, 1)
        list.splice(1, 0, myself)
        return list
    })
    function changeSelete(value: string) {
        if (curSelect.value == value) return
        curSelect.value = value
        let startTime: Date
        switch (value) {
            //全部
            case '0':
                formline.startTime = ''
                formline.endTime = ''
                defaultDate.value = null
                break;
            // 今天
            case '1':
                formline.startTime = formatDate(new Date(), 'yyyy-MM-dd')
                formline.endTime = formatDate(new Date(), 'yyyy-MM-dd')
                defaultDate.value = [new Date(), new Date()]
                break;
            // 昨天
            case '2':
                startTime = constgetBeforeDayDate(new Date(), 1)

                formline.startTime = formatDate(startTime, 'yyyy-MM-dd')
                formline.endTime = formatDate(new Date(), 'yyyy-MM-dd')
                defaultDate.value = [startTime, startTime]
                break;
            // 近一周
            case '3':
                startTime = constgetBeforeDayDate(new Date(), 6)
                formline.startTime = formatDate(startTime, 'yyyy-MM-dd')
                formline.endTime = formatDate(new Date(), 'yyyy-MM-dd')
                defaultDate.value = [startTime, new Date()]
                break;
            //当月
            case '4':
                startTime = new Date(new Date().getFullYear(), new Date().getMonth(), 1)
                formline.startTime = formatDate(startTime, 'yyyy-MM-dd')
                formline.endTime = formatDate(new Date(), 'yyyy-MM-dd')
                defaultDate.value = [startTime, new Date()]
                break;
        }

        callback && callback()

    }
    function selectTime() {
        calendarIsShow.value = true
    }

    function onConfirm(params: any) {
        defaultDate.value = params
        if (Array.isArray(params)) {
            formline.startTime = formatDate(params[0], 'yyyy-MM-dd')
            formline.endTime = formatDate(params[1], 'yyyy-MM-dd')
        } else {
            formline.startTime = formatDate(params, 'yyyy-MM-dd')
            formline.endTime = formatDate(params, 'yyyy-MM-dd')
        }

        calendarIsShow.value = false
        curSelect.value = ''
        callback && callback()
    }



    function confirmStaffCode(selectedValues: any) {
        formline.staffCode = selectedValues.selectedValues
        searchName.value = selectedValues.selectedOptions[0].staffName
        popoverIsShow.value = false
    }


    return {
        formline,
        defaultDate,
        maxDate,
        calendarIsShow,
        curSelect,
        selectOptionList,
        searchName,
        listData,
        popoverIsShow,
        changeSelete,
        selectTime,
        onConfirm,

        confirmStaffCode,
    }

}


