// 导入所需的 Node.js 模块
// const fs = require('fs').promises;  // 文件系统模块，使用 Promise 版本
// const process = require('child_process');  // 子进程模块，用于执行命令
// const path = require('path');  // 路径处理模块

import { promises as fs } from 'fs';
import { exec } from 'child_process';
import path from 'path';

// 定义要处理的目标目录
const relativePath = './dist/js';

// 定义需要跳过的文件/库列表（不需要混淆的文件）
const skipFiles = [
    '@vant', 'vue', 'vant', 'echarts', 'pinia-plugin-persistedstate',
    'pinia', 'myCharts', 'vconsole', 'lodash', 'xlsx', 'axios', 'zrender', 
];

// 'config'

// 并发控制相关变量
const MAX_CONCURRENT = 10;  // 最大并发任务数
let activeTasks = 0;  // 当前活动任务数
const taskQueue = [];  // 任务队列
const taskPromises = [];  // 存储所有任务的 Promise

/**
 * 处理队列中的下一个任务
 * 确保同时运行的任务不超过 MAX_CONCURRENT
 */
async function processNext() {
    // 如果队列为空或已达到最大并发数，则返回
    if (taskQueue.length === 0 || activeTasks >= MAX_CONCURRENT) {
        return;
    }

    activeTasks++;  // 增加活动任务计数
    const task = taskQueue.shift();  // 从队列中取出一个任务
    const promise = task().finally(() => {
        activeTasks--;  // 任务完成后减少活动任务计数
        processNext();  // 尝试处理下一个任务
    });
    taskPromises.push(promise);  // 将任务 Promise 添加到数组中
    processNext();  // 尝试处理更多任务
}

/**
 * 将任务添加到队列中
 * @param {Function} task - 要执行的任务函数
 */
async function enqueueTask(task) {
    taskQueue.push(task);
    processNext();
}

/**
 * 处理单个文件或目录
 * @param {string} filePath - 文件或目录的路径
 */
async function scannerFile(filePath) {
    try {
        const stats = await fs.stat(filePath);
        if (stats.isDirectory()) {
            // 如果是目录，递归处理
            await readDirectory(filePath);
        } else if (filePath.endsWith('.js')) {
            // 如果是 JS 文件，执行混淆
            console.log('开始混淆:', filePath);
            await runObfuscation(filePath);
        } else {
            // 其他类型文件跳过
            console.log(filePath, '-->skip: not a .js file');
        }
    } catch (error) {
        console.error('Error processing file', filePath, error);
    }
}

/**
 * 读取目录内容并处理文件
 * @param {string} directory - 要处理的目录路径
 */
async function readDirectory(directory) {
    try {
        const files = await fs.readdir(directory);
        for (const file of files) {
            const fullPath = path.join(directory, file);
            // 如果文件不在 skipFiles 中且不是 .json 文件，则进行混淆
            if (!(skipFiles.some(skipKey => fullPath.includes(skipKey))) && !file.endsWith('.json')) {
                await enqueueTask(() => scannerFile(fullPath));
            } else {
                console.log(fullPath, '-->skip: in skipFiles or json file');
            }
        }
    } catch (error) {
        console.error('Error reading directory', directory, error);
    }
}

/**
 * 执行文件混淆操作
 * @param {string} filePath - 要混淆的文件路径
 * @returns {Promise} - 返回混淆操作的 Promise
 */
function runObfuscation(filePath) {
    return new Promise((resolve, reject) => {
        // 构建混淆命令
        const cmd = `javascript-obfuscator ${filePath} --config obfuscator.json --output ${filePath}`;
        // 执行混淆命令
        exec(cmd, (error, stdout, stderr) => {
            if (error) {
                console.error("error:", error);
                reject(error);
                return;
            }
            if (stderr) {
                console.error("stderr:", stderr);
            }
            console.log("stdout:", stdout);
            resolve();
        });
    });
}

/**
 * 启动程序的主函数
 */
async function start() {
    await readDirectory(relativePath);  // 开始处理目标目录
    await Promise.all(taskPromises);    // 等待所有任务完成
}

// 执行程序
start();