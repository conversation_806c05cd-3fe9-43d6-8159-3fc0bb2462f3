
import request from "../utils/request";
import configAxios from './config'

//获取分局执行
export function getDaySecuresWeek_AREAL2ListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/daySecuresWeek/areal2`,
        method: "post",
        data,
    });
}

//派单执行
export function getDaySecuresWeek_OREADERListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/daySecuresWeek/project`,
        method: "post",
        data,
    });
}
//白名单执行
export function getDaySecuresWeek_WLListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/daySecuresWeek/staff`,
        method: "post",
        data,
    });
}
