.btn {
  cursor: pointer;
  outline: none !important;
  vertical-align: middle;
  transition: color 0.15s ease, background-color 0.15s ease,
    border-color 0.15s ease, box-shadow 0.15s ease,
    -webkit-box-shadow 0.15s ease;
  font-weight: 500;
  // display: inline-block;
  // text-align: center;
  border: 1px solid transparent;
  padding: 8px 13px;
  font-size: 13px;
  line-height: 20px;
  border-radius: 9px;
  display: inline-flex;
  align-items: center;
  overflow: hidden;
}

/* btn-light-success   */
.btn.btn-light-success {
  color: #1bc5bd;
  background-color: #c9f7f5;
  border-color: transparent;
}

.btn.btn-light-success:hover:not(.btn-text):not(:disabled):not(.disabled) {
  color: #ffffff;
  background-color: #1bc5bd;
  border-color: transparent;
}

.btn:hover:not(.btn-text) {
  transition: color 0.15s ease, background-color 0.15s ease,
    border-color 0.15s ease, box-shadow 0.15s ease,
    -webkit-box-shadow 0.15s ease;
}

.btn.btn-success:hover {
  color: #ffffff;
  background-color: #0bb7af;
  border-color: #0bb7af;
}

.btn.btn-success {
  color: #ffffff;
  background-color: #1bc5bd;
  border-color: #1bc5bd;
}
/* ---------------------------------- */
/* btn-light-primary */
.btn.btn-light-primary {
  color: #0bb783;
  background-color: #d7f9ef;
  border-color: transparent;
}

.btn.btn-light-primary:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-light-primary:focus:not(.btn-text),
.btn.btn-light-primary.focus:not(.btn-text) {
  color: #ffffff;
  background-color: #0bb783;
  border-color: transparent;
}

/* ---------------------------------- */
/* btn-primary  */
.btn.btn-primary {
  color: #ffffff;
  background-color: #0bb783;
  border-color: #0bb783;
}
.btn.btn-primary:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-primary:focus:not(.btn-text),
.btn.btn-primary.focus:not(.btn-text) {
  color: #ffffff;
  background-color: #04aa77;
  border-color: #04aa77;
}
/* --------------- */
/* btn-danger */
.btn.btn-danger {
  color: #ffffff;
  background-color: #f64e60;
  border-color: #f64e60;
}
.btn.btn-danger:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-danger:focus:not(.btn-text),
.btn.btn-danger.focus:not(.btn-text) {
  color: #ffffff;
  background-color: #ee2d41;
  border-color: #ee2d41;
}
/* -------------------------- */
/* btn-secondary */
.btn.btn-secondary {
  color: #3f4254;
  background-color: #e4e6ef;
  border-color: #e4e6ef;
}
.btn.btn-secondary:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-secondary:focus:not(.btn-text),
.btn.btn-secondary.focus:not(.btn-text) {
  color: #3f4254;
  background-color: #d7dae7;
  border-color: #d7dae7;
}
/* -------------------------- */
/* btn-warning */
.btn.btn-warning {
  color: #ffffff;
  background-color: #ee9d01;
  border-color: #ee9d01;
}

.btn.btn-warning:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-warning:focus:not(.btn-text),
.btn.btn-warning.focus:not(.btn-text) {
  color: #ffffff;
  background-color: #ee9d01;
  border-color: #ee9d01;
}
/* -------------------------- */
/* btn-light */
.btn.btn-light {
  color: #7e8299;
  background-color: #f3f6f9;
  border-color: #f3f6f9;
}
.btn.btn-light:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-light:focus:not(.btn-text),
.btn.btn-light.focus:not(.btn-text) {
  color: #7e8299;
  background-color: #e4e6ef;
  border-color: #e4e6ef;
}
/* -------------------------- */
/* btn-info  */
.btn.btn-info {
  color: #ffffff;
  background-color: #8950fc;
  border-color: #8950fc;
}
.btn.btn-info:hover:not(.btn-text):not(:disabled):not(.disabled),
.btn.btn-info:focus:not(.btn-text),
.btn.btn-info.focus:not(.btn-text) {
  color: #ffffff;
  background-color: #7337ee;
  border-color: #7337ee;
}
