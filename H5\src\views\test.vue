<template>
  <div class="voice-recorder">
    <div class="btn-group">
      <!-- 使用 Vant 按钮组件 -->
      <van-button type="primary" @click="startRecording" :disabled="recording">
        开始录音
      </van-button>
      <van-button type="danger" @click="stopRecording" :disabled="!recording">
        停止录音
      </van-button>
    </div>
    
    <!-- 显示录音完成后的播放器 -->
    <div v-if="audioUrl" class="audio-player">
      <audio id="audioPlayer" :src="audioUrl" controls preload="auto"></audio>
    </div>

    <!-- 上传和下载按钮 -->
    <div v-if="audioBlob" class="btn-group">
      <van-button type="success" @click="playAudio">
        播放录音
      </van-button>
      <van-button type="success" @click="uploadAudio">
        上传录音
      </van-button>
      <van-button @click="downloadAudio">
        下载录音
      </van-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { showFailToast, showSuccessToast, showLoadingToast } from 'vant'

const recording = ref(false)
const audioUrl = ref<string | null>(null)
const audioBlob = ref<Blob | null>(null)
let mediaRecorder: MediaRecorder | null = null
const audioChunks: Blob[] = [] // 存储音频数据块

// 🔥 检查麦克风权限
const checkPermission = async () => {
  const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName })
  if (permission.state === 'denied') {
    showFailToast('麦克风权限被拒绝，请在设置中打开权限')
    return false
  }
  return true
}

// 🎯 将 Blob 转换成 Base64 格式
const blobToBase64 = (blob: Blob): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(blob) // 将 Blob 读取为 base64
    reader.onloadend = () => resolve(reader.result as string) // 读取完成后返回 base64
    reader.onerror = (error) => reject(error)
  })
}

// 🚀 开始录音
const startRecording = async () => {
  const permissionGranted = await checkPermission()
  if (!permissionGranted) return

  if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
    showFailToast('当前浏览器不支持录音功能')
    return
  }

  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    mediaRecorder = new MediaRecorder(stream)

    mediaRecorder.ondataavailable = (event: BlobEvent) => {
      if (event.data && event.data.size > 0) {
        audioChunks.push(event.data)
      }
    }

    mediaRecorder.onstop = async () => {
      // ✅ 合并数据块生成 Blob 文件
      audioBlob.value = new Blob(audioChunks, { type: 'audio/wav' })
      audioChunks.length = 0

      try {
        // ✅ 将 Blob 转换成 Base64 格式
        audioUrl.value = await blobToBase64(audioBlob.value)
        console.log('Base64音频：', audioUrl.value)
        showSuccessToast('录音完成，已转换为 Base64')
      } catch (error) {
        console.error('Base64 转换失败:', error)
        showFailToast('Base64 转换失败')
      }
    }

    mediaRecorder.start()
    recording.value = true
    showLoadingToast({ message: '录音中...', duration: 0 })
  } catch (error) {
    console.error('录音失败:', error)
    showFailToast('录音失败，请检查录音权限')
  }
}

// 🛑 停止录音
const stopRecording = () => {
  if (mediaRecorder && recording.value) {
    mediaRecorder.stop()
    recording.value = false
  }
}

// ▶️ 播放录音
const playAudio = () => {
  if (audioUrl.value) {
    const audioPlayer = document.getElementById('audioPlayer') as HTMLAudioElement
    audioPlayer.src = audioUrl.value // ✅ 使用 Base64 作为音频源
    audioPlayer.play().catch(err => {
      console.error('播放失败:', err)
      showFailToast('播放失败')
    })
  }
}

// 📤 上传录音
const uploadAudio = () => {
  if (!audioBlob.value) return
  const formData = new FormData()
  formData.append('file', audioBlob.value, 'recording.wav')

  fetch('/upload', {
    method: 'POST',
    body: formData
  })
    .then(response => response.json())
    .then(data => {
      showSuccessToast('上传成功')
      console.log('上传成功:', data)
    })
    .catch(error => {
      console.error('上传失败:', error)
      showFailToast('上传失败')
    })
}

// 💾 下载录音文件
const downloadAudio = () => {
  if (!audioUrl.value) return
  const a = document.createElement('a')
  a.href = audioUrl.value
  a.download = 'recording.wav'
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}
</script>

<style lang="scss" scoped>
.voice-recorder {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
}

.btn-group {
  margin: 10px 0;
  display: flex;
  gap: 10px;
}

.audio-player {
  margin-top: 10px;
  width: 100%;
  max-width: 300px;
}
</style>
