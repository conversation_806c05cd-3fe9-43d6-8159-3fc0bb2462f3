const swipeGesture = {
    install: (app: any) => {
        app.directive('swipeGesture', {
            mounted(el: any, binding: any) {
                let startX = 0;
                let startTime = 0;

                // 触摸开始处理函数
                const handleTouchStart = (event: TouchEvent) => {
                    const touch = event.touches[0];
                    startX = touch.clientX;  // 记录起始X坐标
                    startTime = new Date().getTime();  // 记录起始时间

                };

                // 触摸结束处理函数
                const handleTouchEnd = (event: TouchEvent) => {
                    const touch = event.changedTouches[0];
                    const endX = touch.clientX;  // 结束时的X坐标
                    const endTime = new Date().getTime();  // 结束时的时间

                    const deltaX = endX - startX;  // 滑动距离
                    const deltaTime = endTime - startTime;  // 滑动时间


                    // 判断是否为快速滑动（时间小于 300 毫秒，滑动距离大于 40 像素）
                    if (deltaTime < 300 && Math.abs(deltaX) > 40) {
                        if (deltaX > 0) {
                            // 右滑
                            binding.value('left');
                        } else {
                            // 左滑
                            binding.value('right');
                        }
                    }
                };

                // 添加事件监听器
                el.addEventListener('touchstart', handleTouchStart);
                el.addEventListener('touchend', handleTouchEnd);

                // 存储事件监听器以便后续移除
                el._handleTouchStart = handleTouchStart;
                el._handleTouchEnd = handleTouchEnd;
            },

            // 在元素卸载时移除事件监听器
            beforeUnmount(el: any) {
                el.removeEventListener('touchstart', el._handleTouchStart);
                el.removeEventListener('touchend', el._handleTouchEnd);
            }
        });
    }
};

export default swipeGesture;
