<template>
    <div class='card container_'>
        <div class="container__header">
            <div>
                <img src="@/assets/comparativeCalculationAudit.svg" alt="" srcset="">
                <span class="ml-6">客户派单</span>
            </div>
            <cusTabs :tabList="tabList" v-model:curSelectTab="curSelectType" @changeSelectTab="changeSelectTab">
            </cusTabs>
        </div>
        <!-- <cusTabs :tabList="statusList" v-model:curSelectTab="curSelectStatus" @changeSelectTab="changeStatus"
            v-if="curSelectType == '客户'">
        </cusTabs> -->
        <div>
            <van-search v-model="searchName" placeholder="请搜索客户姓名" @update:model-value="changeSearchName"
                :show-action="curSelectType == '客户'">
                <template #left-icon>
                    <img class="mt-5" src="@/assets/search.svg" alt="" srcset="">
                </template>
                <template #action>
                    <van-popover :actions="statusList" @select="changeStatus" placement="bottom">
                        <template #reference>
                            <div class="selectOption-item ">
                                <span>{{ curSelectStatus.text }}</span>
                                <van-icon name="play" class="play" color="#1A76FF" />
                            </div>
                        </template>
                    </van-popover>
                </template>
            </van-search>
        </div>



        <div class=" container_body ">
            <van-list v-model:loading="loading" :finished="finished"
                :finished-text="tableData.length > 0 ? '没有更多了' : ''" @load="onLoad">

                <template v-if="curSelectType == '客户'">
                    <div class="box_content" @click="toEdit(item, '人员')" v-for="item, index in tableData"
                        :key="item.id || index">
                        <div class="box_content_header">
                            <div> {{ item.customerName }}</div>
                            <div>{{ item.count }}条派单</div>
                        </div>
                    </div>

                </template>
                <template v-else>

                    <div class="dispatch_item" @click="toEdit(item, '主题')" :style="{
                        borderLeft: item.status == '1' ? '8px solid #ee9d0194' : '8px solid #7AA6FF'
                    }" v-for="item, index in tableData" :key="index">
                        <div class="dispatch_item_header">
                            <div class="row" style="color: #000;font-weight: 600;">派单主题:{{ item.collectionName }}</div>
                            <div class="row">创建时间:{{ item.createTime }}</div>
                            <div class="row">派单活动:{{ item.activityName }}</div>
                            <div class="row">创建部门:{{ item.departmentName }}</div>
                            <div class="status" :class="item.status == '1' ? 'warming' : 'sucss'">{{ item.status == '1'
                                ? '未结束' : '已结束'
                            }}</div>
                        </div>

                    </div>
                </template>


            </van-list>
            <van-empty description="暂无数据" v-if="tableData.length == 0" />



        </div>








    </div>
</template>

<script setup lang="ts">
import cusTabs from "@/components/cusTabs/index.vue";
import { debounce } from "lodash-es";
import { getTicketByCustomerNameAPI, getCollectionPageAPI } from "@/api/dispatch";
import { status } from "nprogress";
const router = useRouter()

interface CustomerItem {
    id: string;                // 客户ID
    customerName: string;              // 客户名称
    contactName: string;       // 联系人姓名
    contactPhone: string;      // 联系人电话
    count: number;     // 派单数量
    staffCode: string;         // 员工编码

}

interface DispatchItem {
    id: string;                // 派单ID
    collectionName: string;             // 派单主题
    createTime: string;        // 创建时间
    activityName: string;          // 派单活动
    staffCode: string
    date: string
    departmentName: string;        // 派单部门
    departmentId: string;
    customerName: string;      // 客户名称
    status: '1' | '2'; // 派单状态
}

interface CustomerOrDispatchItem extends CustomerItem, DispatchItem { }

interface LoadParams {
    pageNum: number;
    pageSize: number;
    customerName: string;
    status?: string; // 添加可选属性
}

const tabList = ref([
    { text: '客户', value: '客户' },
    { text: '派单', value: '派单' },

])
const curSelectType = ref<string>('客户')

const searchName = ref<string>('')
const pageSize = ref(10);
const total = ref(0);
const pageNum = ref(1);

const loading = ref<boolean>(false)
const finished = ref<boolean>(true)

const tableData = ref<CustomerOrDispatchItem[]>([])
function changeSelectTab(item: { value: string }) { // 更改参数类型

    curSelectType.value = item.value
    pageNum.value = 1
    tableData.value = []
    loadList()
}

const changeSearchName = debounce(() => {
    pageNum.value = 1
    tableData.value = []
    loadList()
}, 500)


const curSelectStatus = reactive({
    value: '1',
    text: '未结束'
})

const statusList = ref([
    { text: '未结束', value: '1' },
    { text: '已结束', value: '2' },

])
function changeStatus(item: any) {

    curSelectStatus.value = item.value
    curSelectStatus.text = item.text
    pageNum.value = 1
    tableData.value = []
    loadList()
}


function loadList() {
    const toast = showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        loadingType: 'spinner',
        duration: 0,
        overlay: true,
        teleport: '.main-page',
    });

    let params: LoadParams = {
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        customerName: searchName.value,
    }
    if (curSelectType.value == '客户') params.status = curSelectStatus.value
    const API = curSelectType.value == '客户' ? getTicketByCustomerNameAPI : getCollectionPageAPI
    API(params).then((res: any) => {
        if (res.code == 200) {
            tableData.value = [...tableData.value, ...res.data.records]
            total.value = res.data.total
            if (total.value <= tableData.value.length) {
                finished.value = true
            } else {
                finished.value = false
            }

        }

    }).finally(() => {
        loading.value = false;
        toast.close()
    })


}


function onLoad() {
    pageNum.value += 1;
    loadList()
}


function toEdit(item: CustomerOrDispatchItem, type: string) {
    console.log(item);
    router.push({
        path: '/dispatchEdit',
        query: {
            type: type,
            customerName: item.customerName,
            collectionName: item.collectionName,
            departmentId: item.departmentId,
            departmentName: item.departmentName,
        }
    })
}







onMounted(() => {
    loadList()
})

</script>

<style lang="scss" scoped>
$vanSearchHeight: 40px;

.card {
    min-height: 100%;
    background: #fff;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px;
}

:deep(.van-search__action) {
    line-height: 0;
}

:deep(.van-search__content) {
    background-color: #EDF5FF;
    border-radius: 5px;
}



:deep(.van-icon-search) {
    color: #1a76ff;
}

:deep(.van-search__field) {
    height: #{$vanSearchHeight}
}


:deep(.van-field__value) {
    height: 36px;
    line-height: 36px;
    font-weight: 400;
    font-size: 14px;
    color: #72ABFF;
}

:deep(.van-field__control) {
    font-weight: 400;
    font-size: 14px;
    color: #72ABFF;
}

:deep(.van-field__control::placeholder) {
    color: #72ABFF;
}





.box_content {
    background: #f3f6f9;
    border-radius: 10px 10px 10px 10px;
    padding: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), 0 3px 5px rgba(0, 0, 0, 0.1);

    .box_content_header {
        display: grid;
        grid-template-columns: 1fr auto;
        justify-content: space-between;
        align-items: center;

        &>:first-child {
            font-weight: 700;
            font-size: 14px;
            color: #000;
        }

        &>:last-child {
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            background-color: #EFF6FE;
            color: #1a76ff;
        }
    }

    .row {
        font-weight: 500;
        font-size: 12px;
        margin: 3px 0;
    }
}

.dispatch_item {
    margin-bottom: 10px;
    background: #f3f6f9;
    border-radius: 10px 10px 10px 10px;
    padding: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1), 0 3px 5px rgba(0, 0, 0, 0.1);
    position: relative;

    .row {
        font-weight: 500;
        color: #737778;
        margin: 3px 0px;
        font-size: 13px;
    }

    .status {


        background-color: #72ABFF;
        position: absolute;
        right: 0px;
        top: 0px;
        font-size: 12px;
        padding: 5px 10px;
        border-radius: 0 15px 0 15px;
        color: #EDF5FF;


    }

    .sucss {
        background-color: #3F7FFF !important;
    }

    .warming {
        background-color: #ee9d0194 !important;
    }
}

.selectOption-item {
    box-sizing: border-box;
    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
    font-weight: 400;
    font-size: 14px;
    color: #1A76FF;
    padding: var(--van-search-padding);
    height: #{$vanSearchHeight};
    // margin: var(--van-search-padding);
    margin-bottom: 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    .play {
        transform: rotate(90deg);
    }
}

.selectOption-item:active {
    background: #d8dee6;
}


.box_content:not(:first-child) {
    margin-top: 15px
}
</style>