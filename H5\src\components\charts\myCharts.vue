<template>
  <div class="echart" ref="chartDom"></div>
</template>
<script setup>
// import * as echarts from "echarts";
import * as echarts from "echarts/core"; // 引入核心模块
import { Pie<PERSON><PERSON>, BarChart, LineChart, FunnelChart,RadarChart } from "echarts/charts"; // 引入饼图、柱状图、雷达图
import { TooltipComponent, LegendComponent, GridComponent, TitleComponent, DataZoomComponent } from "echarts/components"; // 引入常用的组件
import { SVGRenderer } from "echarts/renderers"; // 引入 SVG 渲染器
import { ref, onMounted, onBeforeUnmount, watch } from "vue";


const props = defineProps({
  options: Object,
  events: Object, // 新增: 用于传入事件和回调的 props
})


// 注册需要的图表和组件
echarts.use([Pie<PERSON><PERSON>, BarChart, LineChart, TooltipComponent, Funnel<PERSON>hart, LegendComponent, GridComponent, <PERSON><PERSON><PERSON><PERSON>, TitleComponent, DataZoomComponent,Radar<PERSON>hart]);

const chartDom = ref()
let myChart = null;



const resizeHandler = () => {
  myChart.resize();
}
const debounce = (fun, delay) => {
  let timer;
  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      fun();
    }, delay);
  }
};

const cancalDebounce = debounce(resizeHandler, 500);

onMounted(() => {

  myChart = echarts.init(chartDom.value, null, { renderer: 'svg' })
  myChart.setOption(props.options, true);



  // 绑定传入的事件
  if (props.events) {
    Object.keys(props.events).forEach(eventName => {
      myChart.on(eventName, props.events[eventName]);
    });
  }

  window.addEventListener('resize', cancalDebounce);

})

onBeforeUnmount(() => {
  // 解绑已绑定的事件
  if (props.events) {
    Object.keys(props.events).forEach(eventName => {
      myChart.off(eventName, props.events[eventName]);
    });
  }
  window.removeEventListener('resize', cancalDebounce)
  myChart.dispose()
})



watch(() => props.options, () => {
  myChart.setOption(props.options, true);
}, { deep: true })

</script>
<style lang="scss" scoped>
.echart {}
</style>
