import { formatCurDate } from "@/utils/loadTime";

interface WatermarkOptions {
    fontSize?: number;
    fontColor?: string;
    backgroundColor?: string;
    backgroundPadding?: number;
    backgroundRadius?: number;
    opacity?: number;
    repeat?: boolean;
    marginBottom?: number;
    marginTop?: number;
    marginRight?: number;
    rotate?: number;
    compressionQuality?: number; // 新增：图片压缩质量，默认 0.8
}

function getContrastingColor(bgColor: string): string {
    const hex = bgColor.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#FFFFFF';
}

function fileToDataURL(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = () => reject(new Error('文件读取失败'));
        reader.readAsDataURL(file);
    });
}

async function addWatermarkToImage(
    input: File | string,
    options: WatermarkOptions = {},
    watermarkText?: string,
): Promise<File> {
    const {
        fontSize = 16,
        fontColor = '#277EFF',
        backgroundColor,
        backgroundPadding = 10,
        backgroundRadius = 10,
        opacity = 1,
        repeat = true,
        marginBottom = 10,
        marginTop,
        marginRight = 10,
        rotate = -45,
        
        compressionQuality = 0.8, // 默认压缩质量 0.8
    } = options;

    const imageUrl = input instanceof File ? await fileToDataURL(input) : input;
    if (!watermarkText) watermarkText = `拍摄时间：${formatCurDate('YYYY-MM-DD HH:mm:ss')}`;

    return new Promise((resolve, reject) => {
        const image = new Image();
        image.crossOrigin = 'anonymous';
        image.src = imageUrl;
        image.onload = () => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            if (!ctx) {
                reject(new Error('无法获取 Canvas 渲染上下文'));
                return;
            }

            const { width, height } = image;
            canvas.width = width;
            canvas.height = height;

            ctx.drawImage(image, 0, 0, width, height);

            const bgColorData = ctx.getImageData(width / 2, height - 1, 1, 1).data;
            const autoBgColor = `#${Array.from(bgColorData)
                .slice(0, 3)
                .map((c) => c.toString(16).padStart(2, '0'))
                .join('')}`;
            const effectiveBgColor = backgroundColor || getContrastingColor(autoBgColor);

            let fontWidth: number;
            let font: string;
            fontWidth = Math.fround(canvas.width / (40 * 12)) * fontSize;
            font = `${fontWidth}px microsoft yahei`;

            ctx.font = font;
            ctx.textBaseline = 'bottom';

            const metrics = ctx.measureText(watermarkText);
            const textWidth = metrics.width;
            const textHeight = metrics.actualBoundingBoxAscent + metrics.actualBoundingBoxDescent;

            const background_Padding = Math.fround(fontWidth / fontSize) * backgroundPadding;
            const background_Radius = Math.fround(fontWidth / fontSize) * backgroundRadius;

            const bgWidth = textWidth + 2 * background_Padding;
            const bgHeight = textHeight + 2 * background_Radius;

            if (!repeat) {
                const x = width - bgWidth - marginRight;
                const y = marginTop !== undefined ? marginTop : height - marginBottom - bgHeight;

                ctx.fillStyle = effectiveBgColor;
                ctx.globalAlpha = opacity;
                ctx.beginPath();
                ctx.moveTo(x + background_Radius, y);
                ctx.arcTo(x + bgWidth, y, x + bgWidth, y + bgHeight, background_Radius);
                ctx.arcTo(x + bgWidth, y + bgHeight, x, y + bgHeight, background_Radius);
                ctx.arcTo(x, y + bgHeight, x, y, background_Radius);
                ctx.arcTo(x, y, x + bgWidth, y, background_Radius);
                ctx.closePath();
                ctx.fill();

                ctx.fillStyle = fontColor;
                ctx.fillText(watermarkText, x + background_Padding, y + bgHeight - background_Padding);
            } else {
                ctx.save();
                ctx.translate(width / 2, height / 2);
                ctx.rotate((rotate * Math.PI) / 180);
                const forEach_W = Math.fround(width / 6);
                const forEach_H = Math.fround(height / 6);

                for (let x = -width; x < width * 2; x += textWidth + forEach_W) {
                    for (let y = -height; y < height * 2; y += textHeight + forEach_H) {
                        ctx.fillStyle = effectiveBgColor;
                        ctx.globalAlpha = opacity;
                        ctx.beginPath();
                        ctx.moveTo(x - background_Padding + background_Radius, y - textHeight - background_Padding);
                        ctx.arcTo(
                            x - background_Padding + bgWidth,
                            y - textHeight - background_Padding,
                            x - background_Padding + bgWidth,
                            y - textHeight - background_Padding + bgHeight,
                            background_Radius
                        );
                        ctx.arcTo(
                            x - background_Padding + bgWidth,
                            y - textHeight - background_Padding + bgHeight,
                            x - background_Padding,
                            y - textHeight - background_Padding + bgHeight,
                            background_Radius
                        );
                        ctx.arcTo(
                            x - background_Padding,
                            y - textHeight - background_Padding + bgHeight,
                            x - background_Padding,
                            y - textHeight - background_Padding,
                            background_Radius
                        );
                        ctx.arcTo(
                            x - background_Padding,
                            y - textHeight - background_Padding,
                            x - background_Padding + bgWidth,
                            y - textHeight - background_Padding,
                            background_Radius
                        );
                        ctx.closePath();
                        ctx.fill();

                        ctx.fillStyle = fontColor;
                        ctx.fillText(watermarkText, x, y);
                    }
                }

                ctx.restore();
            }

            // **新增：压缩图片**
            let quality = compressionQuality;

            // canvas.toBlob(
            //     (blob) => {
            //         if (blob) {
            //             const file = new File([blob], 'watermarked_image.jpg', { type: 'image/jpeg' });
            //             resolve(file);
            //         } else {
            //             reject(new Error('转换 Blob 失败'));
            //         }
            //     },
            //     'image/jpeg',
            //     compressionQuality // 这里设置压缩质量
            // );
            function attemptCompression() {
                canvas.toBlob(
                    (blob) => {
                        if (blob) {
                            if (blob.size <= 0.5 * 1024 * 1024 || quality < 0.1) {
                                // 如果小于 1MB 或质量已降到最低，则返回文件
                                const file = new File([blob], 'watermarked_image.jpg', { type: 'image/jpeg' });
                                resolve(file);
                            } else {
                                // 继续降低质量并尝试压缩
                                quality -= 0.1;
                                attemptCompression();
                            }
                        } else {
                            reject(new Error('转换 Blob 失败'));
                        }
                    },
                    'image/jpeg',
                    quality
                );
            }

            attemptCompression();

        };

        image.onerror = () => {
            reject(new Error('图片加载失败'));
        };
    });
}

export default addWatermarkToImage;
