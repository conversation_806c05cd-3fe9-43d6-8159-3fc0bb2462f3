.refreshButton {
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  user-select: none;

  i {
    display: grid;
    place-items: center;
    width: 100%;
    height: 100%;
    border-radius: 5px;
    color: #b5b5c3;
    background-color: transparent;
    border-color: transparent;
    cursor: pointer;
    transition: all 0.1s ease-in;
  }
}

.refreshButton i:hover {
  color: #0bb783;
  background-color: #c8d1da;
  border-color: transparent;
}

.refreshButton i:active {
  transform: translateX(3px) translateY(2px);
}

.homeBox {
  background-color: rgb(242, 246, 255, 0.25);
  border-radius: 20px;
  overflow: hidden;
  padding: 15px 30px;
  padding-bottom: 100px;
}

.extraTip {
  font-size: 12px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #ff6e6e;
  line-height: 22px;
  text-align: justify;
}

.eps {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.font-weight-light {
  font-weight: 300 !important;
}

.font-weight-lighter {
  font-weight: lighter !important;
}

.font-weight-normal {
  font-weight: 400 !important;
}

.font-weight-bold {
  font-weight: 500 !important;
  cursor: pointer;
  text-decoration: underline;
}

.font-weight-bolder {
  font-weight: 600 !important;
}

.font-italic {
  font-style: italic !important;
}

.text-white {
  color: #ffffff !important;
}

.text-primary {
  color: #0bb783 !important;
}

a.text-primary:hover,
a.text-primary:focus {
  color: #076f4f !important;
}

.text-secondary {
  color: #e4e6ef !important;
}

a.text-secondary:hover,
a.text-secondary:focus {
  color: #b4bad3 !important;
}

.text-success {
  color: #1bc5bd !important;
}

a.text-success:hover,
a.text-success:focus {
  color: #12827c !important;
}

.text-info {
  color: #8950fc !important;
}

a.text-info:hover,
a.text-info:focus {
  color: #5605fb !important;
}

.text-warning {
  color: #ffa800 !important;
}

a.text-warning:hover,
a.text-warning:focus {
  color: #b37600 !important;
}

.text-danger {
  color: #f64e60 !important;
}

a.text-danger:hover,
a.text-danger:focus {
  color: #ec0c24 !important;
}

.text-light {
  color: #f3f6f9 !important;
}

a.text-light:hover,
a.text-light:focus {
  color: #c0d0e0 !important;
}

.text-dark {
  color: #181c32 !important;
}

a.text-dark:hover,
a.text-dark:focus {
  color: black !important;
}

.text-white {
  color: #ffffff !important;
}

a.text-white:hover,
a.text-white:focus {
  color: #d9d9d9 !important;
}

.text-body {
  color: #3f4254 !important;
}

.text-muted {
  color: #b5b5c3 !important;
}

.text-black-50 {
  color: rgba(0, 0, 0, 0.5) !important;
}

.text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important;
}

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-break {
  word-break: break-word !important;
  word-wrap: break-word !important;
}

.text-reset {
  color: inherit !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

.label-dot {
  line-height: 6px;
  min-height: 6px;
  min-width: 6px;
  height: 6px;
  width: 6px;
  border-radius: 50%;
  display: inline-block;
  font-size: 0 !important;
  vertical-align: middle;
  text-align: center;
  background-color: #0bb783;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;

  .card-label {
    font-size: 24px;
    color: #181c32;
    margin: 0 8px 0 0;
    font-weight: 600;
    word-wrap: break-word;
    // font-style: italic;
  }
}

.hr {
  width: 120%;
  height: 0.5px;
  background: #E9E9E9;
  margin-top: 16px;
  margin-bottom: 20px;
  transform: translate(-10%);
}