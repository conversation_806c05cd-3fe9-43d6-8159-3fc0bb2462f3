<template>
    <div>
        <div class="card-header">
            <div class="selectOption-item" @click="calendarIsShow = true">
                <span>{{ time ? time : '请选择时间' }}</span>
                <van-icon name="play" class="play" color="#1A76FF" />
            </div>
        </div>
        <div class=' container_ mt-6'>
            <div class="container__header">
                <div>
                    <img src="@/assets/sendOrdersInfo.svg" alt="" srcset="">
                    <span class="ml-6">{{ showOrgName }}</span>
                </div>
                <!-- <div>
                    <van-icon name="replay" class="revoke van-haptics-feedback" @click="refreshList" size="14" />
                </div> -->

            </div>
            <div class="container_body  ">
                <van-row gutter="5" class="table-header">
                    <van-col class="row-item" :span="item.span" v-for="item in rowList">{{ item.name }}</van-col>
                </van-row>
                <van-row gutter="5" class="table-body" v-for="col, index in tableData" :key="index">
                    <van-col class="col-item" @click="gotoDetail(col)"
                        :style="{ color: item.props == 'name' || item.props == 'areaNameL2' ? '#1a76ffab' : '' }"
                        :span="item.span" v-for="item in rowList">{{
                            col[item.props] }}</van-col>
                </van-row>
                <van-empty description="暂无数据" v-if="tableData.length == 0" />

            </div>

        </div>

        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />
    </div>
</template>

<script setup lang="ts">
import { getSendOrderDepaAndAreaAPI } from "@/api/home";
import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { useUserInfo } from "@/stores/userInfo";
import { useRoute, useRouter } from "vue-router";

const route = useRoute()
const router = useRouter()
const userInfoStore = useUserInfo();
interface List {
    areaL2: string; // 显示的标签
    areaL2Id: string;

    dispatchNum: number;  // 派单总量
    implementedNum: number;  // 已执行量
    outCallNum: number;  // 外呼量
    pengdingNum: number;  // 待处理量
    successResultNum?: number; // 成功数
    [key: string]: any
}

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    onConfirm,
} = searchHooks({
    source: 'stockList',
    callback: loadSendOrderDepaAndArea
})



const rowList = computed<RowItem[]>(() => {
    let list = [
        {
            span: 5,
            name: '分局',
            props: 'name',
            default: ''
        },
        {
            span: 4,
            name: '派单总量',
            props: 'dispatchNum',
            default: ''

        },
        {
            span: 4,
            name: '已执行量',
            props: 'implementedNum',
            default: 0

        },


        {
            span: 4,
            name: '外呼量',
            props: 'outCallNum',
            default: 0

        },
        {
            span: 4,
            name: '待处理量',
            props: 'pengdingNum',
            default: 0

        },
        {
            span: 3,
            name: '成功数',
            props: 'successResultNum',
            default: 0

        },
    ]



    // return [{
    //     span: 5,
    //     name: curslectArea.value ? '分局' : '区域',
    //     props: 'name',
    //     default: ''
    // }, ...list]
    return list
})


const tableData = ref<List[]>([

])















const showOrgName = computed(() => {
    return userInfoStore.userInfo.orgName
})



// function refreshList() {
//     curslectArea.value = undefined
//     loadSendOrderDepaAndArea()
// }




const time = computed(() => {
    if (formline.startTime && formline.endTime) return `${formline.startTime} ~ ${formline.endTime}`
    else return ``
})
// const curslectArea = ref()
function loadSendOrderDepaAndArea() {
    let params = {
        startTime: formline.startTime ? `${formline.startTime} 00:00:00` : undefined,
        endTime: formline.endTime ? `${formline.endTime} 23:59:59` : undefined,
        // areaId: curslectArea.value
    }
    getSendOrderDepaAndAreaAPI(params).then((res: any) => {
        console.log(res);
        if (res.code == 200) {
            tableData.value = res.data
        }

    })
}


function gotoDetail(row: List) {
    console.log(row);

    // if (row.areaIdL2) {
    router.push({
        path: '/sendOrdersZoneContact',
        query: {
            areaL2Id: row.areaIdL2,
            areaL2Name: row.areaNameL2
        }
    })
    // } else if (row.areaId) {
    //     curslectArea.value = row.areaId
    //     loadSendOrderDepaAndArea()
    // }

}

onMounted(() => {
    defaultDate.value = [
        route.query.startTime ? new Date(String(route.query.startTime)) : new Date(),
        route.query.endTime ? new Date(String(route.query.endTime)) : new Date()
    ]
    formline.startTime = String(route.query.startTime ?? '')
    formline.endTime = String(route.query.endTime ?? '')
    loadSendOrderDepaAndArea()


})
</script>

<style lang="scss" scoped>
.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

    .selectOption-item {
        flex: 1 0 auto;
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
            position: absolute;
            right: 10px;
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }
}


.revoke {
    display: grid;
    place-items: center;
    width: 20px;
    height: 20px;
    border-radius: 5px;
    margin-left: 10px;
    background: #eef5ff;
}


:deep(.van-popover__wrapper) {
    flex: 1 0 auto;
}

:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
    width: auto;
}
</style>