import { RouteRecordRaw } from "vue-router";
import Layout from "@/layout/index.vue";
import { useUserInfo } from "@/stores/userInfo";
/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   如果设置为true, item将不会显示在侧边栏中(默认为false)
 * alwaysShow: true               如果设置为true，将始终显示根菜单
 *                                如果没有设置alwaysShow，当item有多个子路由时,它将成为嵌套模式，否则不会显示根菜单
 * 
 * affix                          如果没有设置affix,tagsView 可以删除该路由。
 * 
 * redirect: noRedirect           如果设置noRedirect将不会在面包屑中重定向
 * name:'router-name'             名称被<keep-alive>使用(必须设置!!)
 * meta : {
    roles: ['admin','editor']    控制页面角色(可以设置多个角色)
    title: 'title'               名称显示在侧边栏和面包屑(推荐设置)
    icon: 'svg-name'/'el-icon-x' 图标显示在侧边栏中  如果使用element-plus的图标 需要在 名字前面加上 el-  例如 el-Plus
    breadcrumb: false            如果设置为false，则该项将隐藏在面包屑中(默认为true)
    activeMenu: '/example/list'   如果设置了路径，则侧边栏将突出显示所设置的路径
    size:18                      控制菜单图标大小 number值 不设置默认大小为18px
  }
 */


// 扩展 RouteMeta 接口
declare module "vue-router" {

  interface RouteMeta {
    title?: string;
    isLink?: string;
    hidden?: boolean;
    isKeepAlive?: boolean;
    isAffix?: boolean;
    isIframe?: boolean;
    roles?: string[];
    icon?: string;
  }
}



/**
 * 定义动态路由
 * 前端添加路由，请在顶级节点的 `children 数组` 里添加
 * @description 未开启 isRequestRoutes 为 true 时使用（前端控制路由），开启时第一个顶级 children 的路由将被替换成接口请求回来的路由数据
 * @description 各字段请查看 `/@/views/system/menu/component/addMenu.vue 下的 ruleForm`
 * @returns 返回路由菜单数据
 */
export const dynamicRoutes: Array<RouteRecordRaw> = [
  // {
  //   path: "/",
  //   redirect: "/home",
  //   component: Layout,
  //   meta: { title: "首页", icon: "el-Handbag" },
  //   children: [
  //     {
  //       path: "home",
  //       name: "home",
  //       component: () => import("@/views/home/<USER>"),
  //       meta: { title: "首页", size: "18", icon: "el-Handbag", affix: true },
  //     },
  //   ],
  // },
  {
    path: "/",
    // redirect: (to) => {
    //   const userInfoStore = useUserInfo();
    //   const roles = userInfoStore.userInfo.roles;
    //   if (roles.includes('department_dispatch')) {
    //     return '/activityList';
    //   } else if (roles.includes('area_dispatch')) {
    //     return '/seedOrderList';
    //   }
    //   return '/activityList'; // 默认重定向
    // },
    component: Layout,
    meta: { title: "活动", icon: "el-Handbag", },
    children: [
      {
        path: "/activityList",
        name: "activityList",
        component: () => import("@/views/activity/activityList.vue"),
        meta: { title: "活动列表", size: "18", icon: "el-Handbag", affix: true, roles: ['department_dispatch'] },
      },
      {
        path: "/seedOrderList",
        name: "seedOrderList",

        component: () => import("@/views/activity/seedOrderList.vue"),
        meta: { title: "派单列表", size: "18", icon: "el-Handbag", affix: true, roles: ['department_dispatch', 'area_dispatch'] },
      },
      {
        path: "/editOrder",
        name: "editOrder",

        component: () => import("@/views/activity/editOrder.vue"),
        meta: { title: "编辑派单", size: "18", icon: "el-Handbag", affix: true, roles: ['department_dispatch'], hidden: true },
      },
      {
        path: "/receiptStatistics",
        name: "receiptStatistics",

        component: () => import("@/views/activity/receiptStatistics.vue"),
        meta: { title: "派单回单统计", size: "18", icon: "el-Handbag", affix: true, hidden: true },
      },
    ],
  },
  {
    path: "/",
    component: Layout,
    meta: { title: "路由配置", icon: "el-Handbag", },
    children: [
      {
        path: "/routerList",
        name: "routerList",

        component: () => import("@/views/RouteConfig/index.vue"),
        meta: { title: "路由配置", size: "18", icon: "el-Handbag", affix: true, hidden: false, },
      },
    ],
  },

];

/**
 * 定义404、401界面
 * @link 参考：https://next.router.vuejs.org/zh/guide/essentials/history-mode.html#netlify
 */
export const notFoundAndNoPower = [
  {
    path: "/:path(.*)*",
    name: "notFound",

    component: () => import("@/views/error/404.vue"),
    meta: {
      title: "404",
      hidden: true,
    },
  },
];

/**
 * 定义静态路由（默认路由）
 * 此路由不要动，前端添加路由的话，请在 `dynamicRoutes 数组` 中添加
 * @description 前端控制直接改 dynamicRoutes 中的路由，后端控制不需要修改，请求接口路由数据时，会覆盖 dynamicRoutes 第一个顶级 children 的内容（全屏，不包含 layout 中的路由出口）
 * @returns 返回路由菜单数据
 */
export const staticRoutes: Array<RouteRecordRaw> = [
  {
    path: "/login",
    name: "login",
    component: () => import("@/views/login.vue"),
    meta: {
      title: "登录", hidden: true
    },
  },
  {
    path: "/loading",
    name: "loading",
    component: () => import("@/views/loading.vue"),
    meta: {
      title: "加载", hidden: true
    },
  },
];
