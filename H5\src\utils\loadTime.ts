// 2.获取几天之前的日期 传入date和天数
export function constgetBeforeDayDate(date: Date | string, day: number) {
    if (typeof date == 'string') date = new Date(date)
    let timestamp = date.getTime();
    // 获取day天前的日期
    return new Date(timestamp - day * 24 * 3600 * 1000);
}

//获取几天后的日期
export function getAfterDayDate(date: Date | string, day: number) {
    if (typeof date == 'string') date = new Date(date)

    let timestamp = date.getTime();
    // 获取day天之后的日期
    return new Date(timestamp + day * 24 * 3600 * 1000);
}

export function formatDate(date: Date | string, fmt: string) {
    if (typeof date == 'string') date = new Date(date)
   
    var o:any = {
        "M+": date.getMonth() + 1, //月份
        "d+": date.getDate(), //日
        "h+": date.getHours(), //小时
        "m+": date.getMinutes(), //分
        "s+": date.getSeconds(), //秒
        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
        S: date.getMilliseconds(), //毫秒
    };
    if (/(y+)/.test(fmt))
        fmt = fmt.replace(
            RegExp.$1,
            (date.getFullYear() + "").substr(4 - RegExp.$1.length)
        );
    for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt))
            fmt = fmt.replace(
                RegExp.$1,
                RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
            );
    return fmt;
}

export function formatCurDate(format:string) {
    const date = new Date();
    const map:any = {
        'YYYY': date.getFullYear(),
        'MM': String(date.getMonth() + 1).padStart(2, '0'),
        'DD': String(date.getDate()).padStart(2, '0'),
        'HH': String(date.getHours()).padStart(2, '0'),
        'mm': String(date.getMinutes()).padStart(2, '0'),
        'ss': String(date.getSeconds()).padStart(2, '0'),
    };

    return format.replace(/YYYY|MM|DD|HH|mm|ss/g, matched => map[matched]);
}