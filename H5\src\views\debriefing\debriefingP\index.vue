<template>
  <div class="card container_">
    <div class="container__header">
      <div>
        <img src="../../template/MyDay/assets/images/fpIcon.png" alt="" srcset="" class="card_icon" />
        <span class="ml-6">复盘</span>
      </div>
      <div style="display: flex;align-items: center;">
        <span class="fs13 mr-3">语音转换</span>
        <van-switch v-model="switch_" size="15px" :disabled="recording" />
      </div>
    </div>
    <div class="form_card">
      <van-form required="auto" ref="formField" :show-error-message="true">
        <van-cell-group>
          <van-field name="uploader" label="复盘照" label-align="left" input-align="right"
            :rules="[{ required: formData.description?.trim() ? false : true, message: '请拍摄照片' }]">
            <template #input>
              <div class="right_align">
                <van-uploader :after-read="handleAfterRead" :before-read="handleBeforeRead" v-model="formData.fileList"
                  capture="camera" preview-size="89" :max-count="3">
                  <template #default>
                    <div class="form_card_camera">
                      <van-image width="28px" height="23px" :src="camera" />
                    </div>
                  </template>
                </van-uploader>
              </div>
            </template>
          </van-field>

          <van-field v-loading="loadingConfig" v-model="formData.description"
            :rules="[{ required: formLine.images.length > 0 ? false : true, message: '请输入留言' }]" label-align="left"
            autosize label="备注" type="textarea" maxlength="100" placeholder="请输入留言" />
        </van-cell-group>







        <van-field input-align="right">
          <template #input>
            <span @click="navTo" class="sign_list">复盘记录></span>
          </template>
        </van-field>
      </van-form>
    </div>


    <div class="container_body" v-if="switch_">
      <div style="display: flex;flex-direction: column;align-items: center;">
        <audioLoading class="mb-10" v-if="recording && !isPaused" />
        <div class="fs12 mb-8" style="color: #666;" v-if="recording">当前录制时间：{{ currentTime }}s</div>

      </div>
      <div class=" mr-10 mt-10" style="display: flex;align-items: center; justify-content: center;">
        <van-button style="margin-right: 5px;" type="primary" @click="cancelRecording" size='mini' v-if="recording">
          取消录音
        </van-button>
        <i class="icon-maikefeng-XDY ml-10 mr-10" :style="{
          color: recording && !isPaused ? '#558DFD' : '#666'
        }" style="font-size: 45px;" @click="handleMicrophoneClick"></i>
        <van-button style="margin-left: 5px;" type="danger" @click="stopRecording" v-if="recording" size='mini'>
          停止录音
        </van-button>
      </div>
      <div class="fs12 mt-3 mb-3" style="color: #666;list-style: none;">

        <li> <van-icon name="info-o" />
          <span class="ml-2">语音录制请限制在2分钟以内,两分钟后将自动结束录制。</span>
        </li>
      </div>
      <!-- <van-field v-loading="loadingConfig" :readonly="true" style="background-color: #f3f6f9c2; border-radius: 10px;"
        class="mt-20" v-model="messageCallBack" rows="3" autosize type="textarea"
        v-if="messageCallBack || loadingConfig.visible" /> -->
    </div>


    <div class="sure_btn">
      <van-button type="primary" color="  linear-gradient( 90deg, #73ACFF 0%, #237CFF 100%)" block @click="submit"
        :disabled="!isSubmitAble">确认</van-button>
    </div>



  </div>
</template>

<script setup lang="ts">
import { UploaderFileListItem } from "vant";
import audioLoading from "@/components/loading/audio.vue";
import { uploadAudioHooks } from "@/hooks_modules/upload_audio_hooks";
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast, showSuccessToast, showFailToast } from "vant";
import camera from "../../template/MyDay/assets/images/camera.png";
import { uploadFileHooks } from "@/hooks_modules/upload_hooks";
import { reviewSaveAPI } from "@/api/review";
import { debounce } from "@/utils/debounce";
const router = useRouter();
let formField = ref();
const switch_ = ref(true)    //是否开启 语音转文本




const {
  startRecording,
  stopRecording,
  pauseRecording,
  resumeRecording,
  uploadAudio,
  cancelRecording,
  recording,
  isPaused,
  audioUrl,
  currentTime,
  loadingConfig,
  messageCallBack,
  reqId
} = uploadAudioHooks({
  recordingTime: 2 * 60
})


const {
  handleAfterRead,
  handleBeforeRead,
  formLine,
  formData,
} = uploadFileHooks()

const isSubmitAble = computed(() => {
  if (switch_.value) {
    return reqId.value != ''
  } else {
    return true
  }

})


const submit = debounce(() => {
  formField.value
    .validate()
    .then(() => {
      let params = {
        images: formLine.images.join(','),
        description: formData.description,
        reqId: switch_.value ? reqId.value : undefined
      }
      reviewSaveAPI(params).then((res: any) => {
        if (res.code == 200) {
          showSuccessToast("提交成功");
          router.push({ path: '/DebriefingDiary' })
        } else {
          showFailToast("提交失败");
        }

      })

    })
    .catch((err: any) => {
      showFailToast("请完善信息");
    });
}, 300, true)




const navTo = () => {
  router.push("/DebriefingDiary");
};

// 修改麦克风图标点击处理方法
const handleMicrophoneClick = async () => {
  if (!recording.value) {
    // 如果未开始录制，则开始录制
    await startRecording();
  } else if (isPaused.value) {
    // 如果处于暂停状态，则继续录制
    resumeRecording();
  } else {
    // 如果正在录制，则暂停录制
    pauseRecording();
  }
}
watch(() => messageCallBack.value, (newValue) => {
  formData.description += newValue
})
</script>

<style lang="scss" src="../formCard.scss" scoped></style>
<style>
.audioLoading.van-toast {
  background-color: #edf5ff !important;
  border-radius: 15px;
  padding: 20px;
}
</style>
