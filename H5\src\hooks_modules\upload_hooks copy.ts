import { ref, reactive } from "vue";
import { uploadFileAPI, deleteFile, byImageUrlQueryTextAPI, byImageUrlChatPictureTextAPI } from "@/api/common";
import addWatermarkToImage from "@/utils/canvas_addWatermark.ts";
import { showToast, UploaderFileListItem } from "vant";

type FormEntry = {
    fileList: UploaderFileListItem[],
    address?: string,
    description?: string,
}

interface uploadFileType {
    images: string[],
    shouldNumber?: number | string, //应到人数
    realNumber?: number | string,  //实到人数
    reason?: string      //事由
}
interface Params {
    enableTextRecognition?: boolean
    enableAddWatermarkToImage?: boolean
}

export const uploadFileHooks = function ({ enableTextRecognition = false, enableAddWatermarkToImage = true }: Partial<Params> = {}) {
    const formData = reactive<FormEntry>({
        fileList: [],
        address: '',
        description: "",
    })
    const formLine = reactive<uploadFileType>({
        images: [],
        shouldNumber: '',
        realNumber: '',
        reason: '',
    });
    const curSelectType = reactive({
        text: '详情-72B',
        value: '详情-72B',
        API: byImageUrlQueryTextAPI
    })
    const modelList = ref([
        {
            text: '详情-72B',
            value: '详情-72B'
        },
        {
            text: '概要-72B',
            value: '概要-72B'
        },
    ])

    function onSelect(params: any) {

        curSelectType.text = params.text
        curSelectType.value = params.value
        if (params.text == '详情-72B') {
            curSelectType.API = byImageUrlQueryTextAPI
        } else {
            curSelectType.API = byImageUrlChatPictureTextAPI
        }
    }


    const imageTransitionTextList = ref<{ url: string; visible: boolean, ImageText: string; text: string, error: string | null }[]>([
    ]);


    const uploadLoading = ref(false);
    const { VITE_IMAGE_URL } = import.meta.env


    const pollForTextRecognition = (url: string) => {
        const item = {
            url, visible: true, ImageText: '', error: '',
            text: '识别中，请稍等...',
            textSize: '12',
            iconSize: '24',
            iconColor: '#fff',
            borderRadius: '10px',
            zIndex: 100
        };
        imageTransitionTextList.value.push(item);
        curSelectType.API({ picUrl: url }).then((res: any) => {
            // byImageUrlQueryTextAPI({ picUrl: url }).then((res: any) => {
            const index = imageTransitionTextList.value.findIndex(item => item.url == url)

            if (index > -1) {
                if (res.code == 200) {
                    imageTransitionTextList.value[index].ImageText = res.data ? res.data : '未识别到图片中比算信息'
                } else {
                    imageTransitionTextList.value[index].ImageText = '识别异常'
                }

            }

        }).catch((error) => {
            const index = imageTransitionTextList.value.findIndex(item => item.url == url)
            imageTransitionTextList.value[index].ImageText = '识别异常'
        }).finally(() => {
            const index = imageTransitionTextList.value.findIndex(item => item.url == url)
            if (index > -1) {
                imageTransitionTextList.value[index].visible = false
            }
        })
    };



    const handleAfterRead = (file: any) => {

        file.status = "uploading";
        file.message = "上传中...";

        const formData = new FormData();
        formData.append("file", file.file);
        uploadLoading.value = true
        uploadFileAPI(formData).then((res: any) => {
            file.status = "success";
            file.message = "上传成功";
            let imageUrl = VITE_IMAGE_URL + res.data.name;
            formLine.images.push(imageUrl);
            if (enableTextRecognition) {
                pollForTextRecognition(imageUrl);
            }

        }).catch((error) => {
            file.status = "failed";
            file.message = "上传失败";

        }).finally(() => {
            uploadLoading.value = false
        })

    };
    const handleBeforeRead = (file: any) =>
        new Promise<File | File[] | undefined>((resolve, reject) => {
            if (["image/png", "image/jpeg", "image/jpg",].includes(file.type)) {
                if (enableAddWatermarkToImage) {
                    addWatermarkToImage(file, { repeat: false, }).then((watermarkedFile) => {
                        resolve(watermarkedFile)


                    }).catch((error) => {
                        reject('水印生成失败');
                        console.error('水印生成失败:', error);
                    });
                } else {
                    resolve(file)
                    console.log(file);

                }


            } else {
                showToast("请上传png,jpeg,jpg,webp文件");
                reject();
            }
        });


    // function inputFile(event: Event) {
    //     const input = event.target as HTMLInputElement;
    //     if (input.files && input.files[0]) {
    //         console.log(input.files[0]);
    //         const file = input.files[0]
    //         addWatermarkToImage(file, { repeat: false, }).then((watermarkedFile) => {
    //             const formData = new FormData();
    //             formData.append("file", watermarkedFile);
    //             uploadLoading.value = true
    //             uploadFileAPI(formData).then((res: any) => {

    //                 let imageUrl = VITE_IMAGE_URL + res.data.name;
    //                 formLine.images.push(imageUrl);
    //                 if (enableTextRecognition) {
    //                     pollForTextRecognition(imageUrl);
    //                 }

    //             }).catch((error) => {

    //             }).finally(() => {
    //                 uploadLoading.value = false
    //             })

    //         })
    //     }
    // }

    function deleteFile(file: any, otherObj: any) {
        formLine.images.splice(otherObj.index, 1)
        imageTransitionTextList.value.splice(otherObj.index, 1)
        return true
    }


    return {
        handleAfterRead,
        handleBeforeRead,
        deleteFile,
        // inputFile,
        onSelect,
        uploadLoading,
        formLine,
        formData,
        imageTransitionTextList,
        curSelectType,
        modelList
    }

}


