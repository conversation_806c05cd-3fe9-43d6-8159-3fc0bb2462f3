import { App, DirectiveBinding, createVNode, render } from 'vue';
import { Loading } from 'vant'; // 引入 Vant 的 Loading 组件

// 扩展 HTMLElement 接口，添加自定义属性
interface ExtendedHTMLElement extends HTMLElement {
    __loadingOverlay__?: HTMLDivElement;
    __updateLoading__?: () => void;
}

// 创建一个名为 v-loading 的自定义指令
const loadingDirective = {
    mounted(el: ExtendedHTMLElement, binding: DirectiveBinding) {
        el.style.position = 'relative';

        // 创建加载遮罩元素
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = `loading-overlay ${binding.value.customClass || ''}`;

        // 设置遮罩的样式
        loadingOverlay.style.position = 'absolute';
        loadingOverlay.style.top = '0';
        loadingOverlay.style.left = '0';
        loadingOverlay.style.width = '100%';
        loadingOverlay.style.height = '100%';
        loadingOverlay.style.backgroundColor = binding.value.backgroundColor || 'rgba(0, 0, 0, 0.7)';
        loadingOverlay.style.color = '#fff';
        loadingOverlay.style.display = 'flex';
        loadingOverlay.style.alignItems = 'center';  // 垂直居中
        loadingOverlay.style.justifyContent = 'center'; // 水平居中
        loadingOverlay.style.zIndex = binding.value.zIndex || 999; // 确保遮罩在最上层
        loadingOverlay.style.borderRadius = binding.value.borderRadius || '0'; // 设置边框圆角

        // 加载内容容器
        const loadingContent = document.createElement('div');
        loadingContent.style.display = 'flex'; // 使用 flex 布局
        loadingContent.style.flexDirection = 'column'; // 垂直排列
        loadingContent.style.alignItems = 'center'; // 水平居中对齐
        loadingContent.style.justifyContent = 'center'; // 垂直居中对齐

        // 加载图标
        const loadingIcon = document.createElement('div');
        loadingIcon.className = `loading-icon ${binding.value.spinner || ''}`;
        loadingIcon.style.fontSize = binding.value.iconSize || '24px'; // 自定义图标大小

        // 处理图标：如果提供了自定义图标，则使用它；否则使用 Vant 的 Loading 组件
        let iconVNode;

        if (typeof binding.value.icon === 'string') {
            // 如果 icon 是字符串，直接插入 HTML
            loadingIcon.innerHTML = binding.value.icon;
        } else if (binding.value.icon) {
            // 如果 icon 是组件，使用 createVNode 进行渲染
            iconVNode = createVNode(binding.value.icon, {
                color: binding.value.iconColor || '#1989fa', // 自定义图标颜色
                style: { fontSize: binding.value.iconSize || '24px' }, // 自定义图标大小
            });
            render(iconVNode, loadingIcon); // 渲染到 loadingIcon
        } else {
            // 默认使用 Vant 的 Loading 组件
            iconVNode = createVNode(Loading, {
                type: 'spinner', // 默认类型
                color: binding.value.iconColor || '#1989fa', // 默认颜色
                size: binding.value.iconSize || '24px', // 默认大小
            });
            render(iconVNode, loadingIcon); // 渲染 Vant 的 Loading 组件
        }

        // 加载文本
        const loadingText = document.createElement('div');
        loadingText.className = 'loading-text';
        loadingText.innerText = binding.value.text || 'Loading...'; // 默认文本
        loadingText.style.color = binding.value.textColor || '#fff'; // 自定义文字颜色
        loadingText.style.fontSize = binding.value.textSize || '16px'; // 自定义文字大小

        // 将图标和文本添加到内容容器
        loadingContent.appendChild(loadingIcon);
        loadingContent.appendChild(loadingText);

        // 将内容容器添加到遮罩
        loadingOverlay.appendChild(loadingContent);

        // 根据 binding 的值决定是否显示遮罩
        const updateLoading = () => {
            if (binding.value.visible) {
                if (!el.contains(loadingOverlay)) {
                    if (binding.modifiers.body) {
                        document.body.appendChild(loadingOverlay); // 添加到 body 中
                    } else {
                        el.appendChild(loadingOverlay); // 默认插入到元素内部
                    }
                    if (binding.value.lock) {
                        document.body.style.overflow = 'hidden'; // 锁定滚动
                    }
                }
            } else {
                if (loadingOverlay.parentNode) {
                    loadingOverlay.parentNode.removeChild(loadingOverlay);
                    if (binding.value.lock) {
                        document.body.style.overflow = ''; // 解锁滚动
                    }
                }
            }
        };

        // 初始化遮罩状态
        updateLoading();

        // 监听绑定值的变化
        el.__loadingOverlay__ = loadingOverlay; // 保存遮罩以便后续操作
        el.__updateLoading__ = updateLoading;

        // 检查 binding.instance 是否存在
        if (binding.instance) {
            binding.instance.$watch(
                () => binding.value.visible,
                updateLoading,
            );
        }
    },

    unmounted(el: ExtendedHTMLElement) {
        if (el.__loadingOverlay__) {
            el.__loadingOverlay__.parentNode?.removeChild(el.__loadingOverlay__);
        }
        if (el.__updateLoading__) {
            el.__updateLoading__(); // 清理观察者
        }
        delete el.__loadingOverlay__;
        delete el.__updateLoading__;
    }
};

// 挂载指令到 Vue 应用
export default {
    install(app: App) {
        app.directive('loading', loadingDirective);
    }
};
