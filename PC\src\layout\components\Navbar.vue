<template>
  <div class="navbar">
    <div>
      <Hamburger_ :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
      <Breadcrumb_></Breadcrumb_>
    </div>

    <div class="right-menu">


      <el-dropdown class="avatar-container" trigger="click">
        <div class="avatar-wrapper">
          <span class="dropdownName fs16" style="font-weight: bold; font-style: italic;"> {{ userInfo.staffName }}</span>
          <el-icon :size="20">
            <ArrowDown />
          </el-icon>
        </div>
        <template #dropdown>
          <el-dropdown-menu class="user-dropdown">
            <el-dropdown-item divided @click="logout">
              <span style="display: block">退出</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

  </div>
</template>

<script setup>
import Breadcrumb_ from "@/components/Breadcrumb_/index.vue";
import Hamburger_ from "@/components/Hamburger_/index.vue";
import { useSidebarStore } from "@/stores/layoutSetting/app.ts";
import { ref, reactive, computed, toRefs, watch, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useUserInfo } from "@/stores/userInfo";
const SidebarState = useSidebarStore()

const router = useRouter();
const route = useRoute();
const userInfoStore = useUserInfo()

const userInfo = computed(() => {
  return userInfoStore.userInfo
})











const sidebar = computed(() => SidebarState.sidebar);





function toggleSideBar() {
  SidebarState.toggleSidebar()
}

async function logout() {

  router.replace(`/login?redirect=${route.fullPath}`);





}





onMounted(() => {

})

</script>

<style lang="scss" scoped>
.my_auth_dialog {
  .dialog_title {
    font-size: 30px;
    height: 50px;
  }
}


.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  justify-content: space-between;

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .right-menu {
    height: 100%;
    line-height: 50px;
    display: flex;
    align-items: center;

    .dropdownName {
      //   color: black;
      cursor: default;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      width: 100px;
      height: 50px;
      line-height: 50px;
      flex: 0 0 auto;
      text-align: center;
      font-weight: 600;
    }

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;
        display: flex;
        align-items: center;
        cursor: pointer;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .top_user {
          font-size: 16px;
          margin: 0 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
