<template>
    <div class='mt-16 container_' v-loading="loadingConfig">
        <div class="container__header">

            <div style="display: flex;align-items: center;justify-content: space-between;">
                <div class="Tabs" id="Tabs">
                    <cusTabs :tabList="tabList" v-model:curSelectTab="curSelectTab" @changeSelectTab="changeSelectTab"
                        style="flex: 1 0 auto;">
                    </cusTabs>
                    <van-popover placement="bottom">
                        <div class="popoverBody">
                            <li>活力&积分情况:</li>
                            <li>统计人员为除'分局长','副分局长','客经','客经主管','门店经理'和"全区"、"部门领导"、"单元领导"之外的角色</li>
                        </div>
                        <template #reference>
                            <van-icon class="ml-3" name="question" color="#1A76FF" style="flex: 1 0 auto;" />
                        </template>
                    </van-popover>

                </div>
                <div style="display: flex;align-items: center;justify-content: flex-end; flex: 0 0 auto;">
                    <img src="./assets//download.png" class="mr-6" style="width: 17px ;height: 17px;" alt="" srcset=""
                        @click="exportExcel"
                        v-if="listType == 0 && curSelectTab == 1 && exportWhiteList.includes(staffCode)">
                    <template v-if="curSelectTab == 0">
                        <div class="selectOption">
                            <van-popover :actions="dayOrMonthList" @select="onSelect" placement="bottom-end">
                                <template #reference>
                                    <div class="selectOption-item">
                                        <span>{{ dayOrMonth.text }}</span>
                                        <van-icon name="play" class="play" color="#1A76FF" />
                                    </div>
                                </template>
                            </van-popover>
                        </div>
                    </template>
                    <template v-else>
                        <div style="display: flex;align-items: center; ">
                            <div class="selectOption">
                                <van-popover :actions="selectOptionList" @select="changeSelete" placement="bottom-end">
                                    <template #reference>
                                        <div class="selectOption-item">
                                            <span>{{ curSelect.text }}</span>
                                            <van-icon name="play" class="play" color="#1A76FF" />
                                        </div>
                                    </template>
                                </van-popover>
                            </div>
                            <img class="ml-10" src="@/assets/calendar.svg" alt="" srcset=""
                                @click.stop="calendarIsShow = true">
                        </div>

                    </template>

                </div>
            </div>

            <div style="text-align: right;width: 100%;" v-if="curSelectTab == 3">
                <div class="selectOption">
                    <van-popover :actions="customerVisitOptionList" @select="changeCustomerVisit" placement="bottom-end"
                        class="customerVisitPopover">
                        <template #reference>
                            <div class="selectOption-item">
                                <span>{{ curCustomerVisit }}</span>
                                <van-icon name="play" class="play" color="#1A76FF" />
                            </div>
                        </template>
                    </van-popover>
                </div>
            </div>

        </div>

        <van-pull-refresh v-model="loading" @refresh="onRefresh">
            <div class="container_body activity-container" ref="activityContainer">
                <van-row gutter="5" class="table-header">
                    <van-col class="row-item" :class="item.isStatic ? 'static-col-item' : ''" :span="item.span"
                        v-for="item in rowList" v-html="item.name">

                    </van-col>
                    <div v-if="showRightArrow && curSelectTab == 1" class="right-arrow" @click="scrollToRight">
                        <van-icon name="arrow-double-right" size="16" color="#A3CFFF" />
                    </div>
                </van-row>

                <van-row gutter="5" align="center" class="table-body" v-for="col, index in tableData" :key="col.id"
                    @click="handleClick(col)">
                    <van-col class="col-item" :class="[
                        item.isStatic ? 'static-col-item' : '',
                        col[`color${item.props}`],
                        item.props == 'name' && listType !== 2 && !col.isTotal ? 'active-col-item' : '',
                        col.isTotal ? 'total-row' : ''
                    ]" v-getName="{ item: col, props: item.props }" :span="item.span" v-for="item in rowList">
                        {{ item.props && item.toFixed ? Number(col[item.props] || 0).toFixed(item.toFixed) :
                            col[item.props] ||
                            0 }}{{ item.unit }}
                    </van-col>
                </van-row>




            </div>

            <van-empty description="暂无数据" v-if="tableData.length == 0" />
        </van-pull-refresh>
        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />
    </div>
</template>

<script setup lang='ts'>
import { formatDate, constgetBeforeDayDate } from "@/utils/loadTime";
import { ref, reactive, onMounted, onUnmounted, computed, toRefs, ComputedRef } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { AreaPriorityMap, rolePriorityMap } from "@/utils/mapSort";
import { getDevelopmentAreaAndDeptAPI, getDevelopmentEnergyAndPointsAPI, getDevelopmentbusinessOperationAPI } from "@/api/userData";
import cusTabs from "@/components/cusTabs/index.vue";
import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { exportJsonToExcel } from "@/utils/exportJsonToExcel";
import { useUserInfo } from "@/stores/userInfo";

interface RowItem {
    span: number;
    name: string;
    props: string;
    default: any;
    id?: string;
    staffName?: string;
    isTotal?: boolean;
    [key: string]: any;
}

interface ColumnItem {
    span: number;
    name: string;
    props: string;
    default: any;
    toFixed?: number;
    unit?: string;
    isStatic?: boolean;
}

const userInfoStore = useUserInfo()

const exportWhiteList = ['71096876', '0913468', '0915034', '0914948', '0913992']

const staffCode = computed(() => {
    return userInfoStore.userInfo.staffCode || ''
})

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    onConfirm,

} = searchHooks({
    source: 'developmentAreaAndDept',
    callback: confirm_Time,
})
const curSelect = reactive({
    text: '当日',
    value: '当日',

})
const selectOptionList = ref([
    { text: '当日', value: '当日' },
    { text: '近一周', value: '近一周' },
    { text: '当月', value: '当月' },
])

function changeSelete(params: any) {
    if (curSelect.value == params.value) return
    curSelect.value = params.value
    curSelect.text = params.text

    let startTime: Date
    switch (params.value) {
        // 今天
        case '当日':
            formline.startTime = formatDate(new Date(), 'yyyy-MM-dd')
            formline.endTime = formatDate(new Date(), 'yyyy-MM-dd')
            defaultDate.value = [new Date(), new Date()]
            break;
        // 当月
        case '当月':
            startTime = new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            formline.startTime = formatDate(startTime, 'yyyy-MM-dd')
            formline.endTime = formatDate(new Date(), 'yyyy-MM-dd')
            defaultDate.value = [startTime, new Date()]
            break;
        // 近一周
        case '近一周':
            startTime = constgetBeforeDayDate(new Date(), 6)
            formline.startTime = formatDate(startTime, 'yyyy-MM-dd')
            formline.endTime = formatDate(new Date(), 'yyyy-MM-dd')
            defaultDate.value = [startTime, new Date()]
            break;
    }
    loadDevelopmentLeader()
}
//确定选择时间 
function confirm_Time() {

    getTimeRangeLabel(formline.startTime, formline.endTime)

    const { text, value } = getTimeRangeLabel(formline.startTime, formline.endTime)
    curSelect.text = text
    curSelect.value = value
    loadDevelopmentLeader()
}


function getTimeRangeLabel(startDate: string, endDate: string): { text: string; value: string } {
    const now = stripTime(new Date()); // 当前日期，仅保留日期部分
    const start = stripTime(new Date(startDate));
    const end = stripTime(new Date(endDate));

    const diffInDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1; // 包含起始日期

    if (diffInDays === 1 && isSameDay(start, now) && isSameDay(end, now)) {
        return {
            text: '当日',
            value: '当日'
        };
    } else if (isCurrentMonthRange(start, end, now)) {
        return {
            text: '当月',
            value: '当月'
        };
    } else if (diffInDays === 7 && isLastWeekRange(start, end, now)) {
        return {
            text: '近一周',
            value: '近一周'
        };
    } else {
        return {
            text: '自选',
            value: '自选'
        };
    }
}

// 辅助函数：移除时间部分，只保留日期
function stripTime(date: Date): Date {
    return new Date(date.getFullYear(), date.getMonth(), date.getDate());
}

// 辅助函数：判断两日期是否为同一天
function isSameDay(date1: Date, date2: Date): boolean {
    return date1.getTime() === date2.getTime();
}

// 辅助函数：判断是否为当月范围
function isCurrentMonthRange(start: Date, end: Date, now: Date): boolean {
    const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    return (
        isSameDay(start, firstDayOfMonth) && // 开始日期是当月第一天
        isSameDay(end, now) // 结束日期是今天
    );
}

// 辅助函数：判断范围是否为"近一周"
function isLastWeekRange(start: Date, end: Date, now: Date): boolean {
    const sevenDaysAgo = new Date(now);
    sevenDaysAgo.setDate(now.getDate() - 6); // 计算7天前的日期

    return (
        isSameDay(end, now) && // 结束日期是今天
        isSameDay(start, sevenDaysAgo) // 开始日期是7天前
    );
}



const curCustomerVisit = ref('全市视图')
const customerVisitOptionList = ref([
    { text: '全市视图', value: '全市视图' },
    { text: '客户拜访TOP榜', value: '客户拜访TOP榜' },
    { text: '活力值TOP榜', value: '活力值TOP榜' },
])
function changeCustomerVisit(params: any) {
    curCustomerVisit.value = params.text
}



const loadingConfig = reactive<LoadingDirectiveOptions>({
    visible: false, // 控制加载状态
    text: '请稍等...', // 自定义加载文本
    iconSize: '36',
    iconColor: '#fff',
    borderRadius: '10px',
});

const rowList = computed(() => {
    let list: ColumnItem[] = []
    if (curSelectTab.value == 0) {
        list = [
            {
                span: 4,
                name: '积分',
                props: 'points',
                default: 0

            },
            {
                span: 3,
                name: '天翼',
                props: 'tianYi',
                default: 0

            },
            {
                span: 3,
                name: '宽带',
                props: 'kuanDai',
                default: 0
            },
            {
                span: 3,
                name: '融合',
                props: 'ronghe',
                default: 0
            },
            {
                span: 4,
                name: '新装高套',
                props: 'xinliangZgz',
                default: 0

            },]

        if (listType.value == 0) {
            list.unshift({
                span: 7,
                name: '区域',
                props: 'name',
                default: '',
                isStatic: true
            })
        } else if (listType.value == 1) {
            list.unshift({
                span: 7,
                name: '分局',
                props: 'name',
                default: '',
                isStatic: true
            })

        } else if (listType.value == 2) {
            list.unshift(
                {
                    span: 4,
                    name: '姓名',
                    props: 'staffName',
                    default: '',
                    isStatic: true
                },

                {
                    span: 3,
                    name: '角色',
                    props: 'primaryRoleName',
                    default: ''

                },)
        }
    } else if (curSelectTab.value == 1) {
        list = [
            {
                span: 4,
                name: '0积分<br>人数',
                props: 'zeroPointsCount',
                default: 0,



            },
            {
                span: 4,
                name: '0积分<br>占比',
                props: 'zeroPointsPercent',
                default: 0,
                unit: '%',
                toFixed: 1,
            }, {
                span: 4,
                name: '0活力<br>人数',
                props: 'zeroHuoliCount',
                default: 0,

            },

            {
                span: 4,
                name: '0活力<br>占比',
                props: 'zeroHuoliPercent',
                default: 0,
                unit: '%',
                toFixed: 1,

            },
            {
                span: 4,
                name: '人均<br>日活力',
                props: 'huoliDayPercent',
                default: 0,
                toFixed: 1,

            },
            {
                span: 4,
                name: '人均<br/>商机',
                props: 'businessDayPercent',
                default: 0,
                toFixed: 2,

            },
        ]
        if (listType.value == 0) {
            list.unshift({
                span: 4,
                name: '区域',
                props: 'name',
                default: '',
                isStatic: true
            },)
        } else if (listType.value == 1) {
            list.unshift({
                span: 4,
                name: '分局',
                props: 'name',
                default: '',
                isStatic: true
            })

        } else if (listType.value == 2) {


            list = [
                {
                    span: 6,
                    name: '姓名',
                    props: 'staffName',
                    default: '',
                    isStatic: true
                },
                {
                    span: 6,
                    name: '角色',
                    props: 'primaryRoleName',
                    default: ''

                },

                {
                    span: 4,
                    name: '积分',
                    props: 'pointTotal',
                    default: ''

                },
                {
                    span: 4,
                    name: '人均<br>日活力',
                    props: 'huoliDayPercent',
                    default: ''

                },
                {
                    span: 4,
                    name: '人均<br/>商机',
                    props: 'businessDayPercent',
                    default: 0,
                    toFixed: 2,

                },
            ]
        }
    } else if (curSelectTab.value == 2) {
        list = [
            {
                span: listType.value == 2 ? 5 : 6,
                name: '实体商机数',
                props: 'stQty',
                default: 0,
            },
            {
                span: listType.value == 2 ? 5 : 6,
                name: '政企商机数',
                props: 'zqQty',
                default: 0,
            }, {
                span: listType.value == 2 ? 5 : 6,
                name: '装维商机数',
                props: 'zwQty',
                default: 0,

            },
        ]

        if (listType.value == 0) {
            list.unshift({
                span: 6,
                name: '区域',
                props: 'name',
                default: '',
                isStatic: true
            },)
        } else if (listType.value == 1) {
            list.unshift({
                span: 6,
                name: '分局',
                props: 'name',
                default: '',
                isStatic: true
            })

        } else if (listType.value == 2) {

            list.unshift(
                {
                    span: 5,
                    name: '姓名',
                    props: 'staffName',
                    default: '',
                    isStatic: true
                },
                {
                    span: 4,
                    name: '角色',
                    props: 'staffRoleName',
                    default: ''

                },
            )

        }
    } else if (curSelectTab.value == 3) {
        if (curCustomerVisit.value === '全市视图') {
            if (listType.value === 0 || listType.value === 1) {
                list = [
                    {
                        span: 6,
                        name: listType.value === 0 ? '区域' : '分局',
                        props: 'name',
                        default: '',
                        isStatic: true
                    },
                    {
                        span: 5,
                        name: '客户经理数',
                        props: 'customerManagerCount',
                        default: 0
                    },
                    {
                        span: 5,
                        name: '拜访客户数',
                        props: 'visitCustomerCount',
                        default: 0
                    },
                    {
                        span: 5,
                        name: '有拜访<br/>客户经理数',
                        props: 'hasVisitCustomerManagerCount',
                        default: 0
                    },
                    {
                        span: 4,
                        name: '占比',
                        props: 'proportion',
                        default: '0%'
                    },
                    {
                        span: 5,
                        name: '无拜访<br/>客户经理数',
                        props: 'noVisitCustomerManagerCount',
                        default: 0
                    },
                    {
                        span: 5,
                        name: '活力值<br/>总和',
                        props: 'vitalitySum',
                        default: 0
                    },
                    {
                        span: 4,
                        name: '日活力<br/>人均',
                        props: 'averageDailyVitality',
                        default: 0
                    }
                ];
            } else if (listType.value === 2) {
                list = [
                    {
                        span: 6,
                        name: '姓名',
                        props: 'staffName',
                        default: '',
                        isStatic: true
                    },
                    {
                        span: 6,
                        name: '角色',
                        props: 'staffRoleName',
                        default: ''
                    },
                    {
                        span: 6,
                        name: '拜访客户数',
                        props: 'visitCustomerCount',
                        default: 0
                    },
                    {
                        span: 6,
                        name: '人均日活力值',
                        props: 'averageDailyVitality',
                        default: 0
                    }
                ];
            }
        }
        // 客户拜访TOP榜
        else if (curCustomerVisit.value === '客户拜访TOP榜') {
            list = [
                {
                    span: 6,
                    name: '分局名称',
                    props: 'name',
                    default: '',
                    isStatic: true
                },
                {
                    span: 6,
                    name: '客户经理数量',
                    props: 'customerManagerCount',
                    default: 0
                },
                {
                    span: 6,
                    name: '可以拜访次数',
                    props: 'possibleVisitCount',
                    default: 0
                },
                {
                    span: 6,
                    name: '人均拜访次数',
                    props: 'averageVisitCount',
                    default: 0
                }
            ];
        }
        // 活力值TOP榜
        else if (curCustomerVisit.value === '活力值TOP榜') {
            list = [
                {
                    span: 8,
                    name: '分局名称',
                    props: 'name',
                    default: '',
                    isStatic: true
                },
                {
                    span: 8,
                    name: '政企活力值',
                    props: 'governmentEnterpriseVitality',
                    default: 0
                },
                {
                    span: 8,
                    name: '人均活力值',
                    props: 'averageVitality',
                    default: 0
                }
            ];
        }
    }

    return list
})



const curSelectTab = ref(0)

const tabList = ref([
    { text: '发展情况', value: 0 },
    { text: '数智运营', value: 1 },
    { text: '商机运营', value: 2 },
    { text: '客户拜访', value: 3 },
])



//当前 默认选择 当日
const dayOrMonth = reactive({
    text: '当日',
    value: 0
})

// tab 列表
const dayOrMonthList = ref([
    { text: '当日', value: 0 },
    { text: '当月', value: 1 },

])


const tableData = ref<RowItem[]>([])
const listType = ref<number>()


async function loadDevelopmentLeader() {
    // loadingConfig.visible = true
    let params: any = {
        areaId: undefined,
        areaIdL2: undefined
    }
    if (listType.value == 0) {
        params.areaId = curslectRow.value?.areaId
    } else if (listType.value == 1) {
        params.areaIdL2 = curslectRow.value?.areaIdL2
    }

    let API = undefined
    //发展情况
    if (curSelectTab.value == 0) {
        params.type = dayOrMonth.value,
            API = getDevelopmentAreaAndDeptAPI
    } else if (curSelectTab.value == 1 || curSelectTab.value == 2) {
        params.startTime = formline.startTime ? `${formline.startTime} 00:00:00` : undefined;
        params.endTime = formline.endTime ? `${formline.endTime} 23:59:59` : undefined;
        API = curSelectTab.value == 1 ? getDevelopmentEnergyAndPointsAPI : getDevelopmentbusinessOperationAPI
    }

    await (API as Function)(params).then((res: any) => {
        const defaultMap = new Map<string, any>(
            rowList.value.map(({ props, default: defaultValue }) => [props || '', defaultValue])
        );

        listType.value = res.data.type
        tableData.value = res.data.list.map((item: RowItem) => ({
            ...item,
            id: Math.random().toString(36).substr(2, 9),
            ...Object.fromEntries(
                Object.entries(item).map(([key, value]) =>
                    [key, value === null ? defaultMap.get(key) : value]
                )
            )
        }));

        let MapType = res.data.type == 0 ? AreaPriorityMap : res.data.type == 2 ? rolePriorityMap : null

        // 排序：先按照角色优先级排序，再按照积分排序
        tableData.value.sort((item1: RowItem, item2: RowItem) => {
            if (MapType) {
                const MapType1 = MapType[res.data.type == 2 ? item1.primaryRoleName || item1.staffRoleName : item1.name] || 99;
                const MapType2 = MapType[res.data.type == 2 ? item2.primaryRoleName || item1.staffRoleName : item2.name] || 99;

                if (MapType1 !== MapType2) {
                    return MapType1 - MapType2;
                }
            }

            return curSelectTab.value == 0 ? item2.huoli - item1.huoli : res.data.type == 2 ? item2.pointTotal - item1.pointTotal : item2.huoliDayPercent - item1.huoliDayPercent;
        });

        if (MapType) {
            tableData.value = tableData.value.filter(item => MapType[res.data.type == 2 ? item.primaryRoleName || item.staffRoleName : item.name] !== undefined);
        }

        markRankClasses(tableData.value)

        //兼容 商机运营接口
        if (curSelectTab.value == 2 && res.data.type == 2) {
            tableData.value = tableData.value.map(item => {
                return {
                    ...item,
                    staffName: item.name
                }
            })
        }

        // 添加合计行
        if (curSelectTab.value == 0 || curSelectTab.value == 2) {
            const totalRow: RowItem = {
                id: 'total',
                name: '合计',
                staffName: '合计',
                isTotal: true,
                span: 0,
                props: '',
                default: 0
            };

            // 计算每一列的总和
            rowList.value.forEach(column => {

                if (column.props === 'primaryRoleName' || column.props === 'staffRoleName') {

                    totalRow[column.props] = '- -';
                } else if (column.props && column.props !== 'name' && column.props !== 'staffName' && column.props !== 'primaryRoleName' && column.props !== 'staffRoleName') {

                    totalRow[column.props] = tableData.value.reduce((sum, item) => {
                        const value = Number(item[column.props] || 0);
                        return sum + value;
                    }, 0);


                    // 如果有toFixed属性，保留对应的小数位数
                    if (column.toFixed !== undefined) {
                        totalRow[column.props] = Number(totalRow[column.props].toFixed(column.toFixed));
                    }
                }
            });


            tableData.value.push(totalRow);
        }

    }).finally(() => {
        loadingConfig.visible = false
    })
}

const curslectRow = ref<RowItem>()
function handleClick(row: RowItem) {
    if (listType.value == 2 || row.isTotal) return
    curslectRow.value = row
    loadDevelopmentLeader()
}

const loading = ref(false)

function onRefresh() {
    loading.value = true
    curslectRow.value = undefined
    loadDevelopmentLeader()
    loading.value = false
}

//excel 导出
function exportExcel() {
    try {
        const tHeader = ['区域名称', '总人数', '积分为0', '占比', '活力值为0', '占比', '商机总数', '活力总数', '人均活力值', '人均日活力值', '人均商机'] // 表头 必填

        const filterVal = [
            'name',
            'totalCount',
            'zeroPointsCount',
            'zeroPointsPercent',
            'zeroHuoliCount',
            'zeroHuoliPercent',
            'businessTotal',
            'huoliTotal',
            'huoliPercent',
            'huoliDayPercent',
            'businessDayPercent'
        ]
        const data = formatJson(filterVal, tableData.value)
        exportJsonToExcel({
            header: tHeader,
            data: data.map(row => row.map(cell => cell.value)),
            filename: '数智运营',
            autoWidth: true,
            bookType: 'xls',
            extraColors: data.map(row => row.map(cell => cell.color)) // 传递颜色信息
        })
        showSuccessToast('导出报表成功！')
    } catch (error) {
        showFailToast(error as string)
    }

}

function formatJson(filterVal: string[], jsonData: any[]) {

    const list = ['zeroPointsPercent', 'zeroHuoliPercent']
    return jsonData.map(v => filterVal.map(j => {

        return {
            value: (v[j] ?? 0) + (list.includes(j) ? '%' : ''),
            color: v[`color${j}`] || null
        };
    }))
}


function changeSelectTab(item: any) {
    tableData.value = []
    curslectRow.value = undefined
    loadDevelopmentLeader()
    nextTick(() => {
        handleScroll()
        const Tabs = document.getElementById('Tabs') as HTMLDivElement
        Tabs.scrollTo({ left: (Tabs.scrollWidth / tabList.value.length) * item.value, behavior: "smooth" });
    })

}




function onSelect(params: any) {
    dayOrMonth.text = params.text
    dayOrMonth.value = params.value
    loadDevelopmentLeader()

}



function markRankClasses<T extends Record<string, any>>(data: T[]): T[] {
    const list = [
        'zeroPointsPercent',
        'zeroHuoliPercent',
        'huoliDayPercent',


    ] as const; // 使用 `as const` 使其成为只读元组类型
    type ListKeys = typeof list[number]; // 获取 list 元素的联合类型
    const rates: Record<ListKeys, number[]> = {} as Record<ListKeys, number[]>;
    for (const key of list) {
        rates[key] = data.map(item => item[key] ?? 0).sort((a, b) => b - a)
        const list = ['zeroPointsPercent', 'zeroHuoliPercent']
        const result = getTopAndBottom(rates[key], list.includes(key))
        markColors(data, result, key, list);
    }

    return data; // 返回标记后的原始数组

}

function markColors(data: any, result: { top: number[]; bottom: number[] }, key: string, list: string[]) {

    const bottomSet = new Set(result.bottom); // 使用 Set 存储 bottom 值
    const topSet = new Set(result.top); // 使用 Set 存储 top 值

    for (const item of data) {
        const rateValue = item[key] ?? 0;
        if (bottomSet.has(rateValue)) {
            item[`color${key}`] = list.includes(key) ? 'green' : 'yellow'; // 标记为黄色
        } else if (topSet.has(rateValue)) {
            item[`color${key}`] = list.includes(key) ? 'yellow' : 'green'; // 标记为绿色
        }
    }
}

function getTopAndBottom<T>(arr: T[], isRever: boolean): { top: T[]; bottom: T[] } {
    const sortedArray = [...arr].sort((a: any, b: any) => b - a); // 降序排序
    //這兩個是相反的


    const top = sortedArray.slice(0, isRever ? 4 : 3); // 获取前三个
    const bottom = sortedArray.slice(-(isRever ? 3 : 4)); // 获取后三个
    return { top, bottom };
}



const activityContainer = ref<HTMLElement | null>(null); // 用来引用容器
const showRightArrow = ref<boolean>(false); // 控制箭头显示的变量



// 处理滚动事件
const handleScroll = () => {
    if (!activityContainer.value) return;
    const container = activityContainer.value;
    // 判断是否能继续向右滚动
    const isScrolledToRight = container.scrollWidth > container.clientWidth && container.scrollLeft < container.scrollWidth - container.clientWidth;

    showRightArrow.value = isScrolledToRight;
};

// 滚动到右侧
const scrollToRight = () => {
    if (activityContainer.value) {
        activityContainer.value.scrollTo({ left: activityContainer.value.scrollWidth, behavior: "smooth" });
    }
};




// 生命周期钩子
onMounted(() => {
    formline.startTime = formatDate(new Date(), 'yyyy-MM-dd')
    formline.endTime = formatDate(new Date(), 'yyyy-MM-dd')
    defaultDate.value = [new Date(), new Date()]
    if (activityContainer.value) {
        activityContainer.value.addEventListener("scroll", handleScroll);
    }

    loadDevelopmentLeader()
});

onBeforeUnmount(() => {
    if (activityContainer.value) {
        activityContainer.value.removeEventListener("scroll", handleScroll);
    }
});
</script>

<style lang='scss' scoped>
.container_body {
    padding: 15px 10px;
    width: 100%;
}


.container__header {
    gap: 5px;
    flex-direction: column;

    .Tabs {
        position: relative;
        overflow-y: hidden;
        overflow-x: auto;
        flex: 0 1 auto;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
}

.activity-container {
    overflow-x: auto;
    position: relative;
}

.right-arrow {
    position: sticky;
    right: -10px;
    top: 100px;
    display: flex;
    align-items: center;
    cursor: pointer;
    width: 20px;
    background-color: #1A76FF;
    border-radius: 0px 5px 5px 0px;
}

.green {
    background-color: #92d050;
    background-clip: content-box;
    /* 只在内容区域显示背景颜色 */
}

.yellow {
    background-color: #ffc000;
    background-clip: content-box;
    /* 只在内容区域显示背景颜色 */
}


.row-item {
    font-weight: 400;
    font-size: 12px;
    color: #2079FF;
    text-align: center;
}

.table-header {
    border-bottom: 1px solid #EDF5FF;
    padding-bottom: 10px;
    position: relative;
}

.table-body {
    border-bottom: 1px solid #EDF5FF;
    padding: 10px 0px;
}

.col-item {
    font-weight: 400;
    font-size: 12px;
    color: #3D3D3D;
    text-align: center;

}

.static-col-item {
    position: sticky;

    left: 0px;
    z-index: 2;
    background-color: #FBFCFF;

}

.active-col-item {
    color: #1a76ffab !important;
    text-decoration: underline;
    text-underline-offset: 3px;
    /* 调整下划线与文字的距离 */
}

.selectOption-item {
    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
    font-weight: 400;
    font-size: 14px;
    color: #1A76FF;
    padding: 8px;

    display: flex;
    justify-content: space-between;
    align-items: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;

    .play {
        transform: rotate(90deg);
    }
}

.selectOption-item:active {
    background: #d8dee6;
}

:deep(.van-row) {
    flex-wrap: nowrap;
}

:deep(.card-header-right > .item.activeItem) {
    font-size: 14px;
}

:deep(.card-header-right > .item) {
    font-size: 14px;
}

.total-row {
    color: #3D3D3D !important;
    text-decoration: none !important;
    font-weight: bold;
}

.customerVisitPopover {
    :deep(.van-popover__action) {
        width: 135px;
    }
}
</style>