<template>
    <div class="searchBox">
        <van-tabs v-model:active="activeName" title-active-color="#1A76FF" :class="activeName" animated swipeable>
            <van-tab :title="item.title" :name="item.moudule_name" v-for="item in componentOption"
                :key="item.moudule_name">
                <component :is="item.moudule_" :key="item.moudule_name"
                    :ref="(el: HTMLDivElement) => item.ref_name && (refsArray[item.ref_name] = el)"
                    v-on="generateEventHandlers(item.EventsList || [])"
                    @="emitEventsListGenerateEventHandlers(item.emitEventsList || [])"
                    v-bind="mergePropsAndVModelBindings(item.props, item.vModels)">
                </component>

            </van-tab>
        </van-tabs>
    </div>
</template>

<script setup lang="ts">
import componentList from "@/views/template/config";
import { useUserInfo } from "@/stores/userInfo";
import { useRouter } from "vue-router";
import { ref, reactive, onMounted, ComponentPropsOptions, ComponentOptions } from "vue";
import { EventsHooks, ComponentOptionProps } from "@/hooks_modules/Eventhooks";
const { generateEventHandlers, emitEventsListGenerateEventHandlers, mergePropsAndVModelBindings } = EventsHooks()
const userInfoStore = useUserInfo();
const router = useRouter()

const activeName = ref<string>('morningLogSearch')

const refsArray = ref<{ [key: string]: HTMLDivElement | null }>({});
// const componentOption: ComputedRef<ComponentOptionProps[]> = computed(() => {
//     let list: Array<ComponentOptionProps> = [
//         {
//             moudule_: componentList['morningLogSearch'].moudule_,
//             moudule_name: 'morningLogSearch',
//             ref_name: 'morningLogSearch',
//             title: '晨会日志',
//         },
//         {
//             moudule_: componentList['reviewLogSearch'].moudule_,
//             moudule_name: 'reviewLogSearch',
//             ref_name: 'reviewLogSearch',
//             title: '复盘日志',

//         },
//         {
//             moudule_: componentList['comparisonLogSearch'].moudule_,
//             moudule_name: 'comparisonLogSearch',
//             ref_name: 'comparisonLogSearch',
//             title: '比算',

//         },
//     ]

//     return list
// })

const componentOption: ComputedRef<ComponentOptionProps[]> = computed(() => {
    let list = userInfoStore.getMenuList.filter((item: MenuItem) => item.menuCode === 'search')[0]?.children || []
    const listOption = list.map((item: MenuItem) => {
        return {
            moudule_: componentList[item.menuCode].moudule_,
            moudule_name: item.menuCode,
            ref_name: item.menuCode,
            title:item.menuName
        }
    })
    return listOption
})


onMounted(() => {
    // console.log(import.meta.env);


})
onActivated(() => {


})
</script>


<style lang="scss" scoped>
.searchBox {
    overflow: hidden;
    background: #FBFCFF;
    border-radius: 10px 10px 0px 0px;
    height: 100%;
}

:deep(.van-tabs) {
    height: 100%;

}

:deep(.van-tabs__content) {
    height: calc(100% - var(--van-tabs-line-height));

}



.morningLogSearch {
    background: url('@/assets/tabbar_change1.png') no-repeat;
    background-size: 100%;
}

.reviewLogSearch {
    background: url('@/assets/tabbar_change2.png') no-repeat;
    background-size: 100%;
}

.comparisonLogSearch {
    background: url('@/assets/tabbar_change3.png') no-repeat;
    background-size: 100%;
}

:deep(.van-tabs__nav) {
    background-color: transparent;
}

:deep(.van-tab) {
    font-weight: 400;
    font-size: 18px;
    color: #3D3D3D;
}

:deep(.van-tabs__line) {
    height: 4px;
    background: #1A76FF;
    border-radius: 26px 26px 26px 26px;
}

.test {

    // clip-path: path("M25 11C19.477 11 15 15.477 15 21L15 63H374V21C374 15.477 369.523 11 364 11H25Z M15 63L240.994 63C248.012 63 254.092 58.1335 255.63 51.2856L264 11H364C369.52 11 374 15.48 374 21V63H15Z"
    //     );


}
</style>