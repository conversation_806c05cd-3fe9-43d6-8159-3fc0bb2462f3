import { ComponentPropsOptions, ComponentOptions } from "vue";

export type Events = Array<Record<string, Function>>
export type emitEvents = Array<{ eventName: string, handler: Function }>
export type ComponentOptionProps = {
    moudule_: ComponentOptions<any, any, any>,
    moudule_name: string,
    ref_name: string,
    // props?: ComponentPropsOptions,
    props?: Record<string, any>,
    vModels?: { prop: string; value: any }[]
    EventsList?: Events, // 原始事件
    emitEventsList?: emitEvents // 父组件事件
    [propsName:string]:any
}

/*    
 let list: Array<ComponentOptionProps> = [
        {
            moudule_: componentList['userInfo'].moudule_,
            moudule_name: 'userInfo',
            ref_name: 'userInfo',
        },
         {
            moudule_: Headlines,
            moudule_name: 'Headlines',
            ref_name: 'Headlines',
            // props: {
            //     title: title.value,
            //     activeReactive: activeReactive,
            //     // active: active.value
            // },
            // vModels: [
            //     { prop: 'customTitle', value: customTitle }, // 使用 `customTitle` 作为 `v-model`
            //     { prop: 'active', value: customActive },     // 使用 `active` 作为另一个 `v-model`
            // ],
            // emitEventsList: [
            //     { eventName: 'test', handler: test },
            //     { eventName: 'sidebarSelect', handler: handleSidebarSelect }],
            // EventsList: [{ 'click': handleHeadlineClick }],
        }
    ]
        
    */




export const EventsHooks = function () {

    // 动态生成事件处理函数
    function generateEventHandlers(events: Events) {
        const handlers: Record<string, Function> = {};
        events.forEach(item => {
            Object.entries(item).forEach(([eventName, handlerFunction]) => {
                if (typeof handlerFunction === "function") {
                    handlers[eventName] = handlerFunction;
                }
            });
        });
        return handlers;
    }

    //处理监听子组件事件
    function emitEventsListGenerateEventHandlers(EventsList: emitEvents): Record<string, Function> {
        const handlers: Record<string, Function> = {};
        EventsList.forEach(item => {
            handlers[item.eventName] = item.handler;
        });
        return handlers;

    }

    function mergePropsAndVModelBindings(
        props: Record<string, any> = {}, // 默认为空对象，确保不是 undefined
        vModels: { prop: string; value: any }[] = [] // 默认为空数组，确保不是 undefined
    ): Record<string, any> {

        const bindings: Record<string, any> = { ...props }; // 合并 props 到 bindings 对象中
        vModels.forEach((vModel) => {
            bindings[vModel.prop] = vModel.value.value;
            // bindings[`modelValue:${vModel.prop}`] = vModel.value.value;
            bindings[`onUpdate:${vModel.prop}`] = (newValue: any) => {
                // vModel.value.value = newValue; 
                if (typeof vModel.value === 'object' && vModel.value !== null && 'value' in vModel.value) {
                    vModel.value.value = newValue;
                } else {
                    // 如果不是 `ref`，则直接更新
                    Object.assign(vModel.value, newValue);
                }
            };
        });
        return bindings;
    }


    return {
        generateEventHandlers,
        emitEventsListGenerateEventHandlers,
        mergePropsAndVModelBindings,
    }

}


