<template>

    <div class="card container_" v-swipe-gesture="swipeCallback">
        <div class="container__header">
            <div style="display: flex; align-items: center;">
                <img src="@/assets/sendOrdersInfo.svg" alt="" srcset="">
                <span class="ml-6" style="font-weight: 500;">以日保周</span>
            </div>
            <cusTabs :tabList="tabList" v-model:curSelectTab="curSelectTab" @changeSelectTab="changeSelectTab">
            </cusTabs>
        </div>
        <div class="container_body ">
            <div class="container_body_header">
                <div class="title">
                    <div>{{ title.title_ }}</div>
                    <div>({{ title.title_1 }})</div>

                </div>
                <div class="selectOption-item" @click="calendarIsShow = true">
                    <span>{{ time ? time : '请选择时间' }}</span>
                    <van-icon name="play" class="play" color="#1A76FF" />
                </div>
            </div>

            <div class="table-container" @touchstart.stop>
                <table>
                    <thead>
                        <template v-if="curSelectTab == 0">
                            <!-- <tr>
                                <th rowspan="2" class="sticky">区域</th>
                                <th colspan="3" class="main-title">管住人 </th>
                                <th colspan="5" class="main-title">管住事</th>
                                <th colspan="2" class="main-title">管住果 </th>
                            </tr> -->
                            <tr>
                                <th class="sticky">区域</th>

                                <th>接单量</th>
                                <th>接通量</th>
                                <th>接通比率</th>

                                <th>未接通量</th>
                                <th>未接通人数</th>

                                <th>达标人数</th>
                                <th>达标比率</th>
                                <th>平均外呼量</th>

                            </tr>

                        </template>
                        <template v-else-if="curSelectTab == 1">
                            <tr>
                                <th class="sticky">派单项目</th>
                                <th class="main-title">接单量</th>
                                <th class="main-title">当日接单率 </th>
                                <th class="main-title">外呼数 </th>
                                <th class="main-title">呼通数</th>
                                <th class="main-title">外呼30以上</th>
                                <th class="main-title">转化成功 </th>

                            </tr>

                        </template>
                        <template v-if="curSelectTab == 2">
                            <tr>
                                <th class="sticky">白名单人员</th>
                                <th class="main-title">派单名称</th>

                                <th class="main-title">接单量</th>
                                <th class="main-title">外呼数</th>
                                <th class="main-title">接单执行率</th>
                                <th class="main-title">提醒 </th>
                                <th class="main-title">呼通数</th>
                                <th class="main-title">呼通率</th>
                                <th class="main-title">派单数</th>
                                <th class="main-title">转化数</th>
                                <th class="main-title">派单转化率</th>
                                <th class="main-title">当日外呼数</th>
                                <th class="main-title">当日呼通数</th>
                                <th class="main-title">当日呼通率</th>
                                <th class="main-title">当日转化数</th>
                                <th class="main-title">当日派单转化率</th>

                            </tr>
                        </template>
                    </thead>
                    <tbody>
                        <template v-if="curSelectTab == 0">
                            <tr v-for="(row, index) in tableData" :key="index">
                                <td class="sticky">
                                    <div class="areaNameL2">
                                        {{ row.areaName }}
                                    </div>
                                </td>
                                <td>{{ row.outboundCallCount }}</td>
                                <td>{{ row.connectedCallCount }}</td>
                                <td>{{ row.connectedCallRatio }}%</td>

                                <td>{{ row.officesWithOutboundCalls }}</td>
                                <td>{{ row.peopleWithOutboundCalls }}</td>

                                <td>{{ row.peopleMeetingTarget }}</td>
                                <td>{{ row.targetPeopleRatio }}%</td>
                                <td>{{ row.averageOutboundCallsPerPerson }}</td>


                            </tr>
                        </template>
                        <template v-else-if="curSelectTab == 1">
                            <tr v-for="(row, index) in tableData" :key="index">
                                <td class="sticky">
                                    <div class="areaNameL2">
                                        {{ row.frameName }}
                                    </div>
                                </td>
                                <!-- <td>{{ row.dispatchCount }}</td> -->
                                <td>{{ row.orderCount }}</td>
                                <td>{{ row.orderExecutionRate }}%</td>
                                <td>{{ row.outboundCallCount }}</td>
                                <td>{{ row.connectedCount }}</td>
                                <td>{{ row.outboundCallOver30s }}</td>
                                <!-- <td>{{ row.conversionSuccess }}</td> -->
                                <td>{{ row.conversionSuccess }}</td>
                            </tr>

                        </template>
                        <template v-else-if="curSelectTab == 2">
                            <tr v-for="(row, index) in tableData" :key="index">
                                <td class="sticky" v-if="shouldShowName(row.staffCode, index)"
                                    :rowspan="getRowSpan(row.staffCode)" v-getName="{ item: row }"> {{ row.name }}</td>

                                <td>
                                    <div style="width: 200px;max-width: 200px; white-space: normal;">
                                        {{ row.activityName }}
                                    </div>
                                </td>
                                <td>{{ row.orderCount }}</td>
                                <td>{{ row.outboundCallCount }}</td>
                                <td :class="row.orderExecutionRate < 100 ? 'yellow' : ''">{{ row.orderExecutionRate }}%
                                </td>
                                <td>
                                    <van-button v-if="row.orderExecutionRate < 100" size="mini" icon="clock-o"
                                        :color="calculateColor(row.remindCount)" @click="tipsSave(row)">提醒他</van-button>
                                </td>

                                <td>{{ row.connectedCount }}</td>
                                <td>{{ row.connectedRate }}%</td>
                                <td>{{ row.dispatchCount }}</td>
                                <td>{{ row.conversionCount }}</td>
                                <td>{{ row.dispatchConversionRate }}%</td>
                                <td>{{ row.currentDayOutboundCallCount }}</td>
                                <td>{{ row.currentDayConnectedCount }}</td>
                                <td>{{ row.currentDayConnectedRate }}%</td>
                                <td>{{ row.currentDayConversionCount }}</td>
                                <td>{{ row.currentDayDispatchConvRate }}%</td>
                            </tr>

                        </template>
                    </tbody>



                </table>

            </div>
            <van-empty description="暂无数据" class="mt-8" style="background-color: #FBFCFF;border-radius: 15px;"
                v-if="tableData.length == 0" />
        </div>
        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />
    </div>





</template>

<script setup lang="ts">
import { AxiosResponse } from "axios";
import cusTabs from "@/components/cusTabs/index.vue";
import { debounce } from "@/utils/debounce";
import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { remindSaveAPI } from "@/api/remind";
import { showSuccessToast } from "vant";
import {
    getDaySecuresWeek_WLListAPI,
    getDaySecuresWeek_OREADERListAPI,
    getDaySecuresWeek_AREAL2ListAPI
} from "@/api/daySecuresWeek";

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    onConfirm,
} = searchHooks({
    source: 'daySecuresWeek',
    callback: loadList
})



const tabList = [
    { text: '分局执行', value: 0 },
    { text: '派单执行', value: 1 },
    { text: '白名单执行', value: 2 },
]

const curSelectTab = ref(0)
function changeSelectTab(params: any) {
    tableData.value = []
    loadList()

}


function swipeCallback(value: string) {
    let curIndex = tabList.findIndex(item => item.value == curSelectTab.value)
    if (value == 'left') {
        if (curSelectTab.value == 0) {
            return
        } else {
            curSelectTab.value = tabList[curIndex - 1].value

        }

    } else {
        if (curSelectTab.value == 2) {
            return
        } else {
            curSelectTab.value = tabList[curIndex + 1].value

        }
    }
}



const title = computed(() => {
    let T = {
        title_: '',
        title_1: '',
    }
    switch (curSelectTab.value) {
        case 0:
            T.title_ = '分局外呼通报'
            T.title_1 = '整体执行'
            break
        case 1:
            T.title_ = '每日派单通报'
            T.title_1 = '动作执行'
            break
        case 2:
            T.title_ = '白名单通报'
            T.title_1 = '人员执行'
            break
    }
    return T
})
const time = computed(() => {
    if (formline.startTime && formline.endTime) return formline.startTime == formline.endTime ? `${formline.startTime}` : `${formline.startTime} ~ ${formline.endTime}`
    else return ``
})



const tableData = ref<any>([])

function loadList() {
    let API: ((data: any) => Promise<AxiosResponse<any, any>>) | undefined
    switch (curSelectTab.value) {
        case 0:
            API = getDaySecuresWeek_AREAL2ListAPI
            break
        case 1:
            API = getDaySecuresWeek_OREADERListAPI
            break
        case 2:
            API = getDaySecuresWeek_WLListAPI
            break

        default:
            return
    }



    API({ day: formline.startTime ? `${formline.startTime} 00:00:00` : undefined }).then((res: any) => {
        if (res.code == 200) {
            tableData.value = res.data
        }
    })
}


const tipsSave = debounce((row: any) => {
    remindSaveAPI({
        remindCode: row.staffCode,
        remindInfo: '您本日派单外呼任务未完成，请尽快处理。',
        remindType: 'KY_SOP_PAIDAN',
    }).then((res: any) => {
        if (res.code == 200) {
            showSuccessToast('提醒成功')
        }
    })

    loadList()

}, 500, true)

// 合并单元格
function shouldShowName(staffCode: string, index: number): boolean {
    return index === tableData.value.findIndex((row: any) => row.staffCode === staffCode);
}

function getRowSpan(staffCode: string): number {
    return tableData.value.filter((row: any) => row.staffCode === staffCode).length;
}

function calculateColor(value: number, minValue: number = 0, maxValue: number = 10): string {
    // Clamp the value between minValue and maxValue
    value = Math.max(minValue, Math.min(value, maxValue));

    // Normalize the value to a range of 0 to 1
    const normalizedValue = (value - minValue) / (maxValue - minValue);

    // Define the starting color (blue) and ending color (red)
    const startColor = { r: 0x35, g: 0x75, b: 0xF7 };  // #3575F7
    const endColor = { r: 0xFF, g: 0x00, b: 0x00 };    // #FF0000

    // Calculate the interpolated color
    const r = Math.round(startColor.r + (endColor.r - startColor.r) * normalizedValue);
    const g = Math.round(startColor.g + (endColor.g - startColor.g) * normalizedValue);
    const b = Math.round(startColor.b + (endColor.b - startColor.b) * normalizedValue);

    // Return the color in hex format
    return `#${r.toString(16).padStart(2, '0').toUpperCase()}${g.toString(16).padStart(2, '0').toUpperCase()}${b.toString(16).padStart(2, '0').toUpperCase()}`;
}

onMounted(() => {
    const currentTime = new Date();
    currentTime.setDate(currentTime.getDate() - 3);
    const threeDaysAgo = currentTime.toISOString().split('T')[0];
    console.log(threeDaysAgo);
    formline.startTime = threeDaysAgo
    formline.endTime = threeDaysAgo
    defaultDate.value = new Date(threeDaysAgo)
    loadList()
})
</script>

<style lang="scss" scoped>
.card {
    font-family: 'Source Han Sans, Source Han Sans';
    min-height: 100%;
    background: #fbfcff;
    border-radius: 10px 10px 0px 0px;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);

    .container__header {
        display: block;
        padding-bottom: 5px;
    }

    .container_body_header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
            display: flex;
            align-items: flex-end;

            &>div:first-child {
                font-weight: 500;
                font-size: 16px;
                color: #000000;
            }

            &>div:last-child {
                font-weight: 400;
                font-size: 13px;
                color: #999999;
                margin-left: 5px;
            }
        }

    }

    .selectOption-item {
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;
        position: relative;
        padding-right: 25px;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
            position: absolute;
            right: 10px;
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }

    .table-container {
        width: 100%;
        overflow-x: auto;
        margin-top: 10px;

        padding-bottom: 10px;

        table {
            width: 100%;
            border-collapse: collapse;
            text-align: center;
            font-size: 13px;

            th,
            td {
                padding: 8px;
                border: 1px solid #ddd;

                white-space: nowrap; // 禁止换行
            }

            .areaNameL2 {
                width: 100px;
                white-space: normal;
            }

            .sticky {
                /* 固定表格内容的门店列 */
                position: sticky;
                left: 0px;
                background-color: #fff;
                /* 设置背景颜色以覆盖其他列 */
                z-index: 1;
                border: 1px solid #ddd;

                /* 确保门店列在最上层 */
            }

            .green {
                background-color: #92d050;
            }

            .yellow {
                background-color: #ffc000;
            }



            .main-title {
                font-size: 13px;
                font-weight: bold;
                padding: 12px;
                background-color: #2e70ba;
                color: #fff;
                text-align: center;
            }

            thead tr:nth-child(n) th {
                background-color: #2e70ba;
                color: #fff;
                font-weight: bold;
                padding: 10px;
                text-align: center;
            }

            thead tr:nth-child(3) th {
                background-color: #2e70ba;
                color: #fff;
                font-weight: bold;
                padding: 8px;
            }



            tbody tr:nth-child(even) {
                background-color: #f2f2f2;
            }




            tbody tr:hover {
                background-color: #e6f7ff;
            }

            tbody td.highlight {
                background-color: #ffe58f;
                font-weight: bold;
            }
        }


    }


}
</style>