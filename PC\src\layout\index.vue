<template>
  <div :class="classObj" class="app-wrapper">
    <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside" />
    <sidebar class="sidebar-container" />
    <div class="main-container" style="padding-top: 40px">
      <div class="fixed-header">
        <navbar />
        <TagsView />
      </div>
      <el-config-provider :locale="locale.locale">
        <app-main />
      </el-config-provider>
    </div>
  </div>
</template>

<script setup>
import Sidebar from "./components/Sidebar/index.vue";

import { Navbar, AppMain, TagsView } from "./components/index.ts";
import ResizeHandler from "./mixin/ResizeHandler.ts";
import { ref, reactive, computed, watch, onMounted, provide } from "vue";
import { ElConfigProvider } from "element-plus";
import { useSidebarStore } from "@/stores/layoutSetting/app.ts";
import { storeToRefs } from "pinia";

import zhCn from "element-plus/dist/locale/zh-cn.mjs";
import en from "element-plus/dist/locale/en.mjs";

const locale = reactive({
  locale: zhCn,
});

const SidebarState = useSidebarStore();
const { sidebar, device } = storeToRefs(SidebarState);

const classObj = computed(() => {
  return {
    hideSidebar: !sidebar.value.opened,
    openSidebar: sidebar.value.opened,
    withoutAnimation: sidebar.value.withoutAnimation,
    mobile: device.value === "mobile",
  };
});
ResizeHandler({ sidebar, device });

function handleClickOutside() {
  SidebarState.closeSidebar(false);
}
</script>

<style lang="scss" scoped>

@use "@/styles/mixin.scss" as *;
@use "@/styles/variables.scss" as *;
.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$sideBarWidth});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.mobile .fixed-header {
  width: 100%;
}

.sticky-toolbar {
  width: 46px;
  position: fixed;
  // top: 40%;
  top: 45%;
  right: 0;
  list-style: none;
  margin: 0;
  z-index: 99;
  background: rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15);
  box-shadow: 0px 0px 50px 0px rgba(82, 63, 105, 0.15);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  border-top-left-radius: 0.42rem;
  border-bottom-left-radius: 0.42rem;
  padding: 10px 7px;

  &>div {
    background-color: #fff;
    border-color: #f3f6f9;
    height: 30px;
    width: 30px;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    transition: all 0.1s ease-in;
  }

  .dataAnalysisIsHide {
    color: #1bc5bd;
  }

  .dataAnalysisIsActive {
    background-color: #1bc2ba;
    color: #fff;
  }

  .search_FixedIsActive {
    color: #ffffff !important;
    background-color: #0bb783 !important;
    border-color: #0bb783 !important;
  }

  .search_FixedIsHide {
    color: #0bb783;
  }

  .scroll_followIsActive {
    color: #ffffff !important;
    background-color: #ffa800 !important;
    border-color: #ffa800 !important;
  }

  .scroll_followIsHide {
    color: #ffa800;
  }
}
</style>
