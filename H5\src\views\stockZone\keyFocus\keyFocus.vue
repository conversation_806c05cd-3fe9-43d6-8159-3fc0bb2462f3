<template>
    <div class="card">

        <div class='  container_ '>
            <div class="container__header">
                <div>
                    <van-popover placement="right-start">
                        <div class="popoverBody">
                            <li>每日18:00前展示T-3数据，18:00后更新T-2数据</li>
                           

                        </div>
                        <template #reference>
                            <img src="./assets/keyFocus.png" alt="" srcset="">
                            <span class="ml-6">重点关注</span>
                            <van-icon class="ml-3" name="question" color="#1A76FF" />
                        </template>
                    </van-popover>
                </div>


                <div class="selectOption">
                    <div class="selectOption-item" @click="calendarIsShow = true">
                        <span>{{ time ? time : '请选择时间' }}</span>
                        <van-icon name="play" class="play" color="#1A76FF" />
                    </div>
                </div>


            </div>
            <div class="container_body">
                <div class="ml-20 fs18 ">{{ showOrgName }}</div>


                <div class="optionList">
                    <div class="item" v-for="item in optionList" :key="item.text">
                        <div class="value" :style="{ color: item.color }">{{ item.value }}</div>
                        <div class="text">{{ item.text }}</div>

                    </div>
                </div>

                <van-row gutter="5" class="table-header">
                    <van-col class="row-item" :span="item.span" v-for="item in rowList">{{ item.name }}</van-col>
                </van-row>
                <van-row gutter="5" class="table-body" v-for="col, index in tableData" :key="index">
                    <van-col class="col-item" v-getName="{ item: col, props: item.props }" :span="item.span"
                        v-for="item in rowList">{{
                            col[item.props] }}</van-col>
                </van-row>
                <van-empty description="暂无数据" v-if="tableData.length == 0" />

            </div>



        </div>

        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />
    </div>
</template>

<script setup lang="ts">
import { useUserInfo } from "@/stores/userInfo";
import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { getKeyFocusAPI } from "@/api/home";
import { on } from "events";
interface Option {
    text: string;  // 选项文本
    value: number;  // 选项值，假设为字符串
    color: string;  // 颜色的十六进制代码
}
interface OptionList {
    monthlyLossCount?: Option | number; // 月流失数
    monthlyLossRate?: Option | number; // 月流失率，百分比（如：0.05 表示 5%）
    totalLossCount?: Option | number; // 累计流失数
    totalLossRate?: Option | number; // 累计流失率，百分比
    dailyActiveDisconnection?: Option | number; // 当日主动拆机
    monthlyTotalDisconnection?: Option | number; // 累计当月拆机
    broadbandTotalDisconnection?: Option | number; // 累计宽带拆机
    [key: string]: any
}

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    onConfirm,
} = searchHooks({
    source: 'keyFocus',
    callback: loadList
})

const userInfoStore = useUserInfo()

const optionList = reactive<OptionList>({
    dayCount: {
        text: '当日主动拆机',
        value: 0,
        color: '#1A76FF',
    },
    monthCount: {
        text: '累计当月拆机',
        value: 0,
        color: '#00AFA3',
    },
    kdDismantle: {
        text: '累计宽带拆机',
        value: 0,
        color: '#FFA34D',
    },

})




const rowList: RowItem[] = [
    {
        span: 5,
        name: '门店',
        props: 'staffName',
        default: ''
    },

    {
        span: 5,
        name: '月流失数',
        props: 'monthlyLossCount',
        default: ''

    },
    {
        span: 4,
        name: '月流失率',
        props: 'monthlyLossRate',
        default: 0

    },


    {
        span: 5,
        name: '累计流失数',
        props: 'totalLossCount',
        default: 0

    },
    {
        span: 5,
        name: '累计流失率',
        props: 'totalLossRate',
        default: 0

    },
]

const tableData = ref<OptionList[]>([

    {
        staffName: '宝燕中路营业厅',
        monthlyLossCount: 10,
        monthlyLossRate: 15,
        totalLossCount: 7,
        totalLossRate: 9,
    },
    {
        staffName: '金燕路社区店',
        monthlyLossCount: 11,
        monthlyLossRate: 0,
        totalLossCount: 4,
        totalLossRate:3,
    },
    {
        staffName: '燕江园菜鸟驿站代办点',
        monthlyLossCount: 11,
        monthlyLossRate: 12,
        totalLossCount: 1,
        totalLossRate: 10,
    },

])


const showOrgName = computed(() => {
    return userInfoStore.userInfo.showOrgName
})
const time = computed(() => {
    if (formline.startTime && formline.endTime) return formline.startTime == formline.endTime ? `${formline.startTime}` : `${formline.startTime} ~ ${formline.endTime}`


    else return ``
})

function loadList() {
    let params = {
        startTime: formline.startTime ? `${formline.startTime} 00:00:00` : undefined,
        endTime: formline.endTime ? `${formline.endTime} 23:59:59` : undefined,
    }
    getKeyFocusAPI(params).then((res: any) => {
        if (res.code == 200) {
            optionList.dayCount.value = res.data.dayCount
            optionList.monthCount.value = res.data.monthCount
            optionList.kdDismantle.value = res.data.kdDismantle
        }


    })


}
onMounted(() => {
    loadList()
})
</script>

<style lang="scss" scoped>
.card {
    min-height: 100%;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
}

.card-header {

    // padding: 2px 5px;

    display: flex;
    align-items: center;
    justify-content: center;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

    .selectOption-item {
        flex: 1 0 auto;
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
            position: absolute;
            right: 10px;
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }
}


.optionList {

    background: #EDF5FF;
    border-radius: 5px;

    display: flex;

    flex-wrap: wrap;

    margin-top: 18px;
    margin-bottom: 18px;

    .item {
        flex: 1 0 33.3333%;
        max-width: 33.3333%;
        display: flex;
        height: 100%;
        padding: 15px 5px;
        flex-direction: column;
        place-content: center;
        text-align: center;

        .text {
            font-weight: 500;
            font-size: 12px;
            color: #3D3D3D;
        }

        .value {
            font-family: 'DIN, DIN';
            font-weight: 700;
            font-size: 23px;
            color: #6282FD;
            margin-bottom: 7px;
        }
    }


}


.row-item {
    font-weight: 400;
    font-size: 12px;
    color: #2079FF;
    text-align: center;
}

.table-header {
    border-bottom: 1px solid #EDF5FF;
    padding-bottom: 10px;
}

.table-body {
    border-bottom: 1px solid #EDF5FF;
    padding: 10px 0px;
}

.col-item {
    font-weight: 400;
    font-size: 12px;
    color: #3D3D3D;
    text-align: center;
}


.selectOption {
    display: flex;
    align-items: center;

    & .selectOption-item {
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
        }
    }

    & .selectOption-item:active {
        background: #d8dee6;
    }

}
</style>