
<template>
    <svg class="iconfont-js" :style="{ 'font-size': size + 'px', color: color }" aria-hidden="true">
  
      <use :xlink:href="iconClass">
        <title>{{ customTitle }}</title>
      </use>
    </svg>
  </template>
  
  <script>


  export default {
    name: "iconfontSvg",
    props: {
  
      icon: {
        type: String,
        required: true,
      },
      color: {
        type: String,
        default: "",
      },
  
      size: {
        type: [String,Number],
        default: "",
      },
  
      customTitle: {
        type: String,
        default: ''
      }
    },
    computed: {
      iconClass() {
        return `#${this.icon}`;
      },
    },
  };
  </script>
  
  <style>
  .iconfont-js {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }
  </style>
  