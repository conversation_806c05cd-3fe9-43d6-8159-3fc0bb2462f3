<template>
    <div>
        <div class="card-header">
            <div class="selectOption-item" @click="calendarIsShow = true">
                <span>{{ time ? time : '请选择时间' }}</span>
                <van-icon name="play" class="play" color="#1A76FF" />
            </div>
        </div>
        <div class=' container_ mt-6'>
            <div class="container__header">
                <div>

                    <van-popover placement="bottom-start">
                        <div class="popoverBody">
                            <li>该数据由客运提供</li>
                            <li>默认时间下18点前为T-3的当日累计数据。</li>
                            <li>18点之后为T-2的当日累计数据</li>
                        </div>
                        <template #reference>
                            <img src="./assets/actionPoins.png" alt="" srcset="">
                            <span class="ml-6">{{ showOrgName }}存量积分:</span>
                            <span class="ml-5">{{ tableData.totalPoints }}</span>
                            <van-icon class="ml-3" name="question" color="#1A76FF" />
                        </template>
                    </van-popover>
                </div>

            </div>
            <div class="table-container">
                <div>
                    <div class="header">
                        <span class="title">结果类</span>
                        <div class="fs12" style="color:#2079FF" @click="goToDetail">查看详情 <van-icon name="arrow"
                                color="#2079FF" />
                        </div>
                    </div>
                    <myCharts class="myEcharts" :options="resultOption"></myCharts>
                </div>


            </div>
            <!-- <van-empty description="暂无数据" class="mt-8" style="background-color: #fff;border-radius: 15px;"
                v-if="tableData.length == 0" /> -->
        </div>

        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />
    </div>
</template>

<script setup lang="ts">

import { actionPoinsHook } from "./hooks/actionPoins.ts";
import { useUserInfo } from "@/stores/userInfo";
import myCharts from "@/components/charts/myCharts.vue";

const userInfoStore = useUserInfo();

const showOrgName = computed(() => {
    return userInfoStore.userInfo.orgName
})





const {
    resultOption,
    actionOption,
    tableData,

    goToDetail,
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    onConfirm,
    loadActionPointsCount,
} = actionPoinsHook()
















const time = computed(() => {
    if (formline.startTime && formline.endTime) return `${formline.startTime} ~ ${formline.endTime}`
    else return ``
})




onMounted(() => {
    loadActionPointsCount()
})
</script>

<style lang="scss" scoped src="./index.scss"></style>