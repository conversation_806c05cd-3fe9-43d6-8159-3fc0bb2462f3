
import request from "../utils/request";
import configAxios from './config'

//方案递单保存
export function proposalSaveAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/proposal/save`,
        method: "post",
        data,
    });
}

//方案递单列表
export function getProposalListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/proposal/page`,
        method: "post",
        data,
    });
}
