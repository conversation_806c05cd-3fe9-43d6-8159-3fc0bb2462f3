<template>
    <div class='Headlines container_ mt-16'>
        <div class="container__header">
            <div>
                <img src="./assets/resultOptimization.svg" alt="" srcset="">
                <span class="ml-6">结果保障</span>
                <div class="selectOption">
                    <van-popover :actions="roleList" @select="onSelect" placement="bottom" class="cusPopover">
                        <template #reference>
                            <div class="selectOption-item  ml-25">
                                <span>{{ curSelectType.text }}</span>
                                <van-icon name="play" class="play" color="#1A76FF" />
                            </div>
                        </template>
                    </van-popover>
                </div>
            </div>
            <van-popover :actions="actions" @select="(params) => params.handleClick()" placement="bottom-end"
                class="moreSelect">
                <template #reference>
                    <div class="selectOption-item">
                        <van-icon name="ellipsis" size="18" color="#1A76FF" />
                    </div>
                </template>
            </van-popover>


        </div>


        <div class="container_body" id='resultOptimization'>

            <div class="row" style="background-color: #fff; padding: 6px 12px;">
                <div class="list-item">
                    <div class="fs13" style="font-weight: 700;color: #3D3D3D;">排序</div>
                    <div class="areaNAme fs13" style="font-weight: 700;color: #3D3D3D;">单元</div>
                    <div class="left">
                        <div>活力值</div>
                    </div>
                </div>

            </div>
            <div class="row" v-for="item, index in list" :key="item.id" @click="GO(item)">
                <div class="list-item">
                    <div class="top" :class="[0, 1, 2].includes(index) ? `top${index + 1}` : ''">{{ index + 1 }}</div>
                    <div class="areaNAme">{{ item.name }}</div>
                    <div class="left">
                        <div>{{ item.avgHuoli }}</div>
                    </div>
                </div>

            </div>


            <van-empty description="暂无数据" v-if="list.length == 0" />
        </div>
    </div>
</template>

<script setup lang='ts'>
import { resultOptimizationHooks } from "./hooks.ts";
import { useRouter } from "vue-router";
import { AreaPriorityMap } from "@/utils/mapSort.ts";
import { domToJpeg, domToPng, domToSvg, domToBlob, domToDataUrl, domToImage } from 'modern-screenshot';
import viewDetails from "./assets/viewDetails.png";
import screenShotPng from "./assets/screenShot.png";
const router = useRouter()
const {
    roleList,
    curSelectType,
    tableList,
    onSelect,
    GO,
    resultOptimizationList,

} = resultOptimizationHooks()

const actions = [
    { text: '查看详情', color: '#1A76FF', icon: viewDetails, handleClick: GOresultOptimizationdDetail },
    { text: '一键截屏分享', color: '#1A76FF', icon: screenShotPng, handleClick: screenShot },

];



function GOresultOptimizationdDetail() {
    router.push({
        path: '/resultOptimizationdDetailArea'
    })
}



async function screenShot() {
    const node = document.getElementById('resultOptimization') as HTMLElement;

    const toast = showLoadingToast({
        message: '加载中...',
        forbidClick: true,
    });

    domToBlob(node, {
        backgroundColor: '#fff',
        scale: 5,
    })
        .then((blob) => {
            const file = new File([blob], '结果保障.png', { type: 'image/png' });
            if (navigator.canShare && navigator.canShare({ files: [file] })) {
                navigator.share({
                    files: [file],
                    title: '结果保障',
                    text: '保存或分享这张图片',
                });
            } else {

                //将blob转为base64  
                const reader = new FileReader();
                reader.readAsDataURL(blob);
                reader.onloadend = () => {
                    const result = reader.result as string;
                    showImagePreview([result]);
                }


            }
        }).finally(() => {
            toast.close()
        })





}

const list = computed(() => {
    return tableList.value.filter((item: any) => AreaPriorityMap[item.name] !== undefined)


})


onMounted(() => {
    resultOptimizationList()
})
</script>

<style lang="scss" scoped src="./index.scss"></style>
<!-- <style lang="scss">
@use './index.scss';
</style> -->