<template>
  <component :is="type" v-bind="linkProps(to)">
    <slot />
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { isExternal } from '@/utils/validate.ts'

const props = defineProps<{ to: string }>()

const isExternalLink = computed(() => isExternal(props.to))

const type = computed(() => {
  return isExternalLink.value ? 'a' : 'router-link'
})

const linkProps = (to: string) => {
  if (isExternalLink.value) {
    return {
      href: to,
      target: '_blank',
      rel: 'noopener'
    }
  }
  return {
    to: to
  }
}
</script>
