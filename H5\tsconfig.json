{
  "compilerOptions": {
    "allowJs": true,
    "target": "ESNext",
    "experimentalDecorators": true, //启用对装饰器的实验性支持。
    "emitDecoratorMetadata": true, //为装饰器提供的类型信息生成元数据。
    "allowImportingTsExtensions": true,
    "useDefineForClassFields": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "strict": true,
    "jsx": "preserve",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "lib": [
      "ESNext",
      "DOM"
    ],
    "skipLibCheck": true,
    "noEmit": true,
    "baseUrl": "." /* 用于设置基础 url，可以帮我们省掉一些多余的路径前缀。 */,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "strictFunctionTypes": true,
    "paths": {
      "@": [
        "src"
      ],
      "@/*": [
        "src/*"
      ]
    } /* 将导入重新映射到相对于“baseUrl”的查找位置的一系列条目。 */
  },
  "include": [
    "src/**/*.ts",
    "src/**/*.d.ts",
    "src/**/*.tsx",
    "src/**/*.vue",
    "types/**/*.d.ts",
    "types/**/*.ts",
  ],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}