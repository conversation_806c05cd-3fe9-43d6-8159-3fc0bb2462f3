import { ComponentOptions } from 'vue';

import userInfo from "./userInfo/index.vue";

//今日头条
import Headlines from "./Headlines/index.vue";
import headlinesLead from "./Headlines/headlinesLead.vue";
import headlinesArea from "./Headlines/headlinesArea.vue";
import headlinesDept from "./Headlines/headlinesDept.vue";

import headlinesLeadGov from "./Headlines/headlinesLeadGov.vue";
import headlinesGov from "./Headlines/headlinesGov.vue";



//我的一天
import MyDay from "./MyDay/index.vue";



//存量专区
import stockZone from "@/views/template/stockZone/index.vue";


//我的贡献 触点端
import Contribution from "./Contribution/index.vue";
// 我的贡献 部门领导
import ContributionDept from "./Contribution/dept/indexDept.vue";
// 我的贡献 市局领导
import ContributionArea from "./Contribution/area/indexArea.vue";

//常用功能
import commonlyUsed from "./commonlyUsed/index.vue";

// 过程管控
import processContro from "./processContro/index.vue";


//结果保障
import resultOptimizationArea from "./resultOptimization/area.vue";
import resultOptimizationDept from "./resultOptimization/dept.vue";


//发展情况
import development from "./development/index.vue";
import developmentLead from "./development/indexLead.vue";
import developmentAreaAndDept from "./development/developmentAreaAndDept.vue";

/* 支局长search */
import morningLogSearch from "./search/morningLogSearch/index.vue";
import reviewLogSearch from "./search/reviewLogSearch/index.vue";
import comparisonLogSearch from "./search/comparisonLogSearch/index.vue";

import stock from "@/views/stock/index.vue";



//发展情况
import highPlanLock from "./highPlanLock/highPlanLock.vue";
import highPlanLockLead from "./highPlanLock/highPlanLockLead.vue";
import highPlanLockDept from "./highPlanLock/highPlanLockDept.vue";
import highPlanLockArea from "./highPlanLock/highPlanLockArea.vue";


const componentList: ComponentOptions<any, any, any> = {
    userInfo: {
        //个人信息
        moudule_: userInfo,
    },


    //#region 今日头条
    headlinesLead: {
        //今日头条
        moudule_: headlinesLead,
    },
    Headlines: {
        //今日头条-个人
        moudule_: Headlines,
    },

    headlinesArea: {
        //今日头条-公司领导
        moudule_: headlinesArea,

    },
    headlinesDept: {
        //今日头条-区域领导
        moudule_: headlinesDept,
    },
    headlinesLeadGov: {
        //今日头条-政企
        moudule_: headlinesLeadGov,
    },
    headlinesGov: {
        //今日头条-政企
        moudule_: headlinesGov,
    },
    //#endregion今日头条 EEEEEEEEEEEEE

    // 过程管控
    processContro: {

        moudule_: processContro,
    },


    // 结果保障 - 公司领导
    resultOptimizationArea: {
        moudule_: resultOptimizationArea,
    },
    // 结果保障 - 区域领导
    resultOptimizationDept: {
        moudule_: resultOptimizationDept,
    },


    MyDay: {
        //我的一天
        moudule_: MyDay,
        roleList: ['']
    },
    stockZone: {
        moudule_: stockZone,
        roleList: ['']
    },
    Contribution: {
        //我的贡献
        moudule_: Contribution,
    },
    ContributionDept: {
        //我的贡献 - 管理端
        moudule_: ContributionDept,
    },
    ContributionArea: {
        //我的贡献 - 管理端
        moudule_: ContributionArea,
    },

    commonlyUsed: {
        //常用功能
        moudule_: commonlyUsed,
        roleList: ['']
    },





    development: {
        // 发展情况
        moudule_: development,
        roleList: ['']
    },
    developmentLead: {
        //发展情况 领导
        moudule_: developmentLead,
        roleList: ['']
    },
    developmentAreaAndDept: {
        //发展情况 领导
        moudule_: developmentAreaAndDept,
    },


    /* 局长检索 模块 */
    morningLogSearch: {
        // 晨会日志检索
        moudule_: morningLogSearch,
        roleList: ['']
    },
    reviewLogSearch: {
        // 复盘日志检索
        moudule_: reviewLogSearch,
        roleList: ['']
    },
    comparisonLogSearch: {
        // 比算检索
        moudule_: comparisonLogSearch,
        roleList: ['']
    },
    stock: {
        // 比算检索
        moudule_: stock,
        roleList: ['']
    },


    //#region  高套封闭 模块
    highPlanLock: {
        // 高套封闭 一线
        moudule_: highPlanLock,
        roleList: ['']
    },
    highPlanLockLead: {
        // 高套封闭 分局长
        moudule_: highPlanLockLead,
        roleList: ['']
    },
    highPlanLockDept: {
        // 高套封闭 部门
        moudule_: highPlanLockDept,
        roleList: ['']
    },
    highPlanLockArea: {
        // 高套封闭 全区 
        moudule_: highPlanLockArea,
        roleList: ['']
    },
    //#endregion 
}
export default componentList