import moment from 'moment';


function transferGMTTime(time:string) {
    return moment(time).format('YYYY-MM-DD HH:mm:ss');
}
const transferUTCTime = (time:Date, type = 'YYYY-MM-DD HH:mm:ss') => {
    time = new Date(time);
    return moment.utc(time).local().format(type);
};
const transferUTCTimeDay = (time:Date, type = 'YYYY-MM-DD') => {
    const  timestamp  = time.getTime() + 24 * 60 * 60 * 1000;
    return moment.utc(timestamp).local().format(type);
};
function handleTimeStamp(timestamp:any) {
    let date = new Date(parseInt(timestamp) * 1000);
    let YY = date.getFullYear() + '-';
    let MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
    let DD = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    let hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
    let mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
    let ss = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
    return YY + MM + DD + ' ' + hh + mm + ss;
}

function echartsHandleTimeStamp(timestamp:string) {
    let date = new Date(parseInt(timestamp));
    let YY = date.getFullYear() + '-';
    let MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
    let DD = date.getDate() < 10 ? '0' + date.getDate() : date.getDate();
    let hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
    let mm = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
    let ss = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
    return MM + DD + ' ' + hh + mm;
    // return hh + mm;
}
function getQueryString(name:string) {
    let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
    let r = window.location.search.substr(1).match(reg);
    if (r != null) {
        return unescape(r[2]);
    }
    return null;
}

function getfilesize(size:any, type = 'B') {
    if (!size && size !== 0) return '';
    var num = 1024.0; //byte
    size = Number(size).toFixed(2);
    if (type === 'kb') {
        if (size < num) return size + ' kB';
        if (size < Math.pow(num, 2)) return (size / num).toFixed(2) + ' MB'; //kb
        if (size < Math.pow(num, 3)) return (size / Math.pow(num, 2)).toFixed(2) + ' GB'; //M
        return (size / Math.pow(num, 3)).toFixed(2) + ' TB'; //G
    } else {
        if (size < num) return size + ' B';
        if (size < Math.pow(num, 2)) return (size / num).toFixed(2) + ' KB'; //kb
        if (size < Math.pow(num, 3)) return (size / Math.pow(num, 2)).toFixed(2) + ' MB'; //M
        if (size < Math.pow(num, 4)) return (size / Math.pow(num, 3)).toFixed(2) + ' GB'; //G
        return (size / Math.pow(num, 4)).toFixed(2) + ' TB'; //T
    }
}
function getfilesize2(size:any) {
    if (!size && size !== 0) return '';
    var num = 1024.0; //byte
    size = Number(size).toFixed(2);
    if (size < num) return size + ' GB'; //G
    return (size / Math.pow(num, 1)).toFixed(2) + ' TB'; //T
}

function numberToThousands(num:any) {
    if (num === undefined) {
        return '--';
    }
    const parts = num.toString().split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return parts.join('.');
}


function handleExprie(item:any) {
    let created = new Date(item.order_created_at).getTime();
    let week = item.week;
    week = week * 7 * 24 * 3600 * 1000;
    let exprie = created + week;
    exprie = transferGMTTime(exprie);
    return exprie;
}




function formatNumber(number:any) {
    // 先判断输入是否是有效数字
    if (isNaN(number)) {
        return { integerPart: 0, decimalPart: 0 };
    }

    // 将数字切分为整数和小数部分
    const parts = number.toString().split('.');

    // 整数部分
    let integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    // 小数部分，保留4位小数
    let decimalPart = parts[1] ? parseFloat(`0.${parts[1]}`).toFixed(4).substring(2) : '0000';

    // 返回整数和小数部分的组合
    return { integerPart, decimalPart };
}
const getType = (fileName: string) => {
    fileName = fileName.toLowerCase();
    if (
        fileName.endsWith(".jpeg") ||
        fileName.endsWith(".jpg") ||
        fileName.endsWith(".png") ||
        fileName.endsWith(".svg") ||
        fileName.endsWith('.bmp') ||
        fileName.endsWith('.gif') ||
        fileName.endsWith('.ico') ||
        fileName.endsWith('.webp') ||
        fileName.endsWith('.heif') ||
        fileName.endsWith('.psd')
    ) {
        return 1;
    } else if (fileName.endsWith('.mp4') || fileName.endsWith('.avi') || fileName.endsWith('.mov')) {
        return 2;
    } else if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) {
        return 4;
    } else if (fileName.endsWith('.zip') || fileName.endsWith('.rar') || fileName.endsWith('.gz') || fileName.endsWith('.tar')) {
        return 5;
    } else if (fileName.endsWith('.cmd')) {
        return 5;
    } else if (fileName.endsWith('.css')) {
        return 5;
    } else if (fileName.endsWith('.mp3')) {
        return 3;
    } else if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls') || fileName.endsWith('.csv')) {
        return 4;
    } else if (fileName.endsWith('.pdf')) {
        return 4;
    } else if (fileName.endsWith('.ppt') || fileName.endsWith('.pptx')) {
        return 4;
    } else if (fileName.endsWith('.text') || fileName.endsWith('.txt') || fileName.endsWith('.md')) {
        return 4;
    } else if (fileName.endsWith('.html')) {
        return 5;
    } else if (fileName.endsWith('/')) {
        return 5;
    } else {
        return 5;
    }
};

function generateUniqueId() {
    const timestamp = (new Date()).getTime().toString(16);
    const random = Math.random().toString(16).substring(2);
    return `${timestamp}-${random}`;
}


export {

    transferUTCTime,
    handleTimeStamp,
    echartsHandleTimeStamp,
    getQueryString,
    getfilesize,
    getfilesize2,
    numberToThousands,
    transferUTCTimeDay,
    transferGMTTime,
    formatNumber,
    handleExprie,
    getType,
    generateUniqueId,
};
