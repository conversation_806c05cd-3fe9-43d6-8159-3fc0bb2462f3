import { RouteRecordRaw } from "vue-router";
import Layout from "@/layout/index.vue";
import { useUserInfo } from "@/stores/userInfo";


/**
 * 定义动态路由
 * 前端添加路由，请在顶级节点的 `children 数组` 里添加
 * @description 未开启 isRequestRoutes 为 true 时使用（前端控制路由），开启时第一个顶级 children 的路由将被替换成接口请求回来的路由数据
 * @description 各字段请查看 `/@/views/system/menu/component/addMenu.vue 下的 ruleForm`
 * @returns 返回路由菜单数据
 */
export const dynamicRoutes: Array<RouteRecordRaw> = [
  {
    path: "/",
    redirect: "/home",
    component: Layout,
    children: [
      {
        path: "/home",
        name: "home",
        component: () => import("@/views/home/<USER>"),
        meta: { keepAlive: false, isTabbarView: true, title: '首页' },
      },

      {
        path: "/search",
        name: "search",
        component: () => import("@/views/search/index.vue"),
        meta: { keepAlive: true, isTabbarView: true, title: '检索', },
      },
      {
        path: "/userData",
        name: "userData",
        component: () => import("@/views/userData/index.vue"),
        meta: { keepAlive: false, isTabbarView: true, title: '数据', },
      },
      {
        path: "/message",
        name: "message",
        component: () => import("@/views/message/index.vue"),
        meta: { keepAlive: false, isTabbarView: true, title: '消息' },
      },
      {
        path: "/messageLead",
        name: "messageLead",
        component: () => import("@/views/message/messageLead.vue"),
        meta: { keepAlive: false, isTabbarView: true, title: '消息' },
      },
      {
        path: "/editMessage",
        name: "editMessage",
        component: () => import("@/views/message/editMessage.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: '编辑消息' },
      },
      {
        path: "/personalMessageList",
        name: "personalMessageList",
        component: () => import("@/views/message/personalMessageList.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: '个人消息列表' },
      },
      {
        path: "/personalMessageDetail",
        name: "personalMessageDetail",
        component: () => import("@/views/message/personalMessageDetail.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: '个人消息详情' },
      },
    ]
  },

  {
    path: "/",

    component: Layout,
    children: [
      {
        // 存量
        path: "/stock",
        name: "stock",
        component: () => import("@/views/stock/index.vue"),
        meta: { keepAlive: false, isTabbarView: true, title: "存量" },
      },
      {
        // 存量-下钻列表
        path: "/stockList",
        name: "stockList",
        component: () => import("@/views/stock/stockList.vue"),
        meta: { keepAlive: false, isTabbarView: true, title: "存量-下钻列表" },
      },

    ]
  },
  {
    path: "/",
    component: Layout,
    children: [
      {
        // 一线出征
        path: "/signInP",
        name: "signInP",
        component: () => import("@/views/signIn/signInP/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "晨会出征" },
      },
      {
        // 管理层
        path: "/signInG",
        name: "signInG",
        component: () => import("@/views/signIn/signInG/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "晨会出征" },
      },
      {
        path: "/signInDiary",
        name: "signInDiary",
        component: () => import("@/views/signIn/signInDiary/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "打卡日志" },
      },
      {
        path: "/signInArea",
        name: "signInArea",
        component: () => import("@/views/signIn/signInArea/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "公司领导晨会出征" },
      },
      {
        path: "/signInDept",
        name: "signInDept",
        component: () => import("@/views/signIn/signInDept/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "区域领导晨会出征" },
      },
      {
        path: "/signInBranchDetail",
        name: "signInBranchDetail",
        component: () => import("@/views/signIn/signInBranchDetail/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "分局打卡情况详情" },
      },
    ]
  },
  {
    path: "/",
    component: Layout,
    children: [
      {
        // 比算  一线
        path: "/comparativeCalculationP",
        name: "comparativeCalculationP",
        component: () => import("@/views/comparativeCalculation/comparativeCalculationP/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "比算" },
      },
      {
        // 比算-管理端
        path: "/comparativeCalculationG",
        name: "comparativeCalculationG",
        component: () => import("@/views/template/search/comparisonLogSearch/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "比算" },
      },
      {
        // 查看比算
        path: "/comparativeCalculationDetail",
        name: "comparativeCalculationDetail",
        component: () => import('@/views/comparativeCalculation/comparativeCalculationDetail/index.vue'),
        meta: { keepAlive: false, isTabbarView: false, title: "查看比算" },
      },
      {
        // 比算审核
        path: "/comparativeCalculationAudit",
        name: "comparativeCalculationAudit",
        component: () => import('@/views/comparativeCalculation/comparativeCalculationAudit/index.vue'),
        meta: { keepAlive: false, isTabbarView: false, title: "比算审核" },
      },

    ]
  },
  {
    path: "/",
    component: Layout,
    children: [
      {
        path: "/debriefingP",
        name: "debriefingP",
        component: () => import("@/views/debriefing/debriefingP/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "复盘" },
      },
      {
        path: "/debriefingG",
        name: "debriefingG",
        component: () => import("@/views/debriefing/debriefingG/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "复盘" },
      },
      {
        path: "/debriefingDiary",
        name: "debriefingDiary",
        component: () => import("@/views/debriefing/debriefingDiary/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "复盘记录" },
      },
      {
        path: "/middayReviewDept",
        name: "middayReviewDept",
        component: () => import("@/views/debriefing/dept/middayReviewDept.vue"),
        meta: { keepAlive: false, isTabbarView: false, dept: " 晚间复盘 区域领导" },
      },
      {
        path: "/eveningReviewDept",
        name: "eveningReviewDept",
        component: () => import("@/views/debriefing/dept/eveningReviewDept.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "午间复盘 区域领导" },
      },
      {
        path: "/middayReviewArea",
        name: "middayReviewArea",
        component: () => import("@/views/debriefing/area/middayReviewArea.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: " 晚间复盘 公司领导" },
      },
      {
        path: "/eveningReviewArea",
        name: "eveningReviewArea",
        component: () => import("@/views/debriefing/area/eveningReviewArea.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "午间复盘 公司领导" },
      }
    ]
  },
  {
    path: "/",
    component: Layout,
    children: [
      {
        path: "/touchCheckIn",
        name: "touchCheckIn",
        component: () => import("@/views/touchpoint/touchCheckIn/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "触点打卡" },
      },
      {
        path: "/touchAudit",
        name: "touchAudit",
        component: () => import("@/views/touchpoint/touchAudit/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "触点审核" },
      },
      {
        path: "/touchDetail",
        name: "touchDetail",
        component: () => import("@/views/touchpoint/touchDetail/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "触点打卡详情" },
      },
    ]
  },
  {
    path: "/",
    component: Layout,
    children: [

      {
        // 管理端 商机单
        path: "/opportunityG",
        name: "opportunityG",
        component: () => import("@/views/opportunity/opportunityG/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "商机单" },
      },
      {
        // 一线 商机单 
        path: "/opportunityP",
        name: "opportunityP",
        component: () => import("@/views/opportunity/opportunityP/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "商机单" },
      },
      {
        // 基础商机录入 
        path: "/opportunityEntry",
        name: "opportunityEntry",
        component: () => import("@/views/opportunity/opportunityEntry.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "基础商机录入" },
      },
      {
        // 基础商机录入 详情
        path: "/entryDetailList",
        name: "entryDetailList",
        component: () => import("@/views/opportunity/entryDetailList.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "基础商机录入 详情" },
      },
      {
        // 分局长商机审核
        path: "/businessAudit",
        name: "businessAudit",
        component: () => import("@/views/opportunity/businessAudit/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "商机审核" },
      }
    ]
  },

  {
    path: "/",
    component: Layout,
    children: [
      {
        // 派单详情 - 一线
        path: "/sendOrdersDetail",
        name: "sendOrdersDetail",
        component: () => import("@/views/sendOrders/sendOrdersDetail.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "派单详情" },
      },
      {
        // 派单详情 -领导
        path: "/sendOrdersDetailLead",
        name: "sendOrdersDetailLead",
        component: () => import("@/views/sendOrders/sendOrdersDetailLead.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "派单详情" },
      },
      {
        // 派单 - 一线
        path: "/sendOrders",
        name: "sendOrders",
        component: () => import("@/views/sendOrders/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "派单" },
      },
      {
        // 派单 - 领导
        path: "/sendOrdersLead",
        name: "sendOrdersLead",
        component: () => import("@/views/sendOrders/sendOrdersLead.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "派单" },
      },
      {
        // 派单人员详情
        path: "/sendOrdersList",
        name: "sendOrdersList",
        component: () => import("@/views/sendOrders/sendOrdersList.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "派单人员详情" },
      },
      {
        // 派单类别人员详情
        path: "/sendOrdersDetailList",
        name: "sendOrdersDetailList",
        component: () => import("@/views/sendOrders/sendOrdersDetailList.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "派单类别人员详情" },
      },

      {
        // 派单类别人员详情
        path: "/sendOrdersZoneContact",
        name: "sendOrdersZoneContact",
        component: () => import("@/views/sendOrders/zoneContact.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "派单类别人员详情" },
      },
      {
        // 派单类别人员详情
        path: "/sendOrdersZoneContactDetail",
        name: "sendOrdersZoneContactDetail",
        component: () => import("@/views/sendOrders/zoneContactDetail.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "派单类别人员详情" },
      },
    ]
  },
  {
    path: "/",
    component: Layout,
    children: [

      {
        // 今日头条-详情
        path: "/headlinesDetailArea",
        name: "headlinesDetailArea",
        component: () => import("@/views/Headlines/detailsArea.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "今日头条详情-公司领导" },
      },
      {
        // 今日头条-详情
        path: "/headlinesDetailDept",
        name: "headlinesDetailDept",
        component: () => import("@/views/Headlines/detailsDept.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "今日头条详情-区域领导" },
      },
      {
        // 今日头条-详情
        path: "/headlinesList",
        name: "headlinesList",
        component: () => import("@/views/Headlines/list.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "积分完成详情" },
      },
      {
        // 今日头条-指标编辑
        path: "/headlinesSetTarget",
        name: "headlinesSetTarget",
        component: () => import("@/views/Headlines/setTarget.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "指标编辑" },
      },
    ]
  },
  {
    path: "/",
    component: Layout,
    children: [

      {
        // 当前产能 公司领导
        path: "/curCapacityArea",
        name: "curCapacityArea",
        component: () => import("@/views/curCapacity/curCapacityArea/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "当前产能 公司领导" },
      },
      {
        // 当前产能 区域领导
        path: "/curCapacityDept",
        name: "curCapacityDept",
        component: () => import("@/views/curCapacity/curCapacityDept/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: " 当前产能 区域领导" },
      },
    ]
  },
  {
    path: "/",
    component: Layout,
    children: [

      {
        // 结果保障 分局列表
        path: "/resultOptimizationdDetailList",
        name: "resultOptimizationdDetailList",
        component: () => import("@/views/template/resultOptimization/detailList.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "结果保障 分局列表" },
      },
      {
        // 结果保障 区域领导查看敏捷版数据
        path: "/resultOptimizationdDetailDept",
        name: "resultOptimizationdDetailDept",
        component: () => import("@/views/template/resultOptimization/detailDept.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "结果保障 敏捷版" },
      },
      {
        // 结果保障公司领导查看敏捷版数据
        path: "/resultOptimizationdDetailArea",
        name: "resultOptimizationdDetailArea",
        component: () => import("@/views/template/resultOptimization/detailArea.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "结果保障 敏捷版" },
      },
    ]
  },
  {
    path: "/",
    component: Layout,
    children: [

      {
        // 存量专区-存量开门七件事
        path: "/sevenThings",
        name: "sevenThings",
        component: () => import("@/views/stockZone/sevenThings/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "存量开门七件事" },
      },
      {
        // 存量专区-以店包片
        path: "/storePackage",
        name: "storePackage",
        component: () => import("@/views/stockZone/storePackage/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "以店包片" },
      },
      {
        // 存量专区-七件事详情
        path: "/sevenThingsDetail",
        name: "sevenThingsDetail",
        component: () => import("@/views/stockZone/sevenThings/sevenThingsDetail.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "七件事详情" },
      },
      {
        // 存量专区-动作积分
        path: "/actionPoints",
        name: "actionPoints",
        component: () => import("@/views/stockZone/actionPoints/actionPoints.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "动作积分" },
      },
      {
        // 存量专区-动作积分
        path: "/actionPointsLead",
        name: "actionPointsLead",
        component: () => import("@/views/stockZone/actionPoints/actionPointsLead.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "动作积分-分局长" },
      },
      {
        // 存量专区-动作积分列表
        path: "/actionPointsList",
        name: "actionPointsList",
        component: () => import("@/views/stockZone/actionPoints/list.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "动作积分" },
      },
      {
        // 存量专区-重点关注
        path: "/keyFocus",
        name: "keyFocus",
        component: () => import("@/views/stockZone/keyFocus/keyFocus.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "重点关注 - 分局" },
      },
      {
        // 存量专区-小区攻防
        path: "/communityBattle",
        name: "communityBattle",
        component: () => import("@/views/stockZone/communityBattle/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "小区攻防" },
      },
      {
        // 存量专区-小区攻防-网格巡查记录 - 网格列表
        path: "/inspectionLogGridList",
        name: "inspectionLogGridList",
        component: () => import("@/views/stockZone/communityBattle/inspectionLogGridList.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "网格巡查记录" },
      },
      {
        // 存量专区-小区攻防-网格巡查记录
        path: "/inspectionLog",
        name: "inspectionLog",
        component: () => import("@/views/stockZone/communityBattle/inspectionLog.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "网格巡查记录" },
      },
      {
        // 存量专区-小区攻防-网格巡查记录 管理端
        path: "/inspectionLogMangement",
        name: "inspectionLogMangement",
        component: () => import("@/views/stockZone/communityBattle/inspectionLogMangement.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "网格巡查记录" },
      },
      {
        // 存量专区-小区攻防-网格巡查记录详情 管理端
        path: "/inspectionLogMangementDetail",
        name: "inspectionLogMangementDetail",
        component: () => import("@/views/stockZone/communityBattle/inspectionLogMangementDetail.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "网格巡查记录详情" },
      },
      {
        // 存量专区-小区攻防-网格巡查记录
        path: "/promoShots",
        name: "promoShots",
        component: () => import("@/views/stockZone/communityBattle/promoShots.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "宣传拍照" },
      },
      {
        // 存量专区-小区攻防-宣传检查结果查询列表
        path: "/communityDetailList",
        name: "communityDetailList",
        component: () => import("@/views/stockZone/communityBattle/communityDetailList.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "宣传检查结果查询" },
      },
      {
        // 存量专区-小区攻防-管理端-攻防详情
        path: "/communityBattleAreaList",
        name: "communityBattleAreaList",
        component: () => import("@/views/stockZone/communityBattle/communityBattleAreaList.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "攻防详情" },
      },
      {
        // 存量专区-小区攻防-管理端-攻防详情
        path: "/communityBattleArea",
        name: "communityBattleArea",
        component: () => import("@/views/stockZone/communityBattle/communityBattleArea.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "攻防详情" },
      },

      {
        // 存量专区-小区攻防-管理端-攻防详情
        path: "/communityBattleDept",
        name: "communityBattleDept",
        component: () => import("@/views/stockZone/communityBattle/communityBattleDept.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "攻防详情" },
      },

      {
        // 存量专区-以日保周
        path: "/daySecuresWeek",
        name: "daySecuresWeek",
        component: () => import("@/views/stockZone/daySecuresWeek/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "以日保周" },
      },
      {
        // 存量专区-派单SOP
        path: "/dispatchSOP",
        name: "dispatchSOP",
        component: () => import("@/views/stockZone/dispatchSOP/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "派单SOP" },
      },
      {
        // 存量专区-派单SOP
        path: "/dispatchSOPLead",
        name: "dispatchSOPLead",
        component: () => import("@/views/stockZone/dispatchSOP/dispatchSOPLead.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "派单SOP" },
      },
      {
        // 存量专区-消息提示
        path: "/tips",
        name: "tips",
        component: () => import("@/views/stockZone/daySecuresWeek/tips.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "消息提示" },
      },
      {
        // 存量专区-消息提示
        path: "/govStock",
        name: "govStock",
        component: () => import("@/views/stockZone/govStock/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "政企存量" },
      },
    ]
  },
  {
    path: "/",
    component: Layout,
    children: [

      {
        // 今日登录率 首页
        path: "/loginRate",
        name: "loginRate",
        component: () => import("@/views/loginRate/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "今日登录率" },
      },
      {
        // 今日登录率 分局列表
        path: "/loginRateAreaL2List",
        name: "loginRateAreaL2List",
        component: () => import("@/views/loginRate/areaL2List.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "今日登录率 分局列表" },
      },
      {
        // 今日登录率 人员列表
        path: "/loginRateAreaL2Staff",
        name: "loginRateAreaL2Staff",
        component: () => import("@/views/loginRate/areaL2Staff.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "今日登录率 人员列表" },
      },

      {
        // 今日登录率 人员列表
        path: "/chatBi",
        name: "chatBi",
        component: () => import("@/views/chatBi.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "chatBi" },
      },
    ]
  },



  {
    path: "/",
    component: Layout,
    children: [

      {
        // 方案递单
        path: "/planSubmission",
        name: "planSubmission",
        component: () => import("@/views/planSubmission/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "方案递单" },
      },
      {
        // 方案记录
        path: "/solutionRecord",
        name: "solutionRecord",
        component: () => import("@/views/planSubmission/record.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "方案记录" },
      },
      {
        // 方案详情
        path: "/solutionDetail",
        name: "solutionDetail",
        component: () => import("@/views/planSubmission/detailList.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "方案详情" },
      },
      {
        // 客户拜访
        path: "/customerVisit",
        name: "customerVisit",
        component: () => import("@/views/customerVisit/customerVisit.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "客户拜访" },
      },
      {
        // 客户拜访记录
        path: "/visitHistoryList",
        name: "visitHistoryList",
        component: () => import("@/views/customerVisit/visitHistoryList.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "客户拜访记录" },
      },
      {
        // 客户拜访- 工作数据收集
        path: "/workData",
        name: "workData",
        component: () => import("@/views/customerVisit/workData.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "工作数据收集" },
      },
      {
        // 客户拜访- 提交记录
        path: "/submissionLog",
        name: "submissionLog",
        component: () => import("@/views/customerVisit/submissionLog.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "提交记录" },
      },
      {
        // 客户拜访 - 分局长同角
        path: "/customerVisitLead",
        name: "customerVisitLead",
        component: () => import("@/views/customerVisit/customerVisitLead.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "客户拜访" },
      },
      {
        // 今任务助手
        path: "/taskAssistant",
        name: "taskAssistant",
        component: () => import("@/views/taskAssistant/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "任务助手" },
      },
    ]
  },
  {
    path: "/",
    component: Layout,
    children: [

      {
        // 客户派单
        path: "/dispatch",
        name: "dispatch",
        component: () => import("@/views/dispatch/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "客户派单" },
      },
      {
        // 客户派单
        path: "/dispatchEdit",
        name: "dispatchEdit",
        component: () => import("@/views/dispatch/edit.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "客户派单表单" },
      },
    ]
  },
  {
    path: "/",
    component: Layout,
    children: [

      {
        // 客户派单
        path: "/highPlanLockDetail",
        name: "highPlanLockDetail",
        component: () => import("@/views/template/highPlanLock/highPlanLockDetail.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "高套封闭详情" },
      },

    ]
  },
  {
    path: "/",
    component: Layout,
    children: [
      {
        // 网格礼包
        path: "/gridPackages",
        name: "gridPackages",
        component: () => import("@/views/template/MyDay/gridPackages/index.vue"),
        meta: { keepAlive: false, isTabbarView: false, title: "网格礼包" },
      },

    ]
  },
]




/**
 * 定义404、401界面
 * @link 参考：https://next.router.vuejs.org/zh/guide/essentials/history-mode.html#netlify
 */
export const notFoundAndNoPower = [
  {
    path: "/IDNotFound",
    name: "IDNotFound",
    hidden: true,
    component: () => import("@/views/IDNotFound.vue"),
    meta: {
      title: "未查询到统一工号",
    },
  },
  {
    path: "/:path(.*)*",
    name: "notFound",
    hidden: true,
    component: () => import("@/views/404.vue"),
    meta: {
      title: "404",
    },
  },
];

/**
 * 定义静态路由（默认路由）
 * 此路由不要动，前端添加路由的话，请在 `dynamicRoutes 数组` 中添加
 * @description 前端控制直接改 dynamicRoutes 中的路由，后端控制不需要修改，请求接口路由数据时，会覆盖 dynamicRoutes 第一个顶级 children 的内容（全屏，不包含 layout 中的路由出口）
 * @returns 返回路由菜单数据
 */
export const staticRoutes: Array<RouteRecordRaw> = [
  // {
  //   path: "/home",
  //   name: "login",
  //   component: () => import("@/views/home/<USER>"),
  //   meta: {
  //     title: "登录",
  //   },
  // },

];
