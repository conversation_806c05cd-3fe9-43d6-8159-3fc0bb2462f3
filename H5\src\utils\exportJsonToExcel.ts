import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';

/**
 * 将日期转换为 Excel 的日期格式
 * @param v 要转换的日期，可以是 Date 对象或字符串
 * @param date1904 是否使用 1904 日期系统，默认为 false
 * @returns 自 1900 年 1 月 1 日（或 1904 年 1 月 2 日，如果 date1904 为 true）以来的天数
 */

function datenum(v: Date | string, date1904?: boolean): number {
  let date = new Date(v);
  if (date1904) {
    date.setTime(date.getTime() + 1462 * 24 * 60 * 60 * 1000);
  }

  const epoch = Date.parse(date.toISOString());
  return (epoch - Date.UTC(1899, 11, 30)) / (24 * 60 * 60 * 1000);
}

/**
 * 从数组的数组创建工作表
 * @param data 包含数据的数组的数组
 * @param opts 可选的写入选项
 * @returns 新的工作表对象
 */
function sheetFromArrayOfArrays(data: any[][], opts?: XLSX.WritingOptions): XLSX.WorkSheet {
  const ws: XLSX.WorkSheet = {};
  const range = {
    s: { c: 10000000, r: 10000000 },
    e: { c: 0, r: 0 }
  };

  data.forEach((row, R) => {
    row.forEach((val, C) => {
      if (range.s.r > R) range.s.r = R;
      if (range.s.c > C) range.s.c = C;
      if (range.e.r < R) range.e.r = R;
      if (range.e.c < C) range.e.c = C;

      // 初始化单元格，包含 'v'（值）和 't'（类型）
      let cell: XLSX.CellObject;
      if (val == null) return;

      if (typeof val === 'number') {
        cell = { v: val, t: 'n' }; // 数字类型
      } else if (typeof val === 'boolean') {
        cell = { v: val, t: 'b' }; // 布尔类型
      } else if (val instanceof Date) {
        cell = { v: datenum(val), t: 'n', z: XLSX.SSF._table[14] }; // 日期类型
      } else {
        cell = { v: val, t: 's' }; // 字符串类型
      }

      const cellRef = XLSX.utils.encode_cell({ c: C, r: R });
      ws[cellRef] = cell;
    });
  });

  if (range.s.c < 10000000) ws['!ref'] = XLSX.utils.encode_range(range);
  return ws;
}

// 定义工作簿类
class Workbook {
  SheetNames: string[] = [];
  Sheets: { [key: string]: XLSX.WorkSheet } = {};

  constructor() { }
}
/**
 * 将字符串转换为 ArrayBuffer
 * @param s 要转换的字符串
 * @returns 新的 ArrayBuffer
 */
function s2ab(s: string): ArrayBuffer {
  const buf = new ArrayBuffer(s.length);
  const view = new Uint8Array(buf);
  for (let i = 0; i < s.length; ++i) {
    view[i] = s.charCodeAt(i) & 0xFF;
  }
  return buf;
}

interface ExportJsonToExcelOptions {
  multiHeader?: string[][]; // 多级表头
  header: string[]; // 表头
  data: any[][]; // 数据
  filename?: string; // 文件名，默认为 'excel-list'
  merges?: string[]; // 合并单元格范围
  autoWidth?: boolean; // 是否自动调整列宽，默认为 true
  bookType?: XLSX.BookType; // 文件类型，默认为 'xlsx'
  extraColors?: (string | null)[][]; // 额外的颜色信息，每个单元格的颜色
}

export function exportJsonToExcel({
  multiHeader = [],
  header,
  data,
  extraColors = [],
  filename = 'excel-list',
  merges = [],
  autoWidth = true,
  bookType = 'xlsx'
}: ExportJsonToExcelOptions): void {
  data = [...data];
  data.unshift(header);

  multiHeader.forEach(mh => data.unshift(mh));

  const wsName = 'SheetJS';
  const wb = new Workbook();
  const ws = sheetFromArrayOfArrays(data);

  // 应用颜色到单元格
  if (extraColors.length > 0) {
    extraColors.forEach((rowColors, rowIndex) => {
      rowColors.forEach((color, colIndex) => {
        if (color) {
          const cellRef = XLSX.utils.encode_cell({ c: colIndex, r: rowIndex + multiHeader.length + 1 });
          if (!ws[cellRef]) ws[cellRef] = {}; // 如果单元格不存在，初始化它
          ws[cellRef].s = {
            fill: {
              patternType: "solid",
              fgColor: { rgb: color === 'green' ? '00FF00' : 'FFFF00' },
              bgColor: { rgb: 'FFFFFF' }
            }
          };
        }
      });
    });
  }

  if (merges.length > 0) {
    if (!ws['!merges']) ws['!merges'] = [];
    merges.forEach(item => {
      ws['!merges']!.push(XLSX.utils.decode_range(item));
    });
  }

  if (autoWidth) {
    const colWidth = data.map(row => row.map(val => {
      if (val == null) return { 'wch': 10 };
      if (val.toString().charCodeAt(0) > 255) {
        return { 'wch': val.toString().length * 2 };
      }
      return { 'wch': val.toString().length };
    }));

    const result = colWidth[0];
    for (let i = 1; i < colWidth.length; i++) {
      for (let j = 0; j < colWidth[i].length; j++) {
        if (result[j] && result[j]['wch'] < colWidth[i][j]['wch']) {
          result[j]['wch'] = colWidth[i][j]['wch'];
        }
      }
    }
    ws['!cols'] = result;
  }

  wb.SheetNames.push(wsName);
  wb.Sheets[wsName] = ws;

  const wbout = XLSX.write(wb, {
    bookType,
    bookSST: false,
    type: 'binary'
  });

  
  saveAs(
    new Blob([s2ab(wbout)], { type: 'application/octet-stream' }),
    `${filename}.${bookType}`
  );
}