<template>
    <div class="card-header-right" ref="containerRef">

        <div v-for="item, index in props.tabList" class="item" @click="changeOrderType(item)"
            :class="curSelectTab == item.value ? 'activeItem' : ''">
            <van-badge :offset="[10,8]" :content="item.messageCount" :show-zero="false" max="99" v-if="item.isShowBadge">
                {{ item.text }}
            </van-badge>
            <span v-else> {{ item.text }}</span>
        </div>

        <div class="underline" :style="{
            left: left + 'px',
            width: underlineWidth + 'px'
        }"></div>
    </div>
</template>

<script setup lang="ts">

type TabItem = {
    text: string,
    value: string | number
    isShowBadge?: boolean | undefined
    [key: string]: any
}

const emits = defineEmits(['changeSelectTab'])

const props = defineProps({
    tabList: {
        type: Array as PropType<TabItem[]>,
        default: (): TabItem[] => []
    },

})


const curSelectTab = defineModel('curSelectTab', {
    type: [String, Number],
    default: (): string | number => {
        return ''
    }
})

const left = ref(0)
const underlineWidth = ref(0)

const containerRef = ref<HTMLElement | null>(null)

function changeOrderType(item: TabItem) {
    if (curSelectTab.value == item.value) return
    curSelectTab.value = item.value
    emits('changeSelectTab', item)
}


watch(() => curSelectTab.value, (newValue) => {
  
    
    nextTick(() => {
        updateUnderlinePosition();

    })
})



// 更新下划线的位置和宽度
function updateUnderlinePosition() {
    nextTick(() => {
        if (!containerRef.value) return;
        const activeItem = containerRef.value.querySelector(".item.activeItem") as HTMLElement;
        if (activeItem) {
            // 获取纯文本内容
            const textContent = activeItem.textContent || activeItem.innerText;

            // 创建一个临时的 canvas 元素来测量文本宽度
            const canvas = document.createElement('canvas');
            const context = canvas.getContext('2d');
            if (context) {
                context.font = window.getComputedStyle(activeItem).font; // 获取元素字体样式
                underlineWidth.value = context.measureText(textContent).width / 1.2; // 获取文本宽度
            }


            // underlineWidth.value = activeItem.offsetWidth / 1.7; // 设置下划线宽度为元素宽度的 1.7
            left.value =
                activeItem.offsetLeft + activeItem.offsetWidth / 2 - underlineWidth.value / 2; // 下划线居中
        }
    })
}

const resizeObserver = ref<ResizeObserver | null>(null);
onMounted(() => {
    nextTick(() => {
        updateUnderlinePosition();

        // 使用 ResizeObserver 来监听变化
        if (containerRef.value) {
            resizeObserver.value = new ResizeObserver(() => {
                updateUnderlinePosition();
            });
            resizeObserver.value.observe(containerRef.value);
        }
    });
})
onBeforeUnmount(() => {
    if (resizeObserver.value) {
        const container = document.querySelector('.card-header-right');
        if (container) {
            resizeObserver.value.disconnect();
        }
    }
});

</script>

<style lang="scss" scoped>
.card-header-right {
    position: relative;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    touch-action: manipulation;
    gap: 10px;
    /* 提升移动端触摸体验 */

    &>.item {
        flex: 1 0 auto;
        min-width: 45px;
        height: 46px;
        line-height: 46px;
        text-align: center;
        font-weight: 400;
        font-size: 16px;
        color: #919191;
        cursor: pointer;
        position: relative;
        transition: color 0.3s;
        user-select: none;
        /* 防止在移动端选中文本 */
    }

    &>.item.activeItem {
        font-weight: 500;
        font-size: 16px;
        color: #1A76FF;
    }
}

.underline {
    position: absolute;
    bottom: 5px;
    height: 4px;
    background: #1a76ff;
    border-radius: 26px;
    transition: all 0.3s ease;
    width: 25px;
    left: 10px;
    // transform: translateX(50%);
    /* 初始位置 */
}
</style>