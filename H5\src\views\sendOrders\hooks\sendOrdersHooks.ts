import { useRouter } from "vue-router";

import myEcharts from "@/components/charts/myCharts.vue";
import { getSendOrderListAPI } from "@/api/home";
import { useUserInfo } from "@/stores/userInfo";
import { formatDate } from "@/utils/loadTime";
import { searchHooks } from "@/hooks_modules/changeTimeHooks.ts";

interface Option {
    text: string;  // 选项文本
    value: number;  // 选项值，假设为字符串
    color: string;  // 颜色的十六进制代码
}
// 定义每个选项的类型
interface OptionList {
    dispatchNum: Option;  // 派单总量
    implementedNum: Option;  // 已执行量
    outCallNum: Option;  // 外呼量
    pengdingNum: Option;  // 待处理量
    [key: string]: any
}


export const sendOrdersHooks = function () {
    const rouer = useRouter()
    const userInfoStore = useUserInfo()
    const {
        formline,
        defaultDate,
        maxDate,
        calendarIsShow,
        curSelect,
        selectOptionList,
        changeSelete,
        selectTime,
        onConfirm

    } = searchHooks({
        source: 'sendOrders',
        callback,
    })




    const showOrgName = computed(() => userInfoStore.userInfo.showOrgName)

    const optionList = reactive<OptionList>({

        dispatchNum: {
            text: '派单总量',
            value: 0,
            color: '#1A76FF',
        },
        implementedNum: {
            text: '已执行量',
            value: 0,
            color: '#6282FD',
        },
        pengdingNum: {
            text: '待处理量',
            value: 0,
            color: '#FFA34D',
        },

        outCallNum: {
            text: '外呼量',
            value: 0,
            color: '#b462fd',
        },
        successResultNum: {
            text: '成功数',
            value: 0,
            color: '#00AFA3',
        },

    })



    const options = computed(() => {

        const { dispatchNum, implementedNum, outCallNum, successResultNum } = optionList

        let dataValue = [dispatchNum, implementedNum, outCallNum, successResultNum];

        dataValue.sort((a, b) => a.value - b.value)
        const option = {
            color: dataValue.map(item => item.color),
            tooltip: { trigger: 'item' },
            series: [
                // 第一组：只显示百分比
                {
                    name: '派单详情',
                    type: 'funnel',
                    x: '0%',
                    y: 30,
                    width: '60%',
                    minSize: '40%',
                    maxSize: '100%',
                    sort: 'descending',
                    gap: 3,
                    data: dataValue,
                    label: {
                        color: '#fff',
                        position: 'center',
                        formatter: function (params: any) {
                            console.log(params);

                            if (params.data.text == '派单总量') {
                                return `派单总量`;
                            } else {
                                // 处理分母为0的情况，避免出现NaN
                                return optionList.dispatchNum.value == 0
                                    ? `{part|0.00%}`
                                    : `{part|${((params.data.value / optionList.dispatchNum.value) * 100).toFixed(2)}%}`;
                            }

                        },
                        rich: {
                            part: {
                                fontSize: '10px',
                                color: '#FFFFFF',
                                marginLeft: '10px'
                            }
                        }
                    },
                    labelLine: { show: false },
                    itemStyle: {
                        borderWidth: 0,
                        shadowBlur: 40,
                        shadowOffsetX: 4,
                        shadowOffsetY: 3,
                        shadowColor: 'rgba(143, 187, 255, 1)',
                    }
                },
                // 第二组：只显示右侧数量
                {
                    name: '数量',
                    type: 'funnel',
                    x: '0%',
                    y: 30,
                    width: '65%',
                    minSize: '40%',
                    maxSize: '100%',
                    sort: 'descending',
                    gap: 3,
                    data: dataValue.map(item => ({
                        ...item,
                        itemStyle: { color: 'rgba(0,0,0,0)' },
                        labelLine: {
                            lineStyle: {
                                color: item.color,
                                width: 2
                            }
                        }
                    })),
                    label: {
                        show: true,
                        position: 'right',
                        align: 'right',
                        color: '#333',
                        fontSize: 11,
                        distance: 10,
                        formatter: function (params: any) {
                            return `${params.data.text}：${params.data.value}`;
                        }
                    },

                    labelLine: {
                        show: true,
                        length: 40,
                        minTurnAngle: 90,
                        lineStyle: {
                            type: [2, 2],
                            width: 2,
                            cap: 'round'
                        }
                    },
                    itemStyle: {
                        borderWidth: 0,
                        color: 'rgba(0,0,0,0)' // 图形透明
                    },
                    tooltip: { show: false } // 不显示tooltip
                }
            ]
        }

        // const { implementedNum, outCallNum, pengdingNum } = optionList
        // var title = ['待处理量', '外呼量', '已执行量'];
        // var dataValue = [pengdingNum.value, outCallNum.value, implementedNum.value,];
        // var dataList = title.map((item, index) => {
        //     return {
        //         name: item,
        //         value: dataValue[index],
        //     };
        // });
        // var option = {
        //     color: ['#FFA34D', '#b462fd', '#6282FD',],
        //     series: [
        //         // 展示层
        //         {
        //             type: 'pie',
        //             center: ['50%', '50%'],
        //             radius: ['0%', '65%'],
        //             roseType: 'area',
        //             label: {
        //                 show: true,
        //                 backgroundColor: 'inherit',
        //                 height: 1,
        //                 width: 6,
        //                 lineHeight: 1,
        //                 distanceToLabelLine: 0,
        //                 borderRadius: 1,
        //                 borderWidth: 1,
        //                 borderColor: 'inherit',
        //                 padding: [1, 1, 1, 1],
        //                 formatter: function (params: any) {
        //                     if (params.name == "待处理量") {
        //                         return "{a| " + params.name + "}";
        //                     }
        //                     else if (params.name == "已执行量") {
        //                         return "{b| " + params.name + "}";
        //                     }
        //                     else {
        //                         return "{c|" + params.name + "}";
        //                     }
        //                 },
        //                 rich: {
        //                     a: {
        //                         padding: [0, -55, 30, -55],
        //                         fontSize: '12px',
        //                         fontFamily: 'MicrosoftYaHei',
        //                         color: '#FFA34D', // 初始颜色，可以稍后在 formatter 中动态调整
        //                     },
        //                     b: {
        //                         padding: [0, -55, 30, -65],
        //                         fontSize: '12px',
        //                         fontFamily: 'MicrosoftYaHei',
        //                         color: "#6282FD",

        //                     },
        //                     c: {
        //                         padding: [0, -55, 30, -50],
        //                         fontSize: '12px',
        //                         fontFamily: 'MicrosoftYaHei',
        //                         color: "#b462fd",

        //                     },

        //                 }
        //             },
        //             labelLine: {
        //                 show: false,
        //                 normal: {
        //                     length: 20,
        //                     length2: 70,
        //                     lineStyle: {
        //                         width: 2,

        //                     }
        //                 }
        //             },
        //             data: dataList,
        //         },
        //     ],
        // }


        return option
    })

    const orderType = ref('all')
    function changeOrderType(type: string) {
        orderType.value = type
        laodSendOrderList()
    }

    function callback() {
        laodSendOrderList()
    }



    // 获取列表数据 
    function laodSendOrderList() {
        let params = {
            startTime: formline.startTime ? `${formline.startTime} 00:00:00` : '',
            endTime: formline.endTime ? `${formline.endTime} 23:59:59` : '',
            isFrame: orderType.value == 'frame' ? 1 : 0   //1 框架  0 全部
        }
        getSendOrderListAPI(params).then(res => {
            optionList.dispatchNum.value = res.data.dispatchNum ?? 0
            optionList.implementedNum.value = res.data.implementedNum ?? 0
            optionList.outCallNum.value = res.data.outCallNum ?? 0
            optionList.pengdingNum.value = res.data.pengdingNum ?? 0
            optionList.successResultNum.value = res.data.successResultNum ?? 0
        })
    }



    // 生命周期钩子
    onMounted(() => {

        console.log(selectOptionList.value);

        selectOptionList.value.splice(0, 1, { text: '当月', value: '4' },)
        console.log(selectOptionList.value);

        curSelect.value = '4'
        let startTime = new Date(new Date().getFullYear(), new Date().getMonth(), 1)
        formline.startTime = formatDate(startTime, 'yyyy-MM-dd')
        formline.endTime = formatDate(new Date(), 'yyyy-MM-dd')
        defaultDate.value = [startTime, new Date()]


        laodSendOrderList()
    });

    onUnmounted(() => {

    });


    return {
        showOrgName,
        callback,
        options,
        optionList,
        formline,
        defaultDate,
        maxDate,
        calendarIsShow,
        curSelect,
        selectOptionList,
        orderType,
        changeOrderType,
        changeSelete,
        selectTime,
        onConfirm

    }

}


