
<template>
  <template v-if="icon">
    <template v-if="icon.includes('el-')">
      <el-icon :size="size">
        <component :is="icon.replace('el-','')" />
      </el-icon>
    </template>
    <svg-icon v-else :icon="icon" class="sub-el-icon" :size="size"></svg-icon>
  </template>

  <span v-if="isCollapse" slot='title'>{{ title }}</span>
</template>
<script setup>
import { computed } from 'vue'
import { useSidebarStore } from "@/stores/layoutSetting/app.ts";
const SidebarState = useSidebarStore();
const props = defineProps({
  icon: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  size: {
    type: [String, Number],
    default: 18
  }
})
const isCollapse = computed(() => SidebarState.sidebar.opened)
</script>



<style scoped>
.sub-el-icon {
  color: currentColor;
  width: 1em;
  height: 1em;
}
</style>
