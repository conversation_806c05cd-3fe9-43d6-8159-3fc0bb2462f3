.box {
    height: 100%;
    background: #fbfcff;
    overflow-y: scroll;
    overflow-x: hidden;
    border-radius: 10px 10px 0px 0px;
}

:deep(.van-search__field) {
    height: 50px;
}

:deep(.van-field__control::placeholder) {
    font-size: 20px;
    color: #A8A8A8;
}


.search {
    border-bottom: 1px solid #EEF5FF;
}

.box_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px;
    border-bottom: 1px solid #EEF5FF;
    height: 65px;

    .Branch_name {
        font-weight: 500;
        font-size: 24px;
        color: #3D3D3D;
        line-height: 27px;
    }

    .history {
        flex: 0 0 auto;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        display: flex;
        align-items: center;
    }
}

.selectOptionList {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(60px, 1fr));
    place-items: center;
    padding: 0px 12px;
    gap: 10px;
    border-bottom: 1px solid #EEF5FF;

    .item {
        min-width: 60px;
        height: 45px;
        line-height: 45px;
        text-align: center;
        font-weight: 400;
        font-size: 16px;
        color: #919191;
    }

    .activeItem {
        font-weight: 600;
        color: #1A76FF;
    }
}

.selectTime {
    display: grid;
    grid-template-columns: 1fr 40px 1fr;
    gap: 10px;
    place-items: center;
    border-bottom: 1px solid #EEF5FF;

    .startTime {
        font-weight: 400;
        font-size: 14px;
        color: #919191;
        text-align: center;
        margin-top: 20px;
        margin-bottom: 15px;
    }

    .timeValue {
        font-weight: 500;
        font-size: 20px;
        color: #A8A8A8;
        margin-bottom: 20px;
    }

    .activeTime {
        color: #1A76FF;
    }
}

.searchButtomn {
    border-radius: 10px;
    width: 90%;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: 150px;
    font-weight: 500;
    font-size: 18px;
    color: #FFFFFF;

}