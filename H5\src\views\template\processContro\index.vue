<template>
    <div class='container_ mt-16'>
        <div class="container__header">
            <div>
                <img src="./assets/processContro.svg" alt="" srcset="">
                <span class="ml-6">过程管控</span>
            </div>
            <router-link to="/loginRate">
                <div class="logiRate fs14">
                    <span class="fs13">今日登录率</span>：
                    <span>{{ todayTotalCount }}%</span>
                </div>
            </router-link>

        </div>


        <div class="container_body">
            <div class="tabs" id="tabs">
                <div v-for="(item, index) in Config" class="tab-item"
                    :class="{ activeItem: curtabItem?.menuCode == item.menuCode }" @click="changeTab(item, index)">
                    {{ item.menuName }}
                </div>
            </div>
            <div class="tabs-header">
                <div>{{ tabHeader }}</div>
                <div @click="viewDetails" :class="isShowReview.isShow ? '' : 'notRevier'">查看详情 <van-icon name="arrow"
                        color="#B8D4FF" /></div>
            </div>
            <div class="tab_content" :class="getTableClass">
                <template v-if="curtabItem?.menuName == '晨会出征'">
                    <div class="signIn-item" v-for="item in signInTableData" :key="item.signRate">
                        <div class="value" :class="[item.rateClass]">{{ item.signRate }}%</div>
                        <div class="name">{{ item.name }}</div>
                    </div>
                </template>
                <template v-else-if="curtabItem?.menuName == '当前产能'">
                    <div class="curCapacity-item jf">
                        <div class="value">{{ curCapacityTableData.points ?? 0 }}</div>
                        <div class="name">积分</div>
                    </div>
                    <div class="curCapacity-item ty">
                        <div class="value">{{ curCapacityTableData.tianYi ?? 0 }}</div>
                        <div class="name">天翼</div>
                    </div>
                    <div class="curCapacity-item kd">
                        <div class="value">{{ curCapacityTableData.kuanDai ?? 0 }}</div>
                        <div class="name">宽带</div>
                    </div>
                    <div class="curCapacity-item rh">
                        <div class="value">{{ curCapacityTableData.ronghe ?? 0 }}</div>
                        <div class="name">融合</div>
                    </div>
                    <div class="curCapacity-item zgz">
                        <div class="value">{{ curCapacityTableData.xinliangZgz ?? 0 }}</div>
                        <div class="name">新装高套</div>
                    </div>
                </template>
                <template v-else-if="curtabItem?.menuName == '午间复盘' || curtabItem?.menuName == '晚间复盘'">

                    <template v-if="isShowReview.isShow">
                        <div class="review-item">
                            <div class="value" style="color: #FF2424;">{{ reviewTableData.pointsNs }}</div>
                            <div class="name">积分未达{{ curtabItem?.menuName == '午间复盘' ? '20%' : '90%' }} </div>
                        </div>
                        <div class="review-item">
                            <div class="value" style="color: #FF6A1A;">{{ reviewTableData.pointsZ }}</div>
                            <div class="name">积分为0</div>
                        </div>
                        <div class="review-item">
                            <div class="value" style="color:#3D3D3D;">{{ reviewTableData.huoliZ }}</div>
                            <div class="name">活力值为0</div>
                        </div>
                    </template>
                    <div class="noReview" v-else>
                        {{ isShowReview.text }}

                    </div>


                </template>
                <template v-if="curtabItem?.menuName == '战区攻防'">
                    <div class="signIn-item" :class="item.areaName == '合计' ? 'signIn-item_' : ''"
                        v-for="item in promoShotsList" :key="item.areaId">
                        <div class="value">{{ item.gridRate }}%</div>
                        <div class="name">{{ item.areaName }}</div>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { useUserInfo } from "@/stores/userInfo";
import { useRouter } from "vue-router";
import { getSignCountAreaAPI } from "@/api/morning";
import { getAllAreaRateAPI } from "@/api/promoShots";
import { getcurCapacityAPI, getcurCapacityCountAPI, getReviewCountAPI, getTodayTotalCountAPI } from "@/api/home";
const userInfoStore = useUserInfo();
const router = useRouter()
import { mapSort } from "@/utils/mapSort";
const Config = computed(() => {
    let home = userInfoStore.getMenuList.filter((item: MenuItem) => item.menuCode === 'home')[0]?.children || []
    let list = home.filter((item: MenuItem) => item.menuCode === 'processContro')[0]?.children || []
    return list
})

type SignInTableData = {
    name: string,
    rateClass: string
    signRate: number
}
type PromoShotsList = {
    areaId: string,
    areaName: string
    gridNum: number
    gridRate: number
}

interface Progress {
    name: string;
    points?: number
    tianYi: number
    kuanDai: number;
    ronghe: number;
    xinliangZgz: number;
    [key: string]: any
}

//当前激活的tab
const curtabItem = ref<MenuItem>()




const isShowReview = reactive({
    isShow: true,
    text: ''
})
function changeTab(item: MenuItem, index: number) {
    if (curtabItem.value?.menuName == item.menuName) return
    curtabItem.value = item
    isShowReview.isShow = true
    if (item.menuName == '午间复盘') {
        isShowReview.isShow = isCurrentTimeInRange('13:00', '24:00')
        isShowReview.text = isShowReview.isShow ? '' : '午间复盘时间为13:00到18:30之间,现在时间段暂未开始'
        if (isShowReview.isShow) loadReviewCount(1)


    } else if (item.menuName == '晚间复盘') {
        isShowReview.isShow = isCurrentTimeInRange('18:30', '24:00')
        isShowReview.text = isShowReview.isShow ? '' : '晚间复盘时间为18:30到24:00之间,现在时间段暂未开始'
        if (isShowReview.isShow) loadReviewCount(2)
    }
    const tabsDOM = document.getElementById('tabs') as HTMLDivElement
    const left = tabsDOM.clientWidth / Config.value.length
    console.log(tabsDOM.clientWidth / Config.value.length);

    tabsDOM.scrollTo({ left: left * index, behavior: "smooth" })

}




const signInTableData = ref<SignInTableData[]>([])
const curCapacityTableData = ref<Progress>({
    name: '',
    points: 0,
    tianYi: 0,
    kuanDai: 0,
    ronghe: 0,
    xinliangZgz: 0,
})

const reviewTableData = ref<any>({})





//查看详情
function viewDetails() {
    if (curtabItem.value?.menuName.includes('复盘')) {
        if (isShowReview.isShow) router.push({ path: curtabItem.value?.path as string })
    } else {
        router.push({ path: curtabItem.value?.path as string })
    }

}

const tabHeader = computed(() => {
    switch (curtabItem.value?.menuName) {
        case '晨会出征':
            // return '晨会出征（统计时间截止到10：00点）'
            return '晨会出征'

        case '当前产能':
            return '当前产能(实时更新)'

        case '午间复盘':
            return '午间复盘(数据更新截至到18:30)'

        case '晚间复盘':

            return '晚间复盘(数据18:30开始实时更新)'
        case '战区攻防':

            return '小区宣传检查推进率'

    }
})
const getTableClass = computed(() => {
    switch (curtabItem.value?.menuName) {
        case '晨会出征':
            return 'signInTable'
        case '当前产能':
            return 'curCapacity'

        case '午间复盘':
            return isShowReview.isShow ? 'review' : 'noReview'

        case '晚间复盘':
            return isShowReview.isShow ? 'review' : 'noReview'

        case '战区攻防':
            return 'signInTable'


    }
})


//获取晨会出征面板数据
function loadSignCountArea() {
    getSignCountAreaAPI().then((res: any) => {
        res.data = mapSort(res.data)
        for (const item of res.data) {
            if (item.signRate <= 65 && item.signRate > 0) {
                item.rateClass = 'warning'
            } else if (item.signRate <= 0) {
                item.rateClass = 'danger'
            }
        }
        signInTableData.value = res.data || []
    })
}

//获取当前产能数据
function loadcurCapacity() {
    getcurCapacityCountAPI().then((res: any) => {
        if (res.code == 200) {
            curCapacityTableData.value = res.data || []
        }
    })
}
//获取当前复盘数据
function loadReviewCount(type: number) {
    getReviewCountAPI({ reviewType: type }).then((res: any) => {
        if (res.code == 200) {
            reviewTableData.value = res.data || []
        }
    })
}



const todayTotalCount = ref(0)
//获取今日登录数据
async function loadTodayTotalCount() {
    const res: any = await getTodayTotalCountAPI()
    if (res.code == 200) todayTotalCount.value = res.data.loginRate ?? 0

}




//获取当前时间 是否在传入的时间内
function isCurrentTimeInRange(startTime: string, endTime: string): boolean {
    // 获取当前时间
    const now = new Date();
    const currentTime = now.getHours().toString().padStart(2, '0') + ":" + now.getMinutes().toString().padStart(2, '0');

    // 比较当前时间是否在时间范围内
    return startTime <= currentTime && currentTime <= endTime;
}




const promoShotsList = ref<PromoShotsList[]>([])

//获取战区攻防数据面板
function loadAllAreaRate() {
    getAllAreaRateAPI().then((res: any) => {
        res.data.list = mapSort(res.data.list, 'areaName')
        res.data.list.push({
            areaId: 250000000, areaName: '合计', gridRate: res.data.allAreaRate
        })
        promoShotsList.value = res.data.list || []
    })
}


onMounted(() => {
    curtabItem.value = Config.value[0]
    loadSignCountArea()
    loadcurCapacity()
    loadTodayTotalCount()
    loadAllAreaRate()

})
</script>

<style lang="scss" scoped>
.container_ {
    font-family: 'Source Han Sans, Source Han Sans';
}

.tabs {
    width: 100%;
    display: flex;
    align-items: center;
    gap: 10px;
    overflow-x: auto;
    overflow-y: hidden;
    //    padding-top: 5px;
    padding-bottom: 10px;

    .tab-item {
        padding: 5px;
        border-radius: 5px;
        border: 1px solid #B8D4FF;
        font-weight: 500;
        font-size: 12px;
        color: #71A9FF;
        text-align: center;
        white-space: nowrap;
    }

    .activeItem {
        border: 1px solid #1A76FF;
        color: #1A76FF;
        font-weight: 700;
    }
}

#tabs::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
}

.tabs-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 500;
    font-size: 12px;
    color: #3D3D3D;
    text-align: center;
    margin: 20px 0px;
    margin-top: 15px;

    .notRevier {
        color: #afafaf;
    }
}

.tab_content {
    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
    display: flex;
    align-items: center;
    padding: 8px 10px;
    min-height: 60px;

}

.noReview {
    font-size: 12px;
    color: #ee9d01;
}

.signInTable {
    display: grid;
    grid-template-columns: repeat(5, 55px);
    align-items: center;
    justify-items: center;
    grid-auto-flow: dense;
    /* 让元素尽量填充前面的空位 */
    justify-content: space-between;
    // display: flex;
    // flex-wrap: wrap;
    // align-items: center;
    // align-content: center;
    // justify-content: space-between;
    gap: 10px 0px;

    .signIn-item {
        display: flex;
        flex-direction: column;

        // align-items: center;
        .name {
            font-weight: 500;
            font-size: 12px;
            color: #3D3D3D;
            margin-top: 3px;
            text-align: center;
        }



        .value {
            font-weight: 700;
            font-size: 14px;
            color: #3D3D3D;
            text-align: center;
        }

        // .warning {
        //     font-size: 18px;
        //     color: #FF6A1A;
        // }

        // .danger {
        //     color: red;
        // }
    }

    .signIn-item_ {
        grid-column: span 3;
        // justify-self: end; // 让它靠右对齐
        text-align: center !important;

        .name,
        .value {
            font-weight: 700 !important;
            color: #000000;

        }
    }

}

.curCapacity {
    display: grid;
    grid-template-columns: repeat(5, 1fr);

    .jf {
        color: #34C759;
    }

    .ty {
        color: #FF6A1A;
    }

    .kd {
        color: #477BF3;
    }

    .rh {
        color: #FFC34A;
    }

    .zgz {
        color: #E950E1;
    }

    .curCapacity-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .name {
            font-weight: 500;
            font-size: 12px;

            margin-top: 3px;
        }

        .value {
            font-weight: 700;
            font-size: 14px;

        }
    }
}

.review {
    display: grid;
    grid-template-columns: repeat(3, 1fr);

    .review-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .name {
            margin-top: 2px;
            font-weight: 400;
            font-size: 12px;
        }

        .value {
            font-weight: 700;
            font-size: 18px;
        }
    }
}

.logiRate {
    color: #1A76FF;

}
</style>