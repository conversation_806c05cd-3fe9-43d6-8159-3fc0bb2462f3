<template>
  <el-scrollbar ref="scrollContainer" :vertical="false" class="scroll-container" @wheel.prevent="handleScroll">
    <slot />
  </el-scrollbar>
</template>

<script setup lang="ts">
import { ElScrollbar } from 'element-plus'
import { ref, onMounted, onBeforeUnmount, computed, getCurrentInstance } from 'vue'
import type { ComponentPublicInstance } from 'vue'

const tagAndTagSpacing = 4 // tagAndTagSpacing

const left = ref(0)
const scrollContainer = ref<InstanceType<typeof ElScrollbar> | null>(null)

// 用于发送自定义事件
const emit = defineEmits<{
  (e: 'scroll'): void
}>()

const instance = getCurrentInstance()
if (!instance) {
  throw new Error('Component instance not found')
}

// 计算属性：获取 scrollWrapper
const scrollWrapper = computed(() => {
  return scrollContainer.value?.wrapRef
})

// 监听 mounted 生命周期
onMounted(() => {
  const wrapper = scrollWrapper.value
  if (wrapper) {
    wrapper.addEventListener('scroll', emitScroll)
  }
})

onBeforeUnmount(() => {
  const wrapper = scrollWrapper.value
  if (wrapper) {
    wrapper.removeEventListener('scroll', emitScroll)
  }
})

// 方法：处理滚动事件
const handleScroll = (e: WheelEvent) => {
  const eventDelta = -e.deltaY * 40
  const $scrollWrapper = scrollWrapper.value
  if ($scrollWrapper) {
    const newScrollLeft = $scrollWrapper.scrollLeft + eventDelta / 4
    if (newScrollLeft !== $scrollWrapper.scrollLeft) {
      $scrollWrapper.scrollLeft = newScrollLeft
    }
  }
}

const emitScroll = () => {
  emit('scroll')
}

// 方法：移动到目标标签
const moveToTarget = (currentTag: HTMLElement) => {
  const $container = scrollContainer.value?.$el as HTMLElement
  if (!$container) return

  const $containerWidth = $container.offsetWidth
  const $scrollWrapper = scrollWrapper.value
  if (!$scrollWrapper) return

  const parent = instance.parent
  if (!parent) return

  const tagList = (parent.refs.tag as HTMLElement[]) || []
  if (tagList.length === 0) return

  const firstTag = tagList[0]
  const lastTag = tagList[tagList.length - 1]

  if (firstTag === currentTag) {
    $scrollWrapper.scrollLeft = 0
  } else if (lastTag === currentTag) {
    $scrollWrapper.scrollLeft = $scrollWrapper.scrollWidth - $containerWidth
  } else {
    const currentIndex = tagList.findIndex(item => item === currentTag)
    if (currentIndex === -1) return

    const prevTag = tagList[currentIndex - 1]
    const nextTag = tagList[currentIndex + 1]

    const afterNextTagOffsetLeft = nextTag.offsetLeft + nextTag.offsetWidth + tagAndTagSpacing
    const beforePrevTagOffsetLeft = prevTag.offsetLeft - tagAndTagSpacing

    if (afterNextTagOffsetLeft > $scrollWrapper.scrollLeft + $containerWidth) {
      $scrollWrapper.scrollLeft = afterNextTagOffsetLeft - $containerWidth
    } else if (beforePrevTagOffsetLeft < $scrollWrapper.scrollLeft) {
      $scrollWrapper.scrollLeft = beforePrevTagOffsetLeft
    }
  }
}

defineExpose({
  moveToTarget
})
</script>

<style lang="scss" scoped>
.scroll-container {
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  width: 100%;

  :deep(.el-scrollbar__bar) {
    bottom: 0px;
  }

  :deep(.el-scrollbar__wrap) {
    height: 49px;
  }
}
</style>
