import { ComponentOptions } from 'vue';
import header from "./header.vue";
import overviewCard from "./overviewCard.vue";
import list from "./list.vue";



import headerDept from "./dept/headerDept.vue";
import overviewCardDept from "./dept/overviewCardDept.vue";
import listDept from "./dept/listDept.vue";


import headerArea from "./area/headerArea.vue";
import overviewCardArea from "./area/overviewCardArea.vue";
import listArea from "./area/listArea.vue";





const componentList: ComponentOptions<any, any, any> = {

    // 触点端
    header: {
        //我的贡献
        moudule_: header,
        ref_name: 'header',

    },
    overviewCard: {
        //我的贡献
        moudule_: overviewCard,
    },
    list: {
        //我的贡献
        moudule_: list,
    },

    // 管理端-部门
    headerDept: {
        //我的贡献
        moudule_: headerDept,
        ref_name: 'headerDept',

    },
    overviewCardDept: {
        //我的贡献
        moudule_: overviewCardDept,
        ref_name: 'overviewCardDept',

    },
    listDept: {
        //我的贡献
        moudule_: listDept,
        ref_name: 'listDept',

    },



    
    // 管理端-市局
    headerArea: {
        //我的贡献
        moudule_: headerArea,
        ref_name: 'headerArea',

    },
    overviewCardArea: {
        //我的贡献
        moudule_: overviewCardArea,
        ref_name: 'overviewCardArea',

    },
    listArea: {
        //我的贡献
        moudule_: listArea,
        ref_name: 'listArea',

    },

}
export default componentList

