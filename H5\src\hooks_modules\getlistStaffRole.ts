import { getlistStaffRoleOptionsAPI } from "@/api/common";
import { mapSort } from "@/utils/mapSort";
type IAreaList = {
    text: string,
    value: number | string | undefined
}


export const getlistStaffRole = function () {
    const roleList = ref<IAreaList[]>([])
    function listStaffRoleOptions() {
        getlistStaffRoleOptionsAPI().then((res: any) => {
            res.data = mapSort(res.data,'roleName')
            roleList.value = res.data.map((item: any) => {
                return {
                    text: item.roleName,
                    value: item.roleCode
                }
            })
            roleList.value.unshift({ text: '全部', value: undefined })
        })
    }
    onMounted(() => {
        listStaffRoleOptions()
    })
    return {
        roleList,
        listStaffRoleOptions
    }

}


