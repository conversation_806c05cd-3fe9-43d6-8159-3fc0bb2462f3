<template>
  <div ref="scrollContainer">
    <div :style="{ height: search_height + 'px' }" v-show="isShow && search_Fixed" ref="fillHeader"></div>

    <div style="width: 100%">
      <header>
        <slot name="header"></slot>
      </header>
      <main class="searchHeader" ref="searchHeader" :class="isShow && search_Fixed ? 'header-top' : ''">
        <slot></slot>
      </main>
      <footer>
        <slot name="footer"></slot>
      </footer>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";


const scrollContainer = ref();
const isShow = ref(false);

const search_Fixed = true;

const { y: scroll_Top } = useElementBounding(scrollContainer);

const fillHeader = ref<HTMLElement | null>(null);
const searchHeader = ref<HTMLElement | null>(null);

const { height: search_height } = useElementSize(searchHeader);

watchThrottled(
  scroll_Top,
  () => {
    if (searchHeader.value) {
      const top = Math.ceil(searchHeader.value.getBoundingClientRect().top);
      isShow.value = top <= 84;

      if (isShow.value && fillHeader.value) {
        const fillHeaderTop = Math.ceil(fillHeader.value.getBoundingClientRect().top);
        isShow.value = fillHeaderTop <= 84;
      }
    }
  },
  { throttle: 100 }
);

onMounted(() => {
  if (searchHeader.value) {
    const top = Math.ceil(searchHeader.value.getBoundingClientRect().top);
    isShow.value = top <= 84;
  }
});
</script>

<style lang="scss" scoped>
main {
  width: 100%;
}

.searchHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 80px;
  background-color: #f2f6ff42;
}

.header-top {
  position: fixed;
  top: 82px;
  right: 0;
  z-index: 97;
  width: calc(100% - #{$sideBarWidth});
  animation: header-scroll-animation 0.5s ease 1;
  transition: width 0.28s;
  background-color: #f7f7f7 !important;
  color: black;
  padding: 0px 20px;
}

.hideSidebar .header-top {
  width: calc(100% - 54px);
}

.mobile .header-top {
  width: 100%;
}

@keyframes header-scroll-animation {
  0% {
    top: -80px;
  }

  100% {
    top: 80px;
  }
}
</style>
