<template>
    <div class="container_">

        <div class="container_body">

            <div class="message-body">
                <div class="message-header">
                    <img src="@/assets/defaultAvatar.png" alt="头像" class="avatar">
                    <div class="message-info">
                        <div class="message-title">{{ messageObj.title }}</div>
                        <div class="message-time">{{ messageObj.createTime }}</div>
                    </div>
                </div>
                <div class="message-content">
                    <span class="mr-3">内容:</span>
                    <span>{{ messageObj.content }}</span>
                </div>
                <div class="message-content">
                    <span class="mr-3">附件:</span>
                    <div class="attachment-images mt-3">
                        <van-image width="89" height="89" :radius="5" v-imgPreview="[messageObj.annexUrl, indexE]"
                            lazy-load fit="cover" v-for="attachment, indexE in messageObj.annexUrl" :key="attachment"
                            :src="attachment" alt="附件"></van-image>
                    </div>
                </div>
                <div style="text-align: right;" @click="showReadedUsers = true">
                    <van-icon size="12" color="#919191" name="browsing-history-o" />
                    <span style="color: #919191;" class="fs12 ml-3">{{
                        messageObj.readUsers.length ?? 0 }}人已读</span>
                </div>
            </div>



        </div>


        <van-popup v-model:show="showReadedUsers" position="bottom" round class="selected-users-popup"
            :style="{ width: '100%', height: '75%' }">

            <div class="container__header" style="width: 100%;">
                <cusTabs style="width: 100%;" :tabList="tabList" v-model:curSelectTab="curSelectTab">
                </cusTabs>

            </div>


            <div class="selected-users-content">
                <div class="selected-users-list">
                    <div v-for="item in messageObj[curSelectTab == '已读' ? 'readUsers' : 'unReadUsers']"
                        :key="item.staffCode" class="selected-user-item">
                        <div class="user-info">
                            <img src="@/assets/defaultAvatar.png" />
                            <div class="user-detail">
                                <span class="user-name" v-getName="{ item }">{{ item.staffName }}</span>
                                <span class="user-code">{{ item.staffCode }}</span>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </van-popup>

    </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

import { getMessageListBySenderAPI, getMessageDetailAPI } from "@/api/message";
import { useRoute } from "vue-router";
const route = useRoute()
interface Message {
    id: string;
    /** 消息标题 */
    title: string;
    /** 消息内容 */
    content: string;
    /** 附件URL，多个附件用逗号分隔 */
    annexUrl?: string[];
    /** 消息类型 */
    msgType: number;
    /** 创建人 */
    createBy?: string | null;
    /** 创建时间 */
    createTime: string;

    /** 阅读状态：0-未读，1-已读 */
    readFlag: number;
    readUsers: [];
    [key: string]: any
}


const tabList = ref([
    { text: '已读', value: '已读', },
    { text: '未读', value: '未读' },
])
const curSelectTab = ref('已读')


const messageObj = reactive<Message>({
    id: '',
    title: '',
    content: '',
    annexUrl: [],
    msgType: 0,
    createBy: null,
    createTime: '',
    readFlag: 0,
    readUsers: [],
    unReadUsers: [],
})
const showReadedUsers = ref(false)


function viewDetail() {
    getMessageDetailAPI({ id: route.query.id }).then((res: any) => {
        if (res.code == 200) {
            res.data.annexUrl = res.data.annexUrl ? res.data.annexUrl.split(',') : []
            for (const key in messageObj) {
                if (Object.prototype.hasOwnProperty.call(messageObj, key)) {
                    messageObj[key] = res.data[key];
                }
            }
            tabList.value[0].text = `已读 ${messageObj.readUsers.length}`
            tabList.value[1].text = `未读 ${messageObj.unReadUsers.length}`



        }
    })
}
onMounted(() => {
    viewDetail()
})

</script>

<style lang="scss" scoped>
.container_ {
    min-height: 100%;
}

.container__header {
    justify-content: flex-start;
}

.message-body {
    margin-bottom: 10px;
    border-bottom: 1px solid #EEF5FF;
    padding: 10px 0px;

    .message-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
    }

    .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
    }

    .message-info {
        display: flex;
        flex-direction: column;

        .message-title {
            font-weight: 500;
            font-size: 16px;
            color: #3D3D3D;
        }

        .message-time {
            font-weight: 400;
            font-size: 12px;
            color: #999999;
            margin-top: 5px;
        }
    }

    .message-content {
        margin-bottom: 10px;
        font-weight: 400;
        font-size: 12px;
        color: #999999;

        .attachment-images {
            display: grid;
            grid-template-columns: repeat(3, 1fr);

            // gap: 10px;
        }

    }
}


.selected-users-content {
    height: calc(100% - 68px);
    overflow-y: auto;
    padding: 0 16px;
    background: #FFFFFF;
}

.selected-users-list {
    padding: 8px 0;
}

.selected-user-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #EEF5FF;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;

    img {
        width: 40px;
        height: 40px;
    }
}

.user-detail {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.user-name {
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
}

.user-code {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 17px;
}

.remove-icon {
    color: #CCCCCC;
    font-size: 16px;
}
</style>