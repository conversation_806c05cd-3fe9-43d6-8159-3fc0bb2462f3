<template>
    <div class='Headlines container_'>
        <div class="container__header">
            <div>
                <img src="../assets/headline.svg" alt="" srcset="">
                <span class="ml-6">今日头条</span>
            </div>
            <router-link :to="{
                path: '/headlinesSetTarget',
                query: {
                    departmentType: current == 0 ? 'gov' : '',
                }
            }" v-if="staffType == '分局'">
                <div class="header-right">
                    <img src="@/views/template/Headlines/assets/headlineSetting.svg" alt="" srcset="">
                    <span>指标编辑</span>
                </div>
            </router-link>
        </div>


        <van-swipe indicator-color="#66C6F2" @change="onChange">
            <van-swipe-item>
                <transition name="fade-transform">
                    <div v-show="0 === current">

                        <!-- <div class="container_body growth-header">
                            <div class="w_11">
                                <h3>传统业务</h3>
                            </div>
                            <div class="w_2"></div>
                            <div class="w_11">
                                <h3>新兴业务</h3>
                            </div>
                        </div> -->

                        <div class="container_body">
                            <div class="growth-row">

                                <div class="growth-item w_11">
                                    <span class="growth_title">政企积分：</span>
                                    <span class="score van-ellipsis">
                                        <span class="member">{{ dataList.govQudaoJifen }}</span>
                                        <span>/{{ dataList.govQudaoJifenGoal }}</span>
                                    </span>
                                </div>

                                <div class="w_2">
                                    <div class="halvingLine"></div>
                                </div>

                                <div class="growth-item w_11">
                                    <span class="growth_title">政企天翼：</span>
                                    <span class="score van-ellipsis">
                                        <span class="member">{{ dataList.govTianyi }}</span>
                                        <span>/{{ dataList.govTianyiGoal }}</span>
                                    </span>
                                </div>

                            </div>

                            <div class=" growth-row">

                                <div class="growth-item w_11">
                                    <span class="growth_title">5G主卡：</span>
                                    <span class="score van-ellipsis">
                                        <span class="member">{{ dataList.gov5g ?? 0 }}</span>
                                        <span>/{{ dataList.gov5gGoal ?? 0 }}</span>
                                    </span>
                                </div>

                                <div class="w_2">
                                    <div class="halvingLine"></div>
                                </div>

                                <div class="growth-item w_11">
                                    <span class="growth_title">FTTR-B：</span>
                                    <span class="score van-ellipsis">
                                        <span class="member">{{ dataList.govFttrb ?? 0 }}</span>
                                        <span>/{{ dataList.govFttrbGoal ?? 0 }}</span>
                                    </span>
                                </div>

                            </div>

                            <div class=" growth-row">

                                <div class="growth-item w_11 ">
                                    <span class="growth_title">互专：</span>
                                    <span class="score van-ellipsis">
                                        <span class="member">{{ dataList.govHuZhuan }}</span>
                                        <span>/{{ dataList.govHuZhuanGoal }}</span>
                                    </span>
                                </div>

                                <div class="w_2">
                                    <div class="halvingLine"></div>
                                </div>

                                <div class="growth-item van-ellipsis w_11">
                                    <span class="growth_title">商专：</span>
                                    <span class="score van-ellipsis">
                                        <span class="member">{{ dataList.govShangZhuan }}</span>
                                        <span>/{{ dataList.govShangZhuanGoal }}</span>
                                    </span>
                                </div>

                            </div>

                            <div class=" growth-row">

                                <div class="growth-item w_11 " style="margin-bottom: 0px;">
                                    <span class="growth_title">天翼视联：</span>
                                    <span class="score van-ellipsis">
                                        <span class="member">{{ dataList.govTianyiShilian }}</span>
                                        <span>/{{ dataList.govTianyiShilianGoal }}</span>
                                    </span>
                                </div>

                                <div class="w_2">
                                    <div class="halvingLine"></div>
                                </div>

                                <div class="growth-item van-ellipsis w_11" style="margin-bottom: 0px;">
                                    <span class="growth_title">离网积分：</span>
                                    <span class="score van-ellipsis">
                                        <span class="member">{{ dataList.govLiwang ?? 0 }}</span>/{{
                                            dataList.govLiwangGoal ?? 0 }}</span>
                                </div>

                            </div>
                        </div>

                    </div>
                </transition>
            </van-swipe-item>

            <van-swipe-item>
                <transition name="fade-transform">
                    <div v-show="1 === current">
                        <div class="container_body growth-header">
                            <div class="w_11">
                                <h3>{{ staffType }}日发展</h3>
                            </div>
                            <div class="w_2"></div>
                            <div class="w_11">
                                <h3>{{ staffType }}月发展</h3>
                            </div>
                        </div>

                        <div class="container_body">
                            <div class="growth-row">

                                <div class="growth-item w_11">
                                    <span class="growth_title">积分：</span>
                                    <span class="score van-ellipsis"><span class="member">{{ dataList.completeDayPoints
                                            }}</span>/{{
                                                dataList.dailyPointsDenominator }}</span>
                                </div>

                                <div class="w_2">
                                    <div class="halvingLine"></div>
                                </div>

                                <div class="growth-item w_11">
                                    <span class="growth_title">积分：</span>
                                    <span class="score van-ellipsis"><span class="member">{{ dataList.completeDayPoints
                                        +
                                        dataList.completeMonthPoints }}</span>/{{ dataList.points }}</span>
                                </div>

                            </div>

                            <div class=" growth-row">

                                <div class="growth-item w_11">
                                    <span class="growth_title">融合：</span>
                                    <span class="score van-ellipsis"><span class="member">{{ dataList.completeDayRongHe
                                            }}</span>/{{
                                                dataList.dailyRongHeDenominator }}</span>
                                </div>

                                <div class="w_2">
                                    <div class="halvingLine"></div>
                                </div>

                                <div class="growth-item w_11">
                                    <span class="growth_title">融合：</span>
                                    <span class="score van-ellipsis"><span class="member">{{ dataList.completeDayRongHe
                                        +
                                        dataList.completeMonthRongHe
                                            }}</span>/{{ dataList.rongHe }}</span>
                                </div>

                            </div>

                            <div class=" growth-row">

                                <div class="growth-item w_11 " style="margin-bottom: 0px;">
                                    <span class="growth_title">新装高套：</span>
                                    <span class="score van-ellipsis"><span class="member">{{
                                        dataList.completeDayXinliangZgz
                                            }}</span>/{{
                                                dataList.dailyXinliangZgzDenominator }}</span>
                                </div>

                                <div class="w_2">
                                    <div class="halvingLine"></div>
                                </div>

                                <div class="growth-item van-ellipsis w_11" style="margin-bottom: 0px;">
                                    <span class="growth_title">新装高套：</span>
                                    <span class="score van-ellipsis">
                                        <span class="member">{{ dataList.completeDayXinliangZgz +
                                            dataList.completeMonthXinliangZgz
                                            }}</span>/{{
                                                dataList.xinliangZgz }}</span>
                                </div>

                            </div>
                        </div>
                    </div>
                </transition>
            </van-swipe-item>
        </van-swipe>







    </div>
</template>

<script setup lang='ts'>
import { PropType, toRefs } from "vue";

const props = defineProps({
    dataList: {
        type: Object as PropType<GoalCompletion>,
        required: true
    },
    staffType: {
        type: String,
        required: true
    },

})

const current = defineModel('current', {
    type: Number,
    default: 0,
    required: true
})

const { dataList, staffType, } = toRefs(props)


function onChange(index: any) {
    current.value = index;
}


</script>

<style src="../index.scss" lang="scss" scoped></style>
<style lang="scss" scoped>
::v-deep(.van-swipe__indicator) {
    background-color: #a8a8a8;
}
</style>
