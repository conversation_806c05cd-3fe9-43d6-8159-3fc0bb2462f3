<template>
    <div>
        <!-- <GdMAP class="map" @getBuildingName="getBuildingName"></GdMAP> -->
        <TencentMap class="map" @getBuildingName="getBuildingName"></TencentMap>

        <div class="container_ mt-10">
            <div class="container__header">
                <div style="flex-direction: column;align-items: start;">
                    <div style="font-weight: 500; " class="van-multi-ellipsis--l2">{{ location.buildingName }}</div>
                    <div style="display: flex;align-items: center;" class="mt-3">
                        <img class="images" src="./assets/address.png" alt="" srcset="">
                        <span class="fs12 ml-3" style="color: #666666;">{{ location.assistName }}</span>
                    </div>
                </div>
            </div>
            <div class="container_body">

                <van-form required="auto" ref="formField">
                    <div class="form_row">
                        <div>网格ID(数字)</div>
                        <div class="selectOption-item" @click="popoverIsShow = true">
                            <div class="fs12" style="color: #257CFF;text-align: center;" v-if="formLine_.gridId.value">
                                <div class="mb-3">{{ formLine_.gridId.value }}</div>
                                <div>{{ formLine_.gridId.text }}</div>
                            </div>
                            <span class="fs12" style="color: #257CFF;" v-else>请选择网格</span>
                        </div>
                    </div>


                    <div class="form_row">
                        <div>拍照地点</div>
                        <van-popover :actions="tabList" @select="onSelect" placement="bottom">
                            <template #reference>
                                <div class="selectOption-item">
                                    <span style="color: #666;" class="fs12">{{ formLine_.address.text ?
                                        formLine_.address.text : '请选择拍照地点' }}</span>
                                    <van-icon name="play" class="play" color="#666" />
                                </div>
                            </template>
                        </van-popover>
                    </div>


                    <div class="form_row">

                        <van-field v-model="formLine_.description" name="desc" label="备注" label-align="left" rows="3"
                            autosize type="textarea" placeholder="如1栋1单元" show-word-limit
                            :rules="[{ required: true, message: ' 请输入备注信息如1栋1单元' }]" />
                    </div>


                    <div class="form_row">
                        <van-field name="fileList" label="拍照" label-align="left" input-align="right"
                            :rules="[{ required: true, message: '请拍摄照片' }]">
                            <template #input>
                                <div class="right_align">
                                    <van-uploader :after-read="handleAfterRead" capture="camera"
                                        :before-read="handleBeforeRead" v-model="formData.fileList" preview-size="89"
                                        :max-count="3">
                                        <template #default>
                                            <div class="form_card_camera">
                                                <van-image width="28px" height="23px" :src="camera" />
                                            </div>
                                        </template>
                                    </van-uploader>
                                </div>
                            </template>
                        </van-field>
                    </div>

                </van-form>
                <div class="mt-35 mb-35">
                    <van-button type="primary" color=" linear-gradient( 90deg, #73ACFF 0%, #237CFF 100%)" block
                        @click="submit">确认</van-button>
                </div>
            </div>


        </div>

        <van-popup v-model:show="popoverIsShow" round position="bottom" teleport="body">
            <van-picker title="人员列表" :columns="GRID_LIST" option-height="50" @confirm="selectGrid"
                @cancel="popoverIsShow = false">
                <template #option="option">
                    <div class="fs12">{{ option.value }}</div>
                    <div>{{ option.text }}</div>



                </template>
                <template #title>
                    <van-search class="mt-5 mb-5" v-model="searchGridName" placeholder="请输入搜索关键词" />
                </template>
            </van-picker>
        </van-popup>


    </div>
</template>

<script setup lang="ts">
interface Coordinate {
    lng: number;
    lat: number;
}

// import GdMAP from "@/components/GdMAP/index.vue";
import TencentMap from "@/components/GdMAP/TencentMap.vue";

import { getDictInfoAPI } from "@/api/common";
import { getPromoShotsGridAPI, savePromoShotsAPI } from "@/api/promoShots";
import { uploadFileHooks } from "@/hooks_modules/upload_hooks";
import camera from "@/views/template/MyDay/assets/images/camera.png";
import { debounce } from "@/utils/debounce";
import { useRouter } from "vue-router";
import { useUserInfo } from "@/stores/userInfo";
import gcj02towgs84 from "@/utils/map";
const userInfoStore = useUserInfo()

const router = useRouter()
const {
    handleAfterRead,
    handleBeforeRead,
    formLine,
    formData
} = uploadFileHooks()
let formField = ref();


const areaNameL2 = computed(() => {
    return userInfoStore.userInfo.areaNameL2
})
const formLine_ = reactive({
    address: {
        text: '',
        code: ''
    },
    gridId: {
        text: '',
        value: '',
        areaNameL2: '',
        areaIdL2: '',
    },
    description: '',
})

const tabList = ref<{ text: string, value: string }[]>([])
const location = reactive({
    buildingName: '--',
    lng: 118.78614,
    lat: 32.059093,
    assistName: '--'
})




function getBuildingName(params: { buildingName: string, lng: number, lat: number, assistName: string }) {
    location.buildingName = params.buildingName
    location.assistName = params.assistName
    const [lng, lat] = gcj02towgs84(params.lng, params.lat)
    location.lng = lng
    location.lat = lat
    loadGridList()
}











function onSelect(params: any) {
    formLine_.address.text = params.text
    formLine_.address.code = params.value
}
async function initDictInfo() {
    await getDictInfoAPI('promotional_photography_address').then((res: any) => {
        if (res.code == 200) {
            tabList.value = res.data.map((item: any) => ({ text: item.dictName, value: item.dictCode }))
        }

    })
}

const gridList = ref<{ text: string, value: string }[]>([])
const searchGridName = ref('')
const popoverIsShow = ref(false)

const GRID_LIST = computed(() => {
    const input = searchGridName.value.toLowerCase();

    return gridList.value.filter(item => {
        const gridName = item.text ? String(item.text).toLowerCase() : '';
        const gridId = item.value ? String(item.value).toLowerCase() : '';


        // 检查是否匹配 staffName、staffRoleName 或 staffCode
        return (
            gridName.includes(input) || gridId.includes(input)
        );
    });
})


function selectGrid(params: any) {
    const item = params.selectedOptions[0]
    formLine_.gridId.text = item.text
    formLine_.gridId.value = item.value
    formLine_.gridId.areaNameL2 = item.areaNameL2
    formLine_.gridId.areaIdL2 = item.areaIdL2
    popoverIsShow.value = false
}

function loadGridList() {
    getPromoShotsGridAPI({ lonlat: `${location.lng},${location.lat}` }).then((res: any) => {
        if (res.code == 200) {
            gridList.value = (res.data || []).map((item: any) => ({
                text: item.GRID_NAME,
                value: item.GRID_ID,
                areaNameL2: item.CSS_ZJ_NAME,
                areaIdL2: item.CSS_ZJ_ID
            })).filter((el: any) => el.areaNameL2 == areaNameL2.value)

        }
    })
}

const submit = debounce(() => {
    formField.value
        .validate()
        .then(() => {
            let params = {
                images: formLine.images.join(','),
                gridId: formLine_.gridId.value,
                gridName: formLine_.gridId.text,
                areaNameL2: formLine_.gridId.areaNameL2,
                areaIdL2: formLine_.gridId.areaIdL2,
                imageAddress: formLine_.address.text,
                imageAddressCode: formLine_.address.code,
                description: formLine_.description,

            }

            savePromoShotsAPI(params).then((res: any) => {
                if (res.code == 200) {
                    showSuccessToast("提交成功");
                    setTimeout(() => {
                        router.push({ path: '/inspectionLogGridList' })
                    }, 1500)
                } else {
                    showFailToast(res.msg);
                }

            })

        })
        .catch(() => {
            showFailToast("请完善信息");
        });
}, 300, true)




onMounted(() => {



    initDictInfo()
})

</script>

<style lang="scss" scoped>
.map {
    width: 100%;
    height: 300px;

}



.images {
    width: 13px !important;
    height: 18px !important;
}


.form_row {
    background: #EDF5FF;
    border-radius: 5px;
    padding: 8px 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 500;
    font-size: 14px;
    color: #3D3D3D;
    margin-bottom: 6px;
}

.form_card_camera {
    width: 89px;
    height: 89px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(103, 159, 255, 0.15);
}

.play {
    transform: rotate(90deg);
    margin-left: 3px;
}

::v-deep(.van-cell) {
    background-color: transparent;
    padding: 0;
}

:deep(.van-popover__wrapper) {
    display: block;
}

:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
    width: auto;
}

:deep(.van-picker-column__item) {
    display: flex;
    flex-direction: column;
}
</style>