
// export function mapSort(tabList: any[], propName?: string,): any[] {
//     const firstItem = tabList[0] ?? {}
//     const type = firstItem.type
//     const areaId = firstItem.areaId
//     const staffRole = firstItem.staffRole
//     const roleName = firstItem.roleName
//     const label = firstItem.label

//     if (type == 2 || staffRole || roleName) {
//         tabList.sort((item1, item2) => {
//             const areaPriority1 = rolePriorityMap[item1[propName ? propName : 'name']] || Infinity;
//             const areaPriority2 = rolePriorityMap[item2[propName ? propName : 'name']] || Infinity;
//             return areaPriority1 - areaPriority2; // 先比较角色优先级
//         });

//         return tabList

//     } else if (type == 0 || areaId || label) {
//         tabList.sort((item1, item2) => {
//             const areaPriority1 = AreaPriorityMap[item1[propName ? propName : 'name']] || Infinity;
//             const areaPriority2 = AreaPriorityMap[item2[propName ? propName : 'name']] || Infinity;
//             return areaPriority1 - areaPriority2; // 先比较角色优先级
//         });

//         return tabList
//     } else {
//         return tabList
//     }
// }


// 定义区域优先级映射，数字越小优先级越高
export const AreaPriorityMap: { [area: string]: number } = {
    '鼓楼': 1,
    '秦淮': 2,
    '栖霞': 3,
    '建邺': 4,
    '雨花': 5,
    '玄武': 6,
    '化工园': 7,
    '江宁': 8,
    '浦口': 9,
    '六合': 10,
    '溧水': 11,
    '高淳': 12,
    '大客中心': 13,
    '要客中心': 14,
    '工交中心': 15,

};

export const rolePriorityMap: { [role: string]: number } = {
    '分局长': 1,
    '副分局长': 2,
    '客经': 3,
    '片区长': 4,
    '门店经理': 5,
    '店长': 6,
    '值班经理': 7,
    '营业员': 8,
    '装维班长': 9,
    '装维': 10,
    '行销': 11,
    '网格': 12,
    '客户经理': 13
};
export function mapSort(tabList: any[], propName = 'name', isSort = true): any[] {
    const firstItem = tabList[0] ?? {}
    const type = firstItem.type
    const areaId = firstItem.areaId
    const staffRole = firstItem.staffRole
    const roleName = firstItem.roleName
    const label = firstItem.label
    if (type == 1) {
        return tabList;
    } else if (type == 2 || staffRole || roleName) {
        // 先过滤，只保留 rolePriorityMap 里有的角色
        const filteredList = tabList.filter(item => rolePriorityMap[item[propName]] !== undefined);
        // 再排序
        filteredList.sort((item1, item2) => {
            const rolePriority1 = rolePriorityMap[item1[propName]] || Infinity;
            const rolePriority2 = rolePriorityMap[item2[propName]] || Infinity;
            return rolePriority1 - rolePriority2;
        });

        return filteredList;

    } else if (type == 0 || areaId || label) {
        // 先过滤，只保留 AreaPriorityMap 里有的区域
        const filteredList = tabList.filter(item => AreaPriorityMap[item[propName]] !== undefined);
        // 再排序
        filteredList.sort((item1, item2) => {
            const areaPriority1 = AreaPriorityMap[item1[propName]] || Infinity;
            const areaPriority2 = AreaPriorityMap[item2[propName]] || Infinity;
            return areaPriority1 - areaPriority2;
        });

        return filteredList;
    } else {
        return tabList;
    }
}
