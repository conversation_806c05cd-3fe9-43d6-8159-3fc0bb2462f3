.selectOption {
    display: flex;
    align-items: center;

    & .selectOption-item {
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
        }
    }

    & .selectOption-item:active {
        background: #d8dee6;
    }

}
.container_body{
    padding-top: 6px !important;
}

.row {
    background: #EDF5FF;
    border-radius: 5px;
    padding: 12px 12px;
    margin-bottom: 3px;


    .list-item {

        display: grid;
        grid-template-columns: 35px 1fr minmax(60px, 80px);
        align-items: center;


        .top {
            width: 35px;
            height: 30px;
            background-image: url('./assets/top.svg');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            text-align: center;
            font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
            font-weight: 400;
            line-height: 25px;
            font-size: 16px;
            color: #FFFFFF;
        }

        .top1 {
            background-image: url('./assets/top1.svg');
        }

        .top2 {
            background-image: url('./assets/top2.svg');
        }

        .top3 {
            background-image: url('./assets/top3.svg');
        }

        .areaNAme {
            font-family: ' YouSheBiaoTiHei, YouSheBiaoTiHei';
            font-weight: 400;
            font-size: 13px;
            color: #1A76FF;
            margin-left: 13px;
            text-align: center;
        }

        .left {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;

            &>div:first-child {
                font-family: Source Han Sans, Source Han Sans;
                font-weight: 700;
                font-size: 13px;
                color: #3D3D3D;
            }


        }

    }

    .curCapacity {
        margin-top: 15px;
        display: flex;
        justify-content: space-between;

        .jf {
            color: #34C759;
        }

        .ty {
            color: #FF6A1A;
        }

        .kd {
            color: #477BF3;
        }

        .rh {
            color: #FFC34A;
        }

        .zgz {
            color: #E950E1;
        }

        .curCapacity-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .name {
                font-weight: 500;
                font-size: 12px;

                margin-top: 3px;
            }

            .value {
                font-weight: 700;
                font-size: 14px;

            }
        }
    }

}





:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
    width: auto;
}

.moreSelect {
    :deep(.van-popover__content) {
        .van-icon__image {
            width: 0.8em;
            height: 0.8em;
        }

        .van-popover__action-text {
            font-size: 12px;
        }
    }


}