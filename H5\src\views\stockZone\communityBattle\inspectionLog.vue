<template>

    <div class='container_'>
        <div class="container__header">
            <div>
                <img src="@/assets/computed.png" alt="" srcset="">
                <span class="ml-6">{{ route.query.gridName }}网格宣传检查</span>
            </div>
        </div>
        <div class="list-container">

            <div class="solution-card" v-for="(item, index) in tableList" :key="item.id">
                <div class="company-name">
                    <span class="name van-multi-ellipsis--l2" v-getName="{item}"> {{ item.staffName }}</span>
                    <div style="text-align: center;flex-shrink: 0;">
                        <div class="fs12 mb-3">{{ item.staffCode }}</div>
                        <span class="date">{{ item.createTime }}</span>
                    </div>

                </div>
                <div class="item-type">{{ item.gridTypeName }}</div>
                <div class="images">
                    <van-image fit="cover" v-for="el, i in item.images" lazy-load :src="el"
                        v-imgPreview="[item.images, i]" />
                </div>

                <div class="description">
                    <van-text-ellipsis rows="2" :content="item.description" expand-text="展开" collapse-text="收起"
                        position="middle" />
                </div>
            </div>

            <van-empty description="暂无数据" class="mt-8" style="background-color: #FBFCFF;border-radius: 15px;"
                v-if="tableList.length == 0" />
        </div>


    </div>


</template>

<script setup lang="ts">
interface Solution {
    id: string;
    createTime: string;
    areaIdL2: string; // 二级区域ID
    areaName: string; // 区域名称
    areaNameL2: string; // 二级区域名称
    description: string; // 描述信息
    gridId: string; // 网格ID
    gridName: string; // 网格名称
    imageAddress: string; // 图片地址描述
    imageAddressCode: string; // 图片地址代码
    images: string[]; // 图片链接
    lonlat: string | null; // 经纬度
    staffCode: string; // 员工编号
    staffName: string; // 员工姓名
    gridTypeName: string; //
}
// import { getVisitListAPI } from "@/api/customerVisit";
import { formatDate } from "@/utils/loadTime";
import { getPromoShotsListAPI, getGridDetailAPI } from "@/api/promoShots";

import { useRoute } from "vue-router";
const route = useRoute()


const tableList = ref<Solution[]>([])



function loadStorePackageList() {
    let params = {
        startTime: route.query.startTime,
        endTime: route.query.endTime,
    }
    getGridDetailAPI(params).then((res: any) => {
        if (res.code == 200) {


            // 将合并后的数据转换回数组
            res.data = res.data.filter((item: any) => item.id == route.query.id)
            for (const item of res.data) {
                item.images = item.images ? item.images.split(',') : []
                item.createTime = formatDate(item.createTime,'yyyy-MM-dd hh:mm')
            }

            tableList.value = res.data

        }
    })
}

















onMounted(() => {


    loadStorePackageList()
})
</script>

<style lang="scss" scoped>
.container_{
    min-height: 100%;
    background: #FFFFFF;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
}
.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;

    .selectOption-item {
        width: 100%;
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        position: relative;
        text-align: center;


        .play {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%) rotate(90deg);
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }
}




.solution-card {
    border-radius: 10px;
   
    padding: 11px 15px 15px;
    margin-bottom: 10px;

}

.company-name {
    font-weight: 700;
    font-size: 14px;
    color: #3D3D3D;
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.date {
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    flex: 0 0 auto;
}



.images {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 5px;
    margin-top: 14px;
    margin-bottom: 11px;

    &>.van-image {
        width: 100%;
        height: 100px;
        border-radius: 5px;
        overflow: hidden;
    }
}

.description {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 13px;
}

.item-type {
    display: inline-block;
    font-weight: 400;
    font-size: 12px;
    line-height: 13px;
    color: #217BFF;
    background: #EDF5FF;
    border-radius: 2px;
    padding: 3px;
    margin-top: 9px;
}








:deep(.van-popover__wrapper) {
    display: block;
}

:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
    width: auto;
}
</style>