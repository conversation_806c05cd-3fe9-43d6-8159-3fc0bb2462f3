<template>
    <div class="card container_">
        <div class="container__header" style="border-bottom: 0px;">
            <div style="display: flex; align-items: center;">
                <van-icon name="arrow-left" size="4.5vw" class="mr-5" @click="router.go(-1)" />
                <img src="@/assets/sendOrdersInfo.svg" alt="" srcset="">
                <span class="ml-6" style="font-weight: 500;">派单情况</span>
            </div>

        </div>


        <div class="container_body ">


            <div v-for="(staffItem, index) in tableList" :key="index">
                <div class="mb-8 mt-5" style="display: flex;align-items: center;">
                    <span class="itemName" v-getName="{ item: staffItem }">{{ staffItem.staffName }}</span>
                    <span class="ml-2 itemRoleName">{{ route.query.staffRoleName }}</span>
                </div>
                <div class="optionList">

                    <div class="item" v-for="item in optionList" :key="item.text">
                        <div class="value" :style="{ color: item.color }">{{ staffItem[item.props] }}</div>
                        <div class="text">{{ item.text }}</div>

                    </div>
                </div>

            </div>
            <van-empty description="暂无数据" class="mt-8" style="background-color: #fff;border-radius: 15px;"
                v-if="tableList.length == 0" />


        </div>




    </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from "vue-router";
import { getSendOrderZoneContactByRoleAPI } from "@/api/home";
const route = useRoute()
const router = useRouter()

interface Option {
    text: string;  // 选项文本
    color: string;  // 颜色的十六进制代码
    props: string,
}
// 定义每个选项的类型
interface OptionList {
    dispatchNum: Option;  // 派单总量
    implementedNum: Option;  // 已执行量
    successResultNum: Option
    pengdingNum: Option;  // 待处理量
    [key: string]: any
}
// 定义列表
interface DispatchData {

    dispatchNum: string; // 调度次数
    implementedNum: string; // 已执行次数
    outCallNum: string; // 外呼次数
    pengdingNum: string; // 待处理次数
    staffName: string; // 员工姓名
    staffRoleName: string; // 员工角色名称
    successConvertionTotal: string; // 成功转化总数
    [key: string]: any
}


const optionList = reactive<OptionList>({

    dispatchNum: {
        text: '派单总量',
        color: '#1A76FF',
        props: 'dispatchNum'
    },
    implementedNum: {
        text: '已执行量',
        color: '#6282FD',
        props: 'implementedNum'

    },
    successResultNum: {
        text: '成功数',
        color: '#00AFA3',
        props: 'successResultNum'
    },
    pengdingNum: {
        text: '待处理量',
        color: '#FFA34D',
        props: 'pengdingNum'
    },
})



const tableList = ref<DispatchData[]>([])


// 获取列表数据 
function laodSendOrderZoneContact() {
    let params = {
        staffRole: route.query.staffRole,
        areaIdL2: route.query.areaIdL2,
        startTime: route.query.startTime ? `${route.query.startTime} 00:00:00` : '',
        endTime: route.query.endTime ? `${route.query.endTime} 23:59:59` : '',
    }

    getSendOrderZoneContactByRoleAPI(params).then((res: any) => {
        tableList.value = res.data  
    })

}










onMounted(() => {
    laodSendOrderZoneContact()
})
</script>


<style lang="scss" scoped>
.card {
    font-family: 'Source Han Sans, Source Han Sans';
    height: 100%;
    background: #fbfcff;
    overflow-y: scroll;
    overflow-x: hidden;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px 10px 0px 0px;

    .container_body {
        padding-top: 0px;
        padding-bottom: 0px;

    }


    .itemName {
        font-weight: 700;
        font-size: 20px;
    }

    .itemRoleName {
        font-size: 12px;
        padding: 3px 6px;
        border-radius: 5px;
        background-color: #D8D8D8;
        text-align: center;
        margin-left: 5px;
    }
}

.optionList {

    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
    display: flex;
    // justify-content: space-around;
    flex-wrap: wrap;
    margin-bottom: 50px;

    .item {
        flex: 1 0 25%;
        max-width: 25%;
        display: flex;
        height: 100%;
        padding: 15px 5px;
        flex-direction: column;
        place-content: center;
        text-align: center;

        .text {
            font-weight: 500;
            font-size: 12px;
            color: #3D3D3D;
        }

        .value {
            font-family: 'DIN, DIN';
            font-weight: 700;
            font-size: 23px;
            color: #6282FD;
            margin-bottom: 7px;
        }
    }
}
</style>