<template>
    <div>
        <div class="card-header">
            <div class="selectOption">
                <van-popover :actions="tabList" @select="onSelect" placement="bottom">
                    <template #reference>
                        <div class="selectOption-item">
                            <span>{{ curSelectType.text }}</span>
                            <van-icon name="play" class="play" color="#1A76FF" />
                        </div>
                    </template>
                </van-popover>
            </div>
            <div class="selectOption-item" @click="calendarIsShow = true">
                <span class="van-ellipsis">{{ time ? time : '请选择时间' }}</span>
                <van-icon name="play" class="play" color="#1A76FF" />
            </div>
        </div>
        <div class=' container_ mt-6'  :style="{
            background: solutions.length == 0 ? '#FBFCFF':'transparent',
            boxShadow: solutions.length == 0 ? '0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF' : 'none'
        }">
            <div class="list-container">
                <van-list v-model:loading="loading" :finished="finished"
                    :finished-text="solutions.length > 0 ? '没有更多了' : ''" error-text="请求失败，点击重新加载" @load="onLoad">
                    <div class="list-item " v-for="item in solutions" :key="item.id" @click="GO(item)">

                        <div class="item-image">
                            <van-image lazy-load :src="item.images[0]" v-imgPreview="[item.images, 0]" />
                        </div>
                        <div class="item-details">
                            <div class="item-name mb-3">{{ item.customerName }}</div>
                            <div class="item-type">{{ item.proposalType }}</div>
                            <div class="contact">联系人：{{ item.customerTel }}</div>
                            <div class="date">提交时间：{{ item.createTime }}</div>
                            <div class="description van-multi-ellipsis--l3">{{ item.proposalInfo }}</div>
                        </div>


                    </div>
                </van-list>
                <van-empty description="暂无数据" class="mt-8" style="background-color: #FBFCFF;border-radius: 15px;"
                    v-if="solutions.length == 0" />
            </div>


        </div>

        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />
    </div>
</template>

<script setup lang="ts">
interface Solution {
    id: number;
    customerName: string;
    proposalType: string
    createTime: string;
    customerTel: string
    proposalInfo: string;
    images: string[];
}
import { getDictInfoAPI } from "@/api/common";
import { getProposalListAPI } from "@/api/planSubmission";
import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { useRoute, useRouter } from "vue-router";

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    onConfirm,
} = searchHooks({
    source: 'solutionRecord',
    callback: changeTime
})

const route = useRoute()
const router = useRouter()


const loading = ref(false)
const finished = ref(true)
const pageSize = ref(10);
const total = ref(0);
const pageNum = ref(1);


const solutions = ref<Solution[]>([])


function changeTime() {
    solutions.value = []
    loadList()
}

function loadList() {
    let params = {
        proposalTypeCodes: [curSelectType.value],
        startTime: formline.startTime ? `${formline.startTime} 00:00:00` : undefined,
        endTime: formline.endTime ? `${formline.endTime} 23:59:59` : undefined,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
    }
    loading.value = true
    getProposalListAPI(params).then((res: any) => {
        if (res.code == 200) {
            for (const item of res.data.records || []) {
                item.images = item.images ? item.images.split(',') : []
                item.images = item.images.map((el: string) => el)
            }

            solutions.value = [...solutions.value, ...res.data.records]
            total.value = res.data.total
            if (total.value <= solutions.value.length) {
                finished.value = true
            } else {
                finished.value = false
            }
        }
    }).finally(() => {
        loading.value = false
    })
}







const time = computed(() => {
    if (formline.startTime && formline.endTime) return formline.startTime == formline.endTime ? `${formline.startTime}` : `${formline.startTime}~${formline.endTime}`
    else return ``
})


const tabList = ref<{ text: string, value: string }[]>([])
const curSelectType = reactive<{ text: string, value: string }>({
    text: '',
    value: ''
})

function onSelect(item: any) {
    curSelectType.value = item.value
    curSelectType.text = item.text
    pageNum.value = 1
    pageSize.value = 10
    total.value = 0
    solutions.value = []
    loadList()
}
function onLoad() {
    pageNum.value = pageNum.value + 1
    loadList()
}

async function initDictInfo() {
    await getDictInfoAPI('planSubmission').then((res: any) => {
        if (res.code == 200) {
            tabList.value = res.data.map((item: any) => ({ text: item.dictName, value: item.dictCode }))

        }

    })
}

function GO(row: any) {
    router.push({
        path: '/solutionDetail', query: {
            id: row.id,

        }
    })
}

onMounted(() => {
    curSelectType.value = String(route.query.value)
    curSelectType.text = String(route.query.text)
    initDictInfo()
    loadList()
})
</script>

<style lang="scss" scoped>


.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;

    &>div {
        width: 50%;
        max-width: 50%;
    }

    .selectOption-item {
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;

        display: flex;
        justify-content: space-between;
        align-items: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;

        .play {
            transform: rotate(90deg);
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }
}





.list-container {
    display: flex;
    flex-direction: column;

    .list-item {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        background-color: #ffffff;
        border-radius: 8px;
        padding: 12px;
        box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    }

    .item-name {
        font-weight: 700;
        font-size: 14px;
        color: #3D3D3D;
        line-height: 16px;
    }



    .item-image .van-image {
        width: 100px;
        height: 100px;
        object-fit: cover;
        border-radius: 5px;
        overflow: hidden;
    }

    .item-details {
        margin-left: 16px;

        .item-type {
            display: inline-block;
            font-weight: 400;
            font-size: 12px;
            line-height: 13px;
            color: #217BFF;
            background: #EDF5FF;
            border-radius: 2px;
            padding: 3px;
        }

        .contact,
        .date,
        .description {
            font-weight: 400;
            font-size: 12px;
            color: #666666;
            margin-top: 7px;
        }

    }






}








:deep(.van-popover__wrapper) {
    display: block;
}

:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
    width: auto;
}
</style>