<template>
  <div class='Headlines container_'>
    <div class="container__header">
      <div>
        <img src="./assets/headline.svg" alt="" srcset="">
        <span class="ml-6">今日头条</span>
      </div>
    </div>

    <div class="container_body growth-header">
      <div class="w_11">
        <h3>个人日发展</h3>
      </div>
      <div class="w_2"></div>
      <div class="w_11">
        <h3>个人月发展</h3>
      </div>
    </div>

    <div class="container_body">
      <div class="growth-row">

        <div class="growth-item w_11">
          <span class="growth_title">积分：</span>
          <span class="score van-ellipsis"><span class="member">{{ dataList.completeDayPoints }}</span>/{{
            dataList.dailyPointsDenominator }}</span>
        </div>

        <div class="w_2">
          <div class="halvingLine"></div>
        </div>

        <div class="growth-item w_11">
          <span class="growth_title">积分：</span>
          <span class="score van-ellipsis"><span class="member">{{ dataList.completeDayPoints +
            dataList.completeMonthPoints}}</span>/{{ dataList.points }}</span>
        </div>

      </div>

      <div class=" growth-row">

        <div class="growth-item w_11">
          <span class="growth_title">融合：</span>
          <span class="score van-ellipsis"><span class="member">{{ dataList.completeDayRongHe }}</span>/{{
            dataList.dailyRongHeDenominator }}</span>
        </div>

        <div class="w_2">
          <div class="halvingLine"></div>
        </div>

        <div class="growth-item w_11">
          <span class="growth_title">融合：</span>
          <span class="score van-ellipsis"><span class="member">{{ dataList.completeDayRongHe +
            dataList.completeMonthRongHe
              }}</span>/{{ dataList.rongHe }}</span>
        </div>

      </div>

      <div class=" growth-row">

        <div class="growth-item w_11 " style="margin-bottom: 0px;">
          <span class="growth_title">新装高套：</span>
          <span class="score van-ellipsis"><span class="member">{{ dataList.completeDayXinliangZgz
              }}</span>/{{
                dataList.dailyXinliangZgzDenominator }}</span>
        </div>

        <div class="w_2">
          <div class="halvingLine"></div>
        </div>

        <div class="growth-item van-ellipsis w_11" style="margin-bottom: 0px;">
          <span class="growth_title">新装高套：</span>
          <span class="score van-ellipsis">
            <span class="member">{{ dataList.completeDayXinliangZgz + dataList.completeMonthXinliangZgz
              }}</span>/{{
                dataList.xinliangZgz }}</span>
        </div>

      </div>
    </div>




  </div>
</template>

<script setup lang='ts'>
import { HeadlinesHooks } from "./hooks/hook.ts";
const {
  dataList
} = HeadlinesHooks()

</script>

<style src="./index.scss" lang="scss" scoped></style>
