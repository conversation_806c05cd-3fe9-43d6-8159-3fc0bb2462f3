<template>
    <div class='Headlines container_'>
        <div class="container__header">
            <div>
                <img src="./assets/resultOptimization.svg" alt="" srcset="">
                <span class="ml-6">{{ route.query.areaName }}分公司{{ route.query.staffRoleName }}结果保障</span>
            </div>
            <div class="selectOption">
                <van-popover :actions="roleList" @select="onSelect" placement="bottom-end" class="cusPopover">
                    <template #reference>
                        <div class="selectOption-item">
                            <span>{{ curSelectType.text }}</span>
                            <van-icon name="play" class="play" color="#1A76FF" />
                        </div>
                    </template>
                </van-popover>
            </div>
        </div>


        <div class="container_body">
            <div class="row" style="background-color: #fff; padding: 6px 12px;">
                <div class="list-item">
                    <div class="fs13" style="font-weight: 700;color: #3D3D3D;">排序</div>
                    <div class="areaNAme fs13" style="font-weight: 700;color: #3D3D3D;">单元</div>
                    <div class="left">
                        <div>活力值</div>
                    </div>
                </div>

            </div>
            <div class="row" v-for="item, index in tableList" :key="item.id">
                <div class="list-item">
                    <div class="top" :class="[0, 1, 2].includes(index) ? `top${index + 1}` : ''">{{ index + 1 }}</div>
                    <div class="areaNAme">{{ item.name }}</div>
                    <div class="left">
                        <div>{{ item.avgHuoli }}</div>

                    </div>
                </div>

            </div>

            <van-empty description="暂无数据" v-if="tableList.length == 0" />
        </div>
    </div>
</template>

<script setup lang='ts'>

import { PopoverAction } from "vant";
import { getlistStaffRole } from "@/hooks_modules/getlistStaffRole";
import { getResultOptimizationListAPI } from "@/api/home";
import { useRouter } from "vue-router";
const { roleList } = getlistStaffRole()
const router = useRouter()
const route = useRoute()
type List = {
    name: string
    avgHuoli: number
    areaId: string | null
    areaIdL2: string | null
    id: string
    [key: string]: any
}
const curSelectType = reactive({
    text: route.query.staffRoleName || '全部',
    value: route.query.staffRole || ''
})


const tableList = ref<List[]>([])
function resultOptimizationList() {
    let params = {
        staffRole: curSelectType.value,
        areaId: route.query.areaId
    }
    getResultOptimizationListAPI(params).then((res: any) => {
        if (res.code == 200) {
            tableList.value = res.data
        }

    })
}

//切换条线
function onSelect(action: PopoverAction) {
    if (curSelectType.value == action.value) return
    curSelectType.text = action.text
    curSelectType.value = action.value
    resultOptimizationList()
}



onMounted(() => {
    resultOptimizationList()
})
</script>

<style lang="scss" scoped src="./index.scss"></style>

<!-- <style lang="scss" scoped>
.Headlines {
    min-height: 100%;
}

.selectOption {
    display: flex;
    align-items: center;
    flex: 0 0 auto;

    & .selectOption-item {
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
        }
    }

    & .selectOption-item:active {
        background: #d8dee6;
    }

}

.row {
    background: #EDF5FF;
    border-radius: 5px;
    padding: 12px 12px;
    margin-bottom: 8px;


    .list-item {

        display: grid;
        grid-template-columns: 35px 1fr minmax(60px, 80px);
        align-items: center;


        .top {
            width: 35px;
            height: 30px;
            background-image: url('./assets/top.svg');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            text-align: center;
            font-family: YouSheBiaoTiHei, YouSheBiaoTiHei;
            font-weight: 400;
            line-height: 25px;
            font-size: 16px;
            color: #FFFFFF;
        }

        .top1 {
            background-image: url('./assets/top1.svg');
        }

        .top2 {
            background-image: url('./assets/top2.svg');
        }

        .top3 {
            background-image: url('./assets/top3.svg');
        }

        .areaNAme {
            font-family: ' YouSheBiaoTiHei, YouSheBiaoTiHei';
            font-weight: 400;
            font-size: 18px;
            color: #1A76FF;
            margin-left: 13px;
            text-align: center;
        }

        .left {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;

            &>div:first-child {
                font-family: Source Han Sans, Source Han Sans;
                font-weight: 700;
                font-size: 16px;
                color: #3D3D3D;
            }


        }

    }

    .curCapacity {
        margin-top: 15px;
        display: flex;
        justify-content: space-between;

        .jf {
            color: #34C759;
        }

        .ty {
            color: #FF6A1A;
        }

        .kd {
            color: #477BF3;
        }

        .rh {
            color: #FFC34A;
        }

        .zgz {
            color: #E950E1;
        }

        .curCapacity-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .name {
                font-weight: 500;
                font-size: 12px;

                margin-top: 3px;
            }

            .value {
                font-weight: 700;
                font-size: 14px;

            }
        }
    }

}




:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
    width: auto;
}
</style> -->