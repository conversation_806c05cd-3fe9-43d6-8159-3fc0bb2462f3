import { showToast, showSuccessToast, showFailToast } from 'vant';
const directives = {
  install: ((app: any) => {
    app.directive('Copy', (el: any, binding: any) => {
      el.onclick = () => {
        if (!binding.value || binding.value == '') {
          return false;
        }
        if (!navigator.clipboard) {
          fallbackCopyTextToClipboard(binding.value);
          return;
        }
        navigator.clipboard.writeText(binding.value).then(
          function () {
            showSuccessToast('复制成功!');

          },
          function (err) {
            showFailToast('复制失败');

          }
        )
      }
    });
  })
}

function fallbackCopyTextToClipboard(text: string) {
  // 1.Create a selectable element
  let textArea = document.createElement("textarea");
  textArea.value = text;

  // 2.Use positioning to prevent page scrolling
  textArea.style.top = "0";
  textArea.style.left = "0";
  textArea.style.position = "fixed";
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  try {
    let successful = document.execCommand("copy");
    let msg = successful ? "successful" : "unsuccessful";
    showSuccessToast('复制成功!');

  } catch (err) {
    showFailToast('Copying  unsuccessful');

  }
  // 3.Remove element
  document.body.removeChild(textArea);
}
export default directives;
