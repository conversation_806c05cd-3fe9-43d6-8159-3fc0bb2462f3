
import request from "../utils/request";
import configAxios from './config'
// 创建活动接口
export function createActivityAPI(data: Object) {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/activity/create`,
        method: "POST",
        data,
    })
}
// 获取活动列表接口
export function getActivityListAPI(data: Object) {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/activity/list`,
        method: "POST",
        data,
    })
}
// 获取派单列表接口
export function getDispatchListAPI(data: Object) {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/collection/myPage`,
        method: "POST",
        data,
    })
}
//创建派单
export function createDispatchAPI(data: Object) {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/collection/create`,
        method: "POST",
        data,
    })
}
// 获取派单详情
export function getDispatchDetailAPI(data: Object) {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/collection/detail`,
        method: "POST",
        data,
    })
}
//更新派单主题
export function updateDispatchAPI(data: Object) {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/collection/update`,
        method: "POST",
        data,
    })
}
//部门主任下载派单模板
export function downloadTemplateAPI() {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/excelExport/department`,
        method: "GET",
        responseType: "blob",
    })
}
//部门主任上传派单模板
export function uploadTemplateAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/excelImport/department`,
        method: "POST",
        data,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

//区域派单人下载客户经理信息
export function downloadManagerInfoAPI(params: any) {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/excelExport/area`,
        method: "GET",
        responseType: "blob",
        params,
    })
}

//区域派单人区域派单全量下载
export function downloadAllManagerInfoAPI(params: any) {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/excelExport/accManager`,
        method: "GET",
        responseType: "blob",
        params,
    })
}

//区域派单人导入客户经理信息
export function uploadManagerInfoAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/excelImport/area`,
        method: "POST",
        data,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

//区域派单人区域派单全量下载
export function uploadAllManagerInfoAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/excelImport/accManager`,
        method: "POST",
        data,
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    })
}

//获取派单回单统计列表统计
export function getReceiptStatisticsCountAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/ticket/count`,
        method: "POST",
        data,
    })
}

//获取派单回单统计列表
export function getReceiptStatisticsListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/ticket/page`,
        method: "POST",
        data,
    })
}

//导出派单回单统计列表
export function exportReceiptStatisticsListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/pc/dispatch/ticket/export`,
        method: "POST",
        data,
        responseType: "blob",
    })
}
