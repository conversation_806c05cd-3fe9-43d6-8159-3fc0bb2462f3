import { getlistDeptOrSubOptionsAPI } from "@/api/common";
import { mapSort } from "@/utils/mapSort";
type IAreaList = {
    text: string,
    value: number | string | undefined
}
type ParamsProps = {
    areaId: string,
    isPushAll: boolean, //是否不添加全部选项  默认添加
    isNotSort: boolean,  //是否不需要排序 默认需要
    callback: (data?: any) => void;

}


export const getlistDeptOrSubOptions = function (params: Partial<ParamsProps> = {}) {
    const { areaId, isPushAll, isNotSort } = params
    const areaList = ref<IAreaList[]>([])
    function loadListDeptOrSubOptions() {

        getlistDeptOrSubOptionsAPI({ areaId: areaId }).then((res: any) => {
            if (!isNotSort) res.data = mapSort(res.data, 'label')
            areaList.value = res.data.map((item: any) => {
                return {
                    text: item.label,
                    value: item.value
                }
            })
            if (isPushAll) {

            } else {
                areaList.value.unshift({ text: '全部', value: undefined })
            }

        })
    }
    onMounted(() => {
        loadListDeptOrSubOptions()
    })
    return {
        areaList,
        loadListDeptOrSubOptions
    }

}


