<template>
  <div>
    <div class="card-header">
      <div class="card-label">{{ route.query.descriptionId ? '编辑派单' : '创建派单' }}</div>
      <!-- <el-button type="primary" color="#0052D9">
        <i class="icon-daorukehux fs16"></i>
        <span class="ml-3">导入客户</span>
      </el-button> -->
    </div>
    <div class="hr"></div>

    <!-- 基本信息表单 -->
    <el-form ref="ruleFormRef" :inline="true" :model="formLine" class="demo-form-inline" :rules="rules" label-suffix=":"
      label-width="auto" label-position="top">
      <el-form-item label="派单主题" prop="orderName">
        <el-input v-model="formLine.orderName" style="width: 300px" placeholder="请输入派单主题" clearable />
      </el-form-item>
      <el-form-item label="活动名称" prop="activityName">
        <el-input v-model="formLine.activityName" disabled style="width: 300px" placeholder="活动名称" />
      </el-form-item>
      <el-form-item label="派单说明" prop="orderDescription" style="width: 100%;">
        <el-input v-model="formLine.orderDescription" style="min-width: 800px" type="textarea" placeholder="请输入派单说明"
          :rows="6" clearable />
      </el-form-item>
    </el-form>

    <!-- 字段配置区域 -->
    <div class="field-config-container demo-form-inline">
      <!-- 左侧字段类型列表 -->
      <div class="field-types-panel">


        <h3>可选字段</h3>
        <draggable v-model="fieldTypes" ghost-class="ghost" handle=".move" :force-fallback="true"
          :group="{ name: 'fields', pull: 'clone', put: false }" :sort="false" item-key="type" class="field-types-list"
          :clone="cloneItem">
          <template #item="{ element }">
            <div class="field-type-item move">
              <i :class="element.icon"></i>
              <span>{{ element.label }}</span>
            </div>
          </template>
        </draggable>




      </div>

      <!-- 右侧配置区域 -->
      <div class="field-config-panel">
        <h3>字段配置</h3>
        <draggable v-model="fields" handle=".move" :force-fallback="true"
          :group="{ name: 'fields', pull: false, put: true }" :sort="true" item-key="id" class="field-config-list"
          :class="{ 'is-drag-over': isDragging }" animation="300" :scrool="true">
          <template #item="{ element }">
            <div class="field-config-item">
              <div class="field-header move">
                <i :class="element.icon"></i>
                <span>{{ element.label }}</span>
                <el-icon class="delete-icon" @click="removeField(element.id)">
                  <DeleteFilled />
                </el-icon>
              </div>

              <!-- 字段配置内容 -->
              <div class="field-content">
                <template v-if="element.type === 'input'">
                  <el-form-item label="字段名称">
                    <el-input v-model="element.keyName" style="width: 200px" placeholder="请输入字段名称" clearable />
                  </el-form-item>
                  <el-form-item label="占位提示">
                    <el-input v-model="element.placeholder" style="width: 200px" placeholder="请输入占位提示" />
                  </el-form-item>
                </template>

                <template v-if="element.type === 'select'">
                  <el-form-item label="字段名称">
                    <el-input v-model="element.keyName" style="width: 200px" placeholder="请输入字段名称" clearable />
                  </el-form-item>
                  <el-form-item label="占位提示">
                    <el-input v-model="element.placeholder" style="width: 200px" placeholder="请输入占位提示" />
                  </el-form-item>
                  <el-form-item label="选项配置" class="select-option-config">
                    <div v-for="(option, index) in element.options" :key="index" class="option-item">

                      <el-input v-model="option.label" style="width: 200px" placeholder="选项名称" />
                      <!-- <el-input v-model="option.value" style="width: 200px" placeholder="选项值" /> -->
                      <el-icon class="delete-icon" @click="removeOption(element, index)">
                        <DeleteFilled />
                      </el-icon>
                      <i class="icon-tianjia1 fs18" style="color: #606266;" v-if="index === element.options.length - 1"
                        @click="addOption(element)"></i>

                    </div>


                  </el-form-item>
                </template>

                <template v-if="element.type === 'checkbox'">
                  <el-form-item label="字段名称">
                    <el-input v-model="element.keyName" style="width: 200px" placeholder="请输入字段名称" clearable />
                  </el-form-item>
                  <el-form-item label="占位提示">
                    <el-input v-model="element.placeholder" style="width: 200px" placeholder="请输入占位提示" />
                  </el-form-item>
                  <el-form-item label="选项配置" class="select-option-config">
                    <div v-for="(option, index) in element.options" :key="index" class="option-item">
                      <el-input v-model="option.label" style="width: 200px" placeholder="选项名称" clearable />
                      <!-- <el-input v-model="option.value" style="width: 200px" placeholder="选项值" clearable /> -->
                      <el-icon class="delete-icon" @click="removeOption(element, index)">
                        <DeleteFilled />
                      </el-icon>
                      <i class="icon-tianjia1 fs18" style="color: #606266;" v-if="index === element.options.length - 1"
                        @click="addOption(element)"></i>
                    </div>

                  </el-form-item>
                </template>

                <template v-if="element.type === 'date'">
                  <el-form-item label="字段名称">
                    <el-input v-model="element.keyName" style="width: 200px" placeholder="请输入字段名称" clearable />
                  </el-form-item>
                  <el-form-item label="占位提示">
                    <el-input v-model="element.placeholder" style="width: 200px" placeholder="请输入占位提示" />
                  </el-form-item>
                  <el-form-item label="日期类型">
                    <el-select v-model="element.dateType" style="width: 300px" placeholder="请选择日期类型">
                      <el-option label="日期(YYYY-MM-DD)" value="date" />
                      <el-option label="日期时间(YYYY-MM-DD HH:mm:ss)" value="datetime" />
                      <el-option label="日期范围(YYYY-MM-DD/YYYY-MM-DD)" value="daterange" />
                    </el-select>
                  </el-form-item>
                </template>
              </div>
            </div>
          </template>

          <template #footer>
            <div v-if="fields.length === 0" class="empty-tip">
              拖拽左侧字段到此处进行配置
            </div>
          </template>
        </draggable>
      </div>
    </div>
    <div class="footer">
      <el-button class=" btn btn-secondary" @click="resetForm(ruleFormRef)">取消</el-button>

      <el-button type="primary" color="#0052D9" @click="submitForm(ruleFormRef)">
        <i class="icon-tianjia1 fs16"></i>
        <span class="ml-3">发送派单</span>
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { DeleteFilled } from '@element-plus/icons-vue'
import draggable from 'vuedraggable'
import type { FormInstance, FormRules } from 'element-plus'
import { createDispatchAPI, getDispatchDetailAPI, updateDispatchAPI } from '@/api/activity'


interface FieldConfig {
  fieldScenerio: number;
  fieldType: string;
  fieldName: string;
  fieldDesc: string;
  fieldExt: string | undefined;
  fieldSort: number;
}
const router = useRouter()
const route = useRoute()
interface RuleForm {
  orderName: string
  activityName: string
  orderDescription: string
  activityId: string | undefined
}


const isDragging = ref(false)



// 表单实例
const ruleFormRef = ref<FormInstance>()
// 表单数据
const formLine = reactive<RuleForm>({
  orderName: '',
  activityName: '',
  orderDescription: '',
  activityId: undefined,
})
const rules = reactive<FormRules<RuleForm>>({
  orderName: [
    { required: true, message: '请输入活动主题', trigger: 'blur' },
    { min: 2, max: 15, message: '活动主题长度在2到15个字符之间', trigger: 'blur' }
  ],
  activityName: [
    { required: true, message: '请输入活动名称', trigger: 'blur' },
  ],
  orderDescription: [
    { required: true, message: '请输入派单说明', trigger: 'blur' },
  ]
})



// 字段类型定义
type FieldType = {
  type: string
  label: string
  icon: string
  keyName: string
  id?: string | number
  placeholder?: string
  dateType?: string
  options?: Array<{ label: string; }>
}

// 可选字段类型
const fieldTypes = ref<Omit<FieldType, 'keyName'>[]>([
  { type: 'input', label: '文本框', icon: 'icon-wenbenkuang' },
  { type: 'select', label: '下拉选择', icon: 'icon-xialaxuanze' },
  { type: 'date', label: '日期选择', icon: 'icon-riqixuanze' },
  { type: 'checkbox', label: '多选框', icon: 'icon-a-6duoxuankuang' }
])

// 已配置的字段
const fields = ref<FieldType[]>([])

// 克隆字段
const cloneItem = (original: FieldType) => {
  const cloned = {
    ...original,
    keyName: '',
    id: `${original.type}_${Date.now()}`,
  }

  if (['select', 'checkbox'].includes(original.type)) {
    // cloned.options = [{ label: '', value: '' }]
    cloned.options = [{ label: '' }]

  }

  return cloned
}

// 移除字段
const removeField = (id: string | number) => {
  const index = fields.value.findIndex(field => field.id === id)
  if (index > -1) {
    fields.value.splice(index, 1)
  }
}

// 添加选项
const addOption = (field: FieldType) => {
  if (!field.options) {
    field.options = []
  }
  // field.options.push({ label: '', value: '' })
  field.options.push({ label: '' })

}

// 移除选项
const removeOption = (field: FieldType, index: number) => {
  if (field.options) {
    field.options.splice(index, 1)
  }
}


const submitForm = async (formEl: FormInstance | undefined) => {

  if (!formEl) return
  await formEl.validate((valid, field) => {
    if (valid) {

      let params = {
        name: formLine.orderName,
        desc: formLine.orderDescription,
        activityId: formLine.activityId,
        id: route.query.descriptionId,
        fields: (fields.value as FieldType[]).map((item: any, index: number) => {
          let obj: FieldConfig = {
            fieldScenerio: 2,
            fieldType: item.type,
            fieldName: item.keyName,
            fieldDesc: item.placeholder,
            fieldExt: undefined,
            fieldSort: index + 1
          }
          if (item.type == 'date') {
            obj.fieldExt = JSON.stringify(item.dateType)
          } else if (item.type == 'select' || item.type == 'checkbox') {
            obj.fieldExt = JSON.stringify((item.options || []).map((option: any) => {
              return {
                label: option.label,
                // value: option.value
              }
            }))
          }
          return obj
        })
      }



      const APi = route.query.descriptionId ? updateDispatchAPI : createDispatchAPI
      APi(params).then((res: any) => {
        if (res.code == 200) {
          ElMessage.success('操作成功')

          setTimeout(() => {
            router.push({
              path: '/seedOrderList',
              query: {
                ...route.query,
              }
            })
          }, 500);
        }

      })
    }
  })
}
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()

}
const initData = () => {
  if (route.query.descriptionId) {
    getDispatchDetailAPI({ id: route.query.descriptionId }).then((res: any) => {
      if (res.code == 200) {
        const { data } = res
        formLine.orderName = data.name
        formLine.orderDescription = data.desc
        formLine.activityName = data.activityName
        fields.value = data.fields.map((item: any) => {
          let row = fieldTypes.value.find((el: any) => el.type == item.fieldType) as FieldType
          let obj: FieldType = {
            type: item.fieldType,
            label: row.label,
            keyName: item.fieldName,
            icon: row.icon,
            id: item.id,
            placeholder: item.fieldDesc
          }
          if (item.fieldType == 'date') {
            obj.dateType = JSON.parse(item.fieldExt)
          } else if (item.fieldType == 'select' || item.fieldType == 'checkbox') {
            obj.options = JSON.parse(item.fieldExt)
          }


          return obj
        })
      } else {
        ElMessage.error('获取派单详情失败')
      }
    })
  }
}

onMounted(() => {
  formLine.activityName = route.query.activityNmae as string
  formLine.activityId = route.query.id as string | undefined
  if (route.query.descriptionId) {
    initData()
  }
})
</script>

<style lang="scss" scoped>
.field-config-container {
  position: relative;
  display: flex;
  gap: 20px;
  margin-top: 20px;
  padding: 10px;
  background: #f5f7fa;
  border-radius: 8px;
  user-select: none;

  .field-types-panel,
  .field-config-panel {
    background: #fff;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    h3 {
      margin: 0 0 16px;
      font-size: 16px;
      color: #1f2d3d;
    }
  }

  .field-types-panel {
    width: 200px;
    flex-shrink: 0;
    position: relative;

    .field-types-list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    .field-type-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      cursor: grab;
      transition: all 0.3s;

      &:hover {
        background: #f5f7fa;
        border-color: #409eff;
      }

      i {
        font-size: 16px;
        color: #606266;
      }
    }
  }

  .field-config-panel {
    flex: 1;
    min-width: 0;

    .field-config-list {
      min-height: 200px;
    }

    .field-config-item {
      background: #fff;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      margin-bottom: 16px;

      .field-header {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 12px;
        border-bottom: 1px solid #dcdfe6;
        background: #f5f7fa;
        cursor: grab;

        i {
          font-size: 16px;
          color: #606266;
        }

        .delete-icon {
          margin-left: auto;
          cursor: pointer;
          color: #f56c6c;

          &:hover {
            color: #f78989;
          }
        }
      }

      .field-content {
        padding: 16px;
      }
    }
  }
}

.select-option-config {
  :deep(.el-form-item__content) {
    display: flex;
    align-items: start;
    flex-direction: column;
  }
}

.option-item {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  align-items: center;

  .delete-icon {
    color: #f56c6c;
    cursor: pointer;

    &:hover {
      color: #f78989;
    }
  }
}

.empty-tip {
  text-align: center;
  color: #909399;
  padding: 32px 0;
}

.ghost {
  opacity: 0.5;
  background: #f0f9ff;
  border: 1px dashed #409eff;
}

.is-drag-over {
  border: 2px dashed #409eff;
  background: #f0f9ff;
}



.footer {
  padding: 20px 0px;
  display: flex;
  justify-content: flex-end;
}
</style>