<template>

    <div class="card container_" v-swipe-gesture="swipeCallback">
        <div class="container__header">
            <div style="display: flex; align-items: center;">
                <img src="@/assets/sendOrdersInfo.svg" alt="" srcset="">
                <span class="ml-6" style="font-weight: 500;">派单SOP</span>
            </div>
            <cusTabs :tabList="tabList" v-model:curSelectTab="curSelectTab" @changeSelectTab="changeSelectTab">
            </cusTabs>
        </div>
        <div class="container_body ">

            <div class="table-container" @touchstart.stop>
                <table>
                    <thead>
                        <template v-if="curSelectTab == 0">

                            <tr>
                                <th class="sticky">派单名称</th>

                                <th class="main-title">类别 </th>

                                <th class="main-title">
                                    <van-popover placement="bottom">
                                        <div class="popoverBody">
                                            <li>一个活动id下同一个人所有接单汇总</li>
                                        </div>
                                        <template #reference>
                                            接单量<van-icon class="ml-3" name="question" size="12" color="#1bc5bd" />
                                        </template>
                                    </van-popover>
                                </th>
                                <th class="main-title">
                                    <van-popover placement="bottom-end">
                                        <div class="popoverBody">
                                            <li>个人对应活动派单的执行量</li>
                                        </div>
                                        <template #reference>
                                            累计执行量<van-icon class="ml-3" name="question" size="12" color="#1bc5bd" />
                                        </template>
                                    </van-popover>
                                </th>
                            </tr>


                        </template>
                        <template v-else-if="curSelectTab == 1">
                            <tr>
                                <th>派单名称</th>
                                <th class="main-title">类别 </th>

                                <th class="main-title">
                                    <van-popover placement="bottom-end">
                                        <div class="popoverBody">
                                            <li>活动下派单总量减去已执行量</li>
                                        </div>
                                        <template #reference>
                                            待执行量<van-icon class="ml-3" name="question" size="12" color="#1bc5bd" />
                                        </template>
                                    </van-popover>


                                </th>
                                <th class="main-title">
                                    <van-popover placement="bottom-end">
                                        <div class="popoverBody">
                                            <li>该活动下当天外呼时长大于0s的数量</li>
                                        </div>
                                        <template #reference>
                                            当天执行量<van-icon class="ml-3" name="question" size="12" color="#1bc5bd" />
                                        </template>
                                    </van-popover>
                                </th>
                            </tr>

                        </template>
                        <template v-if="curSelectTab == 2">
                            <tr>
                                <th class="main-title">人员</th>
                                <th class="main-title">角色</th>
                                <th class="main-title">
                                    <van-popover placement="bottom-end">
                                        <div class="popoverBody">
                                            <li>当前人员所有活动的未执行量</li>
                                        </div>
                                        <template #reference>
                                            剩余派单量<van-icon class="ml-3" name="question" size="12" color="#1bc5bd" />
                                        </template>
                                    </van-popover>
                                </th>
                            </tr>
                        </template>
                    </thead>
                    <tbody>
                        <template v-if="curSelectTab == 0">
                            <tr v-for="(row, index) in tableData" :key="index">
                                <td class="sticky">
                                    <div style=" white-space: normal;">
                                        {{ row.activityName }}
                                    </div>
                                </td>
                                <td>{{ row.frameType }}</td>
                                <td>{{ row.orderCount }}</td>
                                <td>{{ row.implementedCount }}</td>




                            </tr>
                        </template>
                        <template v-else-if="curSelectTab == 1">
                            <tr v-for="(row, index) in tableData" :key="index">
                                <td class="sticky">
                                    <div style=" white-space: normal;">
                                        {{ row.activityName }}
                                    </div>
                                </td>
                                <td class='areaNameL2'>{{ row.frameName }}</td>
                                <td>{{ row.waitCount }}</td>
                                <td>{{ row.dayCount }}</td>
                            </tr>

                        </template>
                        <template v-else-if="curSelectTab == 2">
                            <tr v-for="(row, index) in tableData" :key="index">
                                <td class="sticky" v-if="shouldShowName(row.staffCode, index)"
                                    :rowspan="getRowSpan(row.staffCode)" v-getName="{ item: row }">{{ row.staffName }}
                                </td>
                                <td>
                                    {{ row.staffRoleName }}
                                </td>
                                <td>
                                    <span :class="row.waitCount < 20 ? 'text-danger' : ''"> {{ row.waitCount }}</span>
                                </td>

                            </tr>

                        </template>
                    </tbody>



                </table>

            </div>
            <van-empty description="暂无数据" class="mt-8" style="background-color: #FBFCFF;border-radius: 15px;"
                v-if="tableData.length == 0" />
        </div>

    </div>





</template>

<script setup lang="ts">
import { AxiosResponse } from "axios";
import cusTabs from "@/components/cusTabs/index.vue";
import { debounce } from "@/utils/debounce";
import { showSuccessToast } from "vant";
import { useRoute } from "vue-router";
import {
    getArriveStationAPI,
    getSopOrderReviewAPI,
    getSopOrderCateringAPI
} from "@/api/dispatchSOP";

const route = useRoute()
const props = defineProps({
    moduleType: {
        type: String,
        default: ''
    }
})

const { moduleType } = toRefs(props)



const tabList = computed(() => {
    const tabList = [
        { text: '到岗提醒', value: 0 },
        { text: '复盘提醒', value: 1 },

    ]

    if (moduleType.value == 'lead') {
        tabList.push({ text: '配餐提醒', value: 2 })
    }
    return tabList
})




const curSelectTab = ref(0)
function changeSelectTab(params: any) {
    tableData.value = []
    loadList()

}


function swipeCallback(value: string) {
    let curIndex = tabList.value.findIndex(item => item.value == curSelectTab.value)
    if (value == 'left') {
        if (curSelectTab.value == 0) {
            return
        } else {
            curSelectTab.value = tabList.value[curIndex - 1].value

        }

    } else {
        if (curSelectTab.value == 2) {
            return
        } else {
            curSelectTab.value = tabList.value[curIndex + 1].value

        }
    }
}




const tableData = ref<any>([])

function loadList() {
    let API: (() => Promise<AxiosResponse<any, any>>) | undefined
    switch (curSelectTab.value) {
        case 0:
            API = getArriveStationAPI
            break
        case 1:
            API = getSopOrderReviewAPI
            break
        case 2:
            API = getSopOrderCateringAPI
            break

        default:
            return
    }



    API().then((res: any) => {
        if (res.code == 200) {
            if (curSelectTab.value == 2) {
                res.data.sort((a: any, b: any) => a.waitCount - b.waitCount)
            }
            tableData.value = res.data
        }
    })
}




// 合并单元格
function shouldShowName(staffCode: string, index: number): boolean {
    return index === tableData.value.findIndex((row: any) => row.staffCode === staffCode);
}

function getRowSpan(staffCode: string): number {
    return tableData.value.filter((row: any) => row.staffCode === staffCode).length;
}

function calculateColor(value: number, minValue: number = 0, maxValue: number = 10): string {
    // Clamp the value between minValue and maxValue
    value = Math.max(minValue, Math.min(value, maxValue));

    // Normalize the value to a range of 0 to 1
    const normalizedValue = (value - minValue) / (maxValue - minValue);

    // Define the starting color (blue) and ending color (red)
    const startColor = { r: 0x35, g: 0x75, b: 0xF7 };  // #3575F7
    const endColor = { r: 0xFF, g: 0x00, b: 0x00 };    // #FF0000

    // Calculate the interpolated color
    const r = Math.round(startColor.r + (endColor.r - startColor.r) * normalizedValue);
    const g = Math.round(startColor.g + (endColor.g - startColor.g) * normalizedValue);
    const b = Math.round(startColor.b + (endColor.b - startColor.b) * normalizedValue);

    // Return the color in hex format
    return `#${r.toString(16).padStart(2, '0').toUpperCase()}${g.toString(16).padStart(2, '0').toUpperCase()}${b.toString(16).padStart(2, '0').toUpperCase()}`;
}

onMounted(() => {

    curSelectTab.value = route.query.type ? Number(route.query.type) : 0

    loadList()
})
</script>

<style lang="scss" scoped>
.card {
    font-family: 'Source Han Sans, Source Han Sans';
    min-height: 100%;
    background: #fbfcff;
    border-radius: 10px 10px 0px 0px;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);

    .container__header {

        flex-wrap: wrap;
        padding-bottom: 5px;
    }


    .table-container {
        width: 100%;
        overflow-x: auto;
        margin-top: 10px;

        padding-bottom: 10px;

        table {
            width: 100%;
            border-collapse: collapse;
            text-align: center;
            font-size: 13px;

            th,
            td {
                padding: 8px;
                border: 1px solid #ddd;

                white-space: nowrap; // 禁止换行
            }

            .areaNameL2 {
                width: 75px;
                white-space: normal;
            }

            .sticky {

                /* 固定表格内容的门店列 */
                position: sticky;
                left: 0px;
                top: 0px;
                background-color: #fff;
                /* 设置背景颜色以覆盖其他列 */
                z-index: 1;
                border: 1px solid #ddd;

                /* 确保门店列在最上层 */
            }

            .green {
                background-color: #92d050;
            }

            .yellow {
                background-color: #ffc000;
            }



            .main-title {
                font-size: 13px;
                font-weight: bold;
                padding: 12px;
                background-color: #2e70ba;
                color: #fff;
                text-align: center;
            }

            thead tr:nth-child(n) th {
                background-color: #2e70ba;
                color: #fff;
                font-weight: bold;
                padding: 10px;
                text-align: center;
            }

            thead tr:nth-child(3) th {
                background-color: #2e70ba;
                color: #fff;
                font-weight: bold;
                padding: 8px;
            }



            tbody tr:nth-child(even) {
                background-color: #f2f2f2;
            }




            tbody tr:hover {
                background-color: #e6f7ff;
            }

            tbody td.highlight {
                background-color: #ffe58f;
                font-weight: bold;
            }
        }


    }


}
</style>