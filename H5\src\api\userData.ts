import request from "../utils/request";
import configAxios from './config'

//获取发展情况数据
export function getDevelopmentAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/data/develop/info`,
        method: "POST",
        data,
    });
}
export function getGovDevelopmentAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/gov/develop/info`,
        method: "POST",
        data,
    });
}


//获取全区 全分局发展情况数据
export function getDevelopmentAreaAndDeptAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/manager/data/list`,
        method: "POST",
        data,
    });
}
//数智运营
export function getDevelopmentEnergyAndPointsAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/manager/data/yj`,
        method: "POST",
        data,
    });
}

//商机运营
export function getDevelopmentbusinessOperationAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/manager/data/businessOperation`,
        method: "POST",
        data,
    });
}