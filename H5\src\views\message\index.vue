<template>
    <div class="container_">
        <div class="container__header">
            <cusTabs :tabList="tabList" v-model:curSelectTab="curSelectTab" @changeSelectTab="changeSelectTab">
            </cusTabs>
            <!-- <div v-if="curSelectTab == '未读消息'" style="display: flex;align-items: center;" @click="handleReadAll">
                <img src="./assets/read.svg" alt="" srcset="">
                <span class="fs12 ml-4" style="color: #5F8CE6;">全部已读</span>
            </div> -->
        </div>
        <div class="container_body " :class="curSelectTab == '已读消息' ? 'message-list-read' : ''">
            <div v-if="messageList.length > 0" class="message-list">
                <div v-for="(item, index) in messageList" :key="index" class="message-item"
                    @click="reviewMessage(item)">
                    <div class="message-icon">
                        <img :src="getMessageIcon(item)" alt="">
                    </div>
                    <div style="flex: 1 0 auto;">
                        <div class="message-content">
                            <div class="message-title">{{ item.title }}</div>

                            <div class="message-action" v-if="item.readFlag === 0">

                                <span @click.stop="handleRead(item)">{{ item.msgSource == 100 ? '立刻查看' : '立刻处理'
                                }}</span>
                                <van-icon name="arrow" color="#327FF0" />
                            </div>

                        </div>
                        <div class="message-time mt-2 mb-2">{{ item.createTime }}</div>
                        <div class="message-desc mt-4">{{ item.content }}</div>
                    </div>


                </div>
            </div>

            <van-empty v-else image-size="160" description="暂时没有更多提醒啦~">
                <template #image>
                    <img src="./assets//emptyMessage.svg" alt="" srcset="">
                </template>
            </van-empty>

        </div>
    </div>
</template>

<script setup lang="ts">
import { getMessageSummaryBySenderAPI, readMessageAPI } from "@/api/message";
import cusTabs from "@/components/cusTabs/index.vue";
import { ref, onMounted } from 'vue';
import { formatDate } from "@/utils/loadTime";
import { useRouter } from "vue-router";

const router = useRouter()
interface MessageItem {
    id: number;
    title: string;
    content: string;
    msgType: number;
    createBy: string; //工号
    msgSource: number; //消息来源 100-人工 200-派单 201-配餐 202-到岗 203-复盘 300-系统
    readFlag: number; // 0: 未读, 1: 已读
    createTime: string;
    portrait?: string
    notice?: string
}

const props = defineProps({
    userType: {
        type: String,
        default: ''
    }
})
const { userType } = toRefs(props)
const tabList = ref([
    { text: '未读消息', value: '未读消息', isShowBadge: true, messageCount: 0 },
    { text: '已读消息', value: '已读消息' },
])
const curSelectTab = ref('未读消息')
const messageList = ref<MessageItem[]>([])

// 获取消息列表
const getMessageList = async () => {
    getMessageSummaryBySenderAPI({ readFlag: curSelectTab.value == '未读消息' ? 0 : 1 }).then((res: any) => {
        if (res.code == 200) {
            messageList.value = res.data
            if (curSelectTab.value == '未读消息') {
                tabList.value[0].messageCount = res.data.length
            }
        }
    })
}

//消息来源 100-人工 200-派单 201-配餐 202-到岗 203-复盘
// 获取消息图标
const getMessageIcon = (row: MessageItem) => {
    const iconMap: Record<number, string> = {
        200: new URL(`./assets/sendOrder.png`, import.meta.url).href,
        203: new URL(`./assets/debriefing.png`, import.meta.url).href, //复盘
        300: new URL(`./assets/system.png`, import.meta.url).href,
        201: new URL(`./assets/pc.png`, import.meta.url).href,
        202: new URL(`./assets/dg.png`, import.meta.url).href,
        100: new URL(`../../assets/defaultAvatar.png`, import.meta.url).href,

    }
    return iconMap[row.msgSource]
}

const RouterList: Record<number, { link: string; query?: Record<string, any> }> = {
    202: {
        link: `/dispatchSOP${userType.value}`,
        query: { type: 0 },
    },
    203: {
        link: `/dispatchSOP${userType.value}`,
        query: { type: 1 },
    },
    201: {
        link: `/dispatchSOP${userType.value}`,
        query: { type: 2 },
    },
    200: {
        link: '/tips',
    },
};
// 标记单条消息已读
const handleRead = async (item: MessageItem) => {
    if (item.msgType == 1) {
        router.push({ path: '/personalMessageList', query: { id: item.createBy } })
    } else {
        readMessageAPI({ msgSource: item.msgSource }).then((res: any) => {
            if (res.code == 200) {
                router.push({ path: RouterList[item.msgSource].link, query: RouterList[item.msgSource].query })

            }
        })

    }

}

// 查看消息详情
const reviewMessage = async (item: MessageItem) => {
    if (item.readFlag == 1) {
        router.push({
            path: '/personalMessageList',
            query: {
                id: item.createBy,
                readFlag: '已读消息',
                headerIsHide: 'true',
            }
        })
    }



}

// 切换标签
function changeSelectTab(params: any) {
    getMessageList()
}

onMounted(() => {
    getMessageList()
})
</script>

<style lang="scss" scoped src="./index.scss"></style>