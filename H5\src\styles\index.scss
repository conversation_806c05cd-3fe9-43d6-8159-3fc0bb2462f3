@import "./variables.scss";
@import "./common.scss";
@import "./transition.scss";
@import "./customButton.scss";
@import "./customVant.scss";


html {
  --blue: #007bff;
  --indigo: #6610f2;
  --purple: #6f42c1;
  --pink: #e83e8c;
  --red: #dc3545;
  --orange: #fd7e14;
  --yellow: #ffc107;
  --green: #28a745;
  --teal: #20c997;
  --cyan: #17a2b8;
  --white: #ffffff;
  --gray: #7e8299;
  --gray-dark: #3f4254;
  --primary: #0bb783;
  --secondary: #e4e6ef;
  --success: #1bc5bd;
  --info: #8950fc;
  --warning: #ffa800;
  --danger: #f64e60;
  --light: #f3f6f9;
  --dark: #181c32;
  --white: #ffffff;

}

// :root{
//   --van-floating-bubble-size: 40px;
//   --van-floating-bubble-icon-size:24px;
// }

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

.pink {
  background-color: pink !important;
}



label {
  font-weight: 700;
}

body::-webkit-scrollbar {
  display: none;
}

html {
  height: 100%;
  box-sizing: border-box;
}



// html {
//   // filter: progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);
//   // -webkit-filter: grayscale(100%);

//   // -webkit-filter: grayscale(100%);
//   // -moz-filter: grayscale(100%);
//   // -ms-filter: grayscale(100%);
//   // -o-filter: grayscale(100%);
//   // filter: grayscale(100%);
//   // filter: gray;
// }

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.clearfix::before,
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

//- ----------------------------------------------

//- ----------------------------------------------
@for $i from 1 through 36 {
  .fs#{$i} {
    font-size: #{$i}px;
  }
}

//- ----------------------------------------------

//- ----------------------------------------------

@for $i from 0 through 35 {
  // @each $item in top, right, bottom, left {
  //   .margin-#{$item}-#{$i} {
  //     margin-#{$item}: #{$i}px;
  //   }

  //   .padding-#{$item}-#{$i} {
  //     padding-#{$item}: #{$i}px;
  //   }
  // }

  .mt-#{$i} {
    margin-top: #{$i}px;
  }

  .mb-#{$i} {
    margin-bottom: #{$i}px;
  }

  .ml-#{$i} {
    margin-left: #{$i}px;
  }

  .mr-#{$i} {
    margin-right: #{$i}px;
  }

  .mx-#{$i} {
    margin-left: #{$i}px;
    margin-right: #{$i}px;
  }

  .py-#{$i} {
    padding-top: #{$i}px;
    padding-bottom: #{$i}px;
  }

  .px-#{$i} {
    padding-left: #{$i}px;
    padding-right: #{$i}px;
  }

  .pt-#{$i} {
    padding-top: #{$i}px;
  }

  .pb-#{$i} {
    padding-bottom: #{$i}px;
  }

  .pr-#{$i} {
    padding-right: #{$i}px;
  }

  .pl-#{$i} {
    padding-left: #{$i}px;
  }
}