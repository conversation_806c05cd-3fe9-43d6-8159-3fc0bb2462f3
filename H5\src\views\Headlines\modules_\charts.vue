<template>
    <div class=' container_ mt-6'>
        <div class="container__header">
            <div>
                <img src="@/assets/debriefingDetail.svg" alt="" srcset="">
                <span class="ml-6">{{ title }}</span>
            </div>

        </div>


        <div class="container_body ">
            <myCharts class="myEcharts" :options="options"></myCharts>
        </div>




    </div>
</template>

<script setup lang='ts'>
import myCharts from "@/components/charts/myCharts.vue";
import { getHeadlinesLineEchartsAPI } from "@/api/home";
const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    options: {
        type: Object,
        default: () => { }
    }
})


const { title, options } = toRefs(props)

</script>

<style lang="scss" scoped>
.myEcharts {
    width: 100%;
    height: 300px;
}
</style>