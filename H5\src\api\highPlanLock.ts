import request from '@/utils/request'
import configAxios from './config'

// 高套封闭详情数据接口
export interface HighPlanLockItem extends HighValueStatsResponse {
    name: string;
    areaId?: string
    areaL2Id?: string
    type: 0 | 1 | 2
    [key: string]: any;
}

/**
 * 高套封闭统计数据接口
 */
export interface HighValueStatsResponse {
    /** 员工姓名 */
    assStaffNm: string;
    /** 员工工号 */
    assStaffNumber: string;
    /** 日期 */
    day: string;
    /** 新装高套 */
    xzgt: number;
    /** 新装高套（折算） */
    xzgtzk: number;
    /** 新装高套完成率 */
    xzgtRate: number;
    /** 存量高套 */
    clgt: number;
    /** 存量高套（折算） */
    clgtzk: number;
    /** 存量高套完成率 */
    clgtRate: number;
    /** 高套拆机 */
    gtcj: number;
    /** 高套降档 */
    gtjd: number;
    /** 高套停机 */
    gttj: number;
    /** 月末日期 */
    monthEnd: string;
}

// 全区-高套指标
export function getHighValueAllCountAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/manager/highValue/allCount`,
        method: 'post',
        data
    });
}

// 区域列表-高套指标
export function getHighValueAreaCountListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/manager/highValue/areaCountList`,
        method: 'post',
        data
    });
}

// 分局列表-高套指标
export function getHighValueAreaL2CountListAPI(data = {}) {
    return request({
        url: `${configAxios.taskHubServer}/h5/manager/highValue/areaL2CountList`,
        method: 'post',
        data
    });
}

// 分局指标 /h5/highValue/areaL2Count

export function getHighValueManagerAreaL2CountAPI(data = {}) {
    return request({
        url: `${configAxios.taskHubServer}/h5/manager/highValue/staffList`,
        method: 'post',
        data
    });
}




export function getHighValueAreaL2CountAPI(data = {}) {
    return request({
        url: `${configAxios.taskHubServer}/h5/highValue/areaL2Count`,
        method: 'post',
        data
    });
}

// 分局指标-下钻到人员  
export function getHighValueAreaL2StaffListAPI(data = {}) {
    return request({
        url: `${configAxios.taskHubServer}/h5/highValue/staffList`,
        method: 'post',
        data
    });
}
// 个人指标
export function getHighValueStaffAPI(data = {}) {
    return request({
        url: `${configAxios.taskHubServer}/h5/highValue/staff`,
        method: 'post',
        data
    });
}
