<template>
    <div>
        <van-popup v-model:show="show" position="bottom" teleport="body" style="height: 100%;">
            <div class="box_content">
                <!-- 标题区域 -->
                <div class="date-range">2025年6月</div>
                <div class="close" @click="show = false">关闭</div>


                <!-- 龙虎榜区域 -->
                <div class="winners-section">
                    <img src="./assets//winners.png" alt="" srcset="" class="winners-bg">
                    <div class="section-header">
                        分局龙虎榜
                    </div>
                    <div class="winners-table">
                        <div class="table-header">
                            <span class="col-rank">排名</span>
                            <span class="col-unit">单元</span>
                            <span class="col-branch">分局</span>
                            <span class="col-type">类型</span>
                            <span class="col-score">得分</span>
                        </div>
                        <div class="table-body_">
                            <div v-for="(item, index) in winnersData" :key="index" class="table-row"
                                :class="index < 3 ? 'type-gov' : ''">
                                <div class="col-rank">
                                    <img v-if="index === 0" src="./assets/TOP1.svg" alt="第一名" class="rank-icon" />
                                    <img v-else-if="index === 1" src="./assets/TOP2.svg" alt="第二名" class="rank-icon" />
                                    <img v-else-if="index === 2" src="./assets/TOP3.svg" alt="第三名" class="rank-icon" />
                                    <!-- <span v-else class="rank-number">{{ index + 1 }}</span> -->
                                </div>
                                <div class="col-unit">{{ item.areaName }}</div>
                                <div class="col-branch">{{ item.areaL2Name }}</div>
                                <div class="col-type">{{ item.areaL2Type }}</div>
                                <div class="col-score">{{ item.score }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 潜力榜区域 -->
                <div class="potential-section">
                    <img src="./assets/potential.png" alt="潜力榜背景" class="potential-bg" />
                    <div class="section-header">
                        分局潜力榜
                    </div>
                    <div class="potential-table">
                        <div class="table-header">
                            <span class="col-unit">单元</span>
                            <span class="col-branch">分局</span>
                            <span class="col-type">类型</span>
                            <span class="col-score">得分</span>
                        </div>
                        <div class="table-body_">
                            <div v-for="(item, index) in potentialData" :key="index" class="table-row">
                                <div class="col-unit">{{ item.areaName }}</div>
                                <div class="col-branch">{{ item.areaL2Name }}</div>
                                <div class="col-type">
                                    {{ item.areaL2Type }}</div>
                                <div class="col-score">{{ item.score }}</div>
                            </div>
                        </div>
                    </div>

                </div>

                <img src="./assets/ribbon.svg" alt="" class="ribbon">
            </div>
        </van-popup>
    </div>
</template>

<script setup lang="ts">
import { queryDragonTigerRankingAPI } from "@/api/common";
import { ref } from 'vue'
import { useUserInfo } from "@/stores/userInfo";
interface RankingData {
    areaL2Name: string; // 分局名称
    areaL2Type: string; // 分局类型
    areaName: string;   // 区域名称
    periodId: number | null; // 期间ID
    rank: number;       // 排名
    rankingId: number | null; // 排名ID
    rankingType: string; // 排名类型
    score: string;      // 得分
}

const props = defineProps({
    // 可以添加其他props
})

const userInfoStore = useUserInfo()
const emit = defineEmits([''])

const show = defineModel('show', {
    type: Boolean,
    default: false
})

// 龙虎榜数据
const winnersData = ref<RankingData[]>([

])

// 潜力榜数据
const potentialData = ref<RankingData[]>([

])
function loadwinnersList() {
    queryDragonTigerRankingAPI({ rankType: '龙虎榜' }).then((res: any) => {
        if (res.code) {
            winnersData.value = res.data
        }
    })
}

function loadpotentialList() {
    queryDragonTigerRankingAPI({ rankType: '潜力榜' }).then((res: any) => {
        if (res.code == 200) {
            potentialData.value = res.data
        }

    })
}




onMounted(() => {
    loadwinnersList()
    loadpotentialList()
})

</script>

<style lang="scss" scoped>
.box_content {
    background: url(./assets/winnersBg.svg);
    background-size: 100%;
    background-repeat: no-repeat;
    width: 100%;
    height: 100%;
    min-height: 100%;
    padding: 20px;
    box-sizing: border-box;
    overflow-y: auto;
    position: relative;
}

// 标题区域
.date-range {
    background: url('./assets/dateRange.svg');
    background-size: 100%;
    background-repeat: no-repeat;
    position: fixed;
    top: 11%;
    left: 8%;
    text-align: center;
    height: 30px;
    width: 174px;
    line-height: 30px;
    font-weight: 500;
    font-size: 16px;
    color: #946532;
    letter-spacing: 2px;
}

.ribbon {
    position: fixed;
    bottom: -3px;
    left: 0px;
}

.close {
    position: fixed;
    z-index: 999;
    right: 15px;
    top: 15px;
    background-color: #252422e6;
    padding: 5px 12px;
    border-radius: 10px;
    color: #fff;
    font-size: 12px;
    line-height: 15px;
}

// 龙虎榜区域
.winners-section {
    position: relative;
    // top: 15%;
    margin-top: 35%;
    left: 0px;
    min-height: 350px;
    padding: 15px 10px;
    padding-bottom: 30px;

    .winners-bg {
        left: 0px;
        top: 0px;
        position: absolute;
        width: 100%;
        height: 100%;
    }

    .section-header {
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;
        font-weight: 500;
        font-size: 18px;
        color: #946532;
        top: -4px;
        letter-spacing: 8px;

    }

    .winners-table {
        position: relative;
        top: 20px;


        .table-header {
            background: #FBEBCE;
            border-radius: 33px;
            color: #A27338;
            display: flex;
            padding: 12px 0;
            font-weight: bold;
            font-size: 14px;

            .col-rank {
                width: 10%;
                text-align: center;
            }

            .col-unit {
                width: 20%;
                text-align: center;
            }

            .col-branch {
                width: 35%;
                text-align: center;
            }

            .col-type {
                width: 17%;
                text-align: center;
            }

            .col-score {
                width: 18%;
                text-align: center;
            }
        }

        .table-body_ {
            margin-top: 6px;

            .table-row {
                display: flex;
                padding: 10px 0;
                border-bottom: 1px solid #f0f0f0;
                align-items: center;
                font-size: 13px;

                &:last-child {
                    border-bottom: none;
                }





                .col-rank {
                    width: 10%;
                    text-align: center;

                    .rank-icon {
                        width: 15px;
                    }

                    .rank-number {
                        font-weight: bold;
                        color: #715026;
                    }
                }

                .col-unit {
                    width: 20%;
                    text-align: center;
                   
                    color: #715026;
                }

                .col-branch {
                    width: 35%;
                    text-align: center;
                    color: #715026;
                    font-size: 12px;
                }

                .col-type {
                    width: 17%;
                    text-align: center;
                    color: #715026;
                }

                .col-score {
                    width: 18%;
                    text-align: center;
                    color: #715026;
                }
            }



            .type-gov>* {
                color: #E74C3C !important;
                font-weight: bold !important;
            }



            .table-row:nth-child(odd) {
                background-color: #FFF6E7 !important;
            }
        }
    }
}

// 潜力榜区域
.potential-section {
    position: relative;
    margin-top: 15px;
    left: 0px;
    /* 移除固定最小高度，让内容自动撑开 */
    min-height: 350px;
    padding: 15px 10px;
    padding-bottom: 30px;


    .potential-bg {
        left: 0px;
        top: 0px;
        position: absolute;
        width: 100%;
        height: 100%;
    }



    .section-header {
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        text-align: center;
        font-weight: 500;
        font-size: 18px;
        color: #976834;
        top: -10px;
        letter-spacing: 8px;
    }

    .potential-table {
        position: relative;
        top: 13px;

        .table-header {
            background: #C8DEFF;
            border-radius: 33px;
            color: #A27338;
            display: flex;
            padding: 12px 0;
            font-weight: bold;
            font-size: 14px;

            .col-unit {
                width: 20%;
                text-align: center;
            }

            .col-branch {
                width: 40%;
                text-align: center;
            }

            .col-type {
                width: 20%;
                text-align: center;
            }

            .col-score {
                width: 20%;
                text-align: center;
            }
        }

        .table-body_ {
            /* 移除高度限制，让内容自动撑开 */
            /* max-height: 200px; */
            /* overflow-y: auto; */
            margin-top: 6px;

            .table-row {
                display: flex;
                padding: 10px 0;
                border-bottom: 1px solid #f0f0f0;
                align-items: center;
                font-size: 13px;

                &:last-child {
                    border-bottom: none;
                }

                .col-unit {
                    width: 20%;
                    text-align: center;
                   
                    color: #A27338;
                }

                .col-branch {
                    width: 40%;
                    text-align: center;
                    color: #A27338;
                    font-size: 12px;
                }

                .col-type {
                    width: 20%;
                    text-align: center;
                    color: #A27338;
                }

                .col-score {
                    width: 20%;
                    text-align: center;
                    color: #A27338;
                }
            }

            .table-row:nth-child(odd) {
                background-color: #E9F2FF !important;
            }
        }
    }


}

// 响应式设计
@media (max-width: 480px) {
    .box_content {
        padding: 15px;
    }

    .title-section {
        .title-wrapper {
            .title-icon {
                width: 160px;
            }

            .date-range {
                font-size: 12px;
                padding: 4px 12px;
            }
        }


    }

    .winners-table,
    .potential-table {

        .table-header,
        .table-row {
            font-size: 12px;
            padding: 8px 0;
        }

        .table-body .table-row .col-branch {
            font-size: 11px;
        }
    }
}
</style>