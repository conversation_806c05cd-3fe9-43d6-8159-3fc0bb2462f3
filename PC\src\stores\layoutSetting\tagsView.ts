import { defineStore } from "pinia";
interface View {
  path: string;
  name: string;
  meta: {
    title?: string;
    noCache?: boolean;
    affix?: boolean;
  };
}

interface ViewsState {
  visitedViews: View[];
  cachedViews: string[];
}
export const useViewsStore = defineStore({
  id: "views",
  state: (): ViewsState => ({
    visitedViews: [],
    cachedViews: [],
  }),
  actions: {
    addVisitedView(view: View) {
      if (this.visitedViews.some((v) => v.path === view.path)) return;
      this.visitedViews.push({
        ...view,
        meta: {
          ...view.meta,
          title: view.meta.title || "no-name",
        },
      });
    },
    addCachedView(view: View) {
      if (this.cachedViews.includes(view.name)) return;
      if (!view.meta.noCache) {
        this.cachedViews.push(view.name);
      }
    },
    delVisitedView(view: View) {
      this.visitedViews = this.visitedViews.filter((v) => v.path !== view.path);
    },
    delCachedView(view: View) {
      const index = this.cachedViews.indexOf(view.name);
      if (index > -1) this.cachedViews.splice(index, 1);
    },
    delOthersVisitedViews(view: View) {
      this.visitedViews = this.visitedViews.filter(
        (item) => {
          return item.meta.affix || item.path === view.path
        }
      );
    },
    delOthersCachedViews(view: View) {
      const index = this.cachedViews.indexOf(view.name);
      if (index > -1) {
        this.cachedViews = this.cachedViews.slice(index, index + 1);
      } else {
        this.cachedViews = [];
      }
    },
    delAllVisitedViews() {
      const affixTags = this.visitedViews.filter((tag) => tag.meta.affix);
      this.visitedViews = affixTags;
    },
    delAllCachedViews() {
      this.cachedViews = [];
    },
    updateVisitedView(view: View) {
      for (let v of this.visitedViews) {
        if (v.path === view.path) {
          Object.assign(v, view);
          break;
        }
      }
    },
    addView(view: View) {
      this.addVisitedView(view);
      this.addCachedView(view);
    },
    delView(view: View) {
      return new Promise<{ visitedViews: View[]; cachedViews: string[] }>(
        (resolve) => {
          this.delVisitedView(view);
          this.delCachedView(view);
          resolve({
            visitedViews: [...this.visitedViews],
            cachedViews: [...this.cachedViews],
          });
        }
      );
    },
    delOthersViews(view: View) {
      return new Promise<{ visitedViews: View[]; cachedViews: string[] }>(
        (resolve) => {
          this.delOthersVisitedViews(view);
          this.delOthersCachedViews(view);
          resolve({
            visitedViews: [...this.visitedViews],
            cachedViews: [...this.cachedViews],
          });
        }
      );
    },
    delAllViews() {
      return new Promise<{ visitedViews: View[]; cachedViews: string[] }>(
        (resolve) => {
          this.delAllVisitedViews();
          this.delAllCachedViews();
          resolve({
            visitedViews: [...this.visitedViews],
            cachedViews: [...this.cachedViews],
          });
        }
      );
    },
  },
});
