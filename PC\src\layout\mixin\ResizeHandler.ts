import { useSidebarStore } from "@/stores/layoutSetting/app.ts";

import { useRouter } from "vue-router";
import {
  ref,
  toRefs,
  watch,
  onBeforeMount,
  onBeforeUnmount,
  onMounted,
  shallowReactive,
  Ref,
} from "vue";

interface Params {
  device: Ref<string>;
  sidebar: Ref<{
    opened: boolean;
    withoutAnimation?: boolean;
  }>;
}

export default function useResponsive(params: Params) {
  const SidebarState = useSidebarStore();
  const { device, sidebar } = params;
  const { body } = document;
  const WIDTH = 992;
  const router = useRouter();

  watch(
    () => router,
    (router) => {
      if (device.value === "mobile" && sidebar.value.opened) {
        SidebarState.closeSidebar(false);
      }
    }
  );

  function isMobile() {
    const rect = body.getBoundingClientRect();
    return rect.width - 1 < WIDTH;
  }

  function resizeHandler() {
    if (!document.hidden) {
      const mobile = isMobile();
      SidebarState.toggleDevice(mobile ? "mobile" : "desktop");

      if (mobile) {
        SidebarState.closeSidebar(true);
      }
    }
  }

  onBeforeMount(() => {
    window.addEventListener("resize", resizeHandler);
  });

  onBeforeUnmount(() => {
    window.removeEventListener("resize", resizeHandler);
  });

  onMounted(() => {
    const mobile = isMobile();
    if (mobile) {
      SidebarState.toggleDevice("mobile");
      SidebarState.closeSidebar(true);
    }
  });

  return {};
}
