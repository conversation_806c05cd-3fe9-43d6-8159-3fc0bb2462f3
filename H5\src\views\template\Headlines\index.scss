.Headlines {


  .header-right {
    font-family: Source <PERSON>, Source <PERSON>;
    font-weight: 400;
    font-size: 12px;
    color: #1A76FF;

    img {
      height: 12px;
      width: 12px;
      margin-right: 3px;

    }
  }

  .halvingLine {
    width: 2px;
    height: 100%;
    background: #DEEDFF;
    margin: 0 auto;

  }


  .container_body {
    padding-top: 0px;

  }

  .growth-header {
    display: flex;
    padding-top: 0px;
    padding-bottom: 0px;

    h3 {
      font-family: 'Source <PERSON>, Source Han San<PERSON>';
      font-weight: 500;
      font-size: 13px;
      color: #3D3D3D;
      margin-bottom: 11px;
      padding: 0px;
    }
  }

  .growth-row {
    display: flex;
    align-items: stretch;



    .growth-item {
      display: flex;
      justify-content: space-between;
      row-gap: 10px;
      align-items: center;
      flex-wrap: wrap;

      height: auto;
      /* 只在内容区域应用背景 */
      background-color: #EDF5FF; // 每个项的背景色
      padding: 7px 10px;
      margin-bottom: 10px;
      border-radius: 5px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);



      .growth_title {

        font-family: 'Source <PERSON>, Source Han <PERSON>';
        font-weight: 500;
        font-size: 12px;
        color: #3D3D3D;

      }

      .score {
        color: #3d9df6; // 分数的颜色
        font-weight: bold;
        font-size: 12px;
      }

      .member {
        color: #00AFA3;
      }

      .highlight {
        color: #f55; // 特殊的分数颜色
      }
    }
  }


  @for $i from 1 through 24 {

    .w_#{$i} {
      flex: 0 0 calc(#{$i} / 24 * 100%);
      max-width: calc(#{$i} / 24 * 100%);
    }
  }


}