/**
 * 创建一个节流函数，在 `wait` 毫秒内最多只能调用 `func` 一次。如果 `leading` 为 `true`，
 * 则在节流开始时立即调用 `func`；如果 `trailing` 为 `true`，则在节流结束后再调用一次。
 *
 * @param {Function} func - 需要节流处理的函数。
 * @param {number} wait - 等待时间，单位为毫秒。
 * @param {Object} [options={}] - 可选配置项。
 * @param {boolean} [options.leading=true] - 如果为 `true`，在节流开始时立即调用函数。
 * @param {boolean} [options.trailing=true] - 如果为 `true`，在节流结束后调用一次函数。
 * @returns {Function} 返回一个新的节流函数，该函数带有 `cancel` 和 `flush` 方法。
 *
 * @example
 * // 节流处理滚动事件，每 200 毫秒触发一次回调
 * function onScroll() {
 *   console.log('Scrolled!');
 * }
 * const throttledScroll = throttle(onScroll, 200);
 * window.addEventListener('scroll', throttledScroll);
 *
 * @example
 * // 创建一个节流函数，在节流开始时立即执行
 * const throttledFunc = throttle(() => {
 *   console.log('Executed!');
 * }, 200, { leading: true, trailing: false });
 * throttledFunc();
 *
 * @example
 * // 取消节流操作
 * const throttledFunc = throttle(onScroll, 200);
 * throttledFunc.cancel();
 *
 * @example
 * // 强制立即执行节流函数
 * throttledFunc.flush();
 */
function throttle<T extends (...args: any[]) => any>(
    func: T,
    wait: number,
    options: { leading?: boolean; trailing?: boolean } = {}
): T & { cancel: () => void; flush: () => void } {
    let timeout: ReturnType<typeof setTimeout> | null;
    let context: any, args: any[] | null, result: any;
    let previous = 0;
    if (!options.leading) previous = Date.now();

    const later = function () {
        previous = options.leading === false ? 0 : Date.now();
        timeout = null;
        result = func.apply(context, args as any[]);
        if (!timeout) context = args = null;
    };

    const throttled = function (this: any, ...argsInput: any[]) {
        const now = Date.now();
        if (!previous && options.leading === false) previous = now;
        const remaining = wait - (now - previous);
        context = this;
        args = argsInput;

        if (remaining <= 0 || remaining > wait) {
            if (timeout) {
                clearTimeout(timeout);
                timeout = null;
            }
            previous = now;
            result = func.apply(context, args);
            if (!timeout) context = args = null;
        } else if (!timeout && options.trailing !== false) {
            timeout = setTimeout(later, remaining);
        }
        return result;
    } as T & { cancel: () => void; flush: () => void };

    /**
     * 取消当前等待的节流操作（如果有）。
     */
    throttled.cancel = function () {
        if (timeout) clearTimeout(timeout);
        previous = 0;
        timeout = context = args = null;
    };

    /**
     * 立即执行节流函数。
     */
    throttled.flush = function () {
        if (timeout) {
            result = func.apply(context, args as any[]);
            clearTimeout(timeout);
            timeout = context = args = null;
        }
    };

    return throttled;
}
