<template>
    <div>
        <div class="card-header">
            <div class="selectOption-item" @click="calendarIsShow = true">
                <span>{{ time ? time : '请选择时间' }}</span>
                <van-icon name="play" class="play" color="#1A76FF" />
            </div>
            <van-search v-model="searchName" :show-action="false" placeholder="网格名称/ID">

                <template #left-icon>
                    <img class="mt-5" src="@/assets/search.svg" alt="" srcset="">

                </template>
            </van-search>
        </div>
        <div class=' container_ mt-6'>
            <div class="container__header">
                <div>
                    <img src="@/assets/computed.png" alt="" srcset="">
                    <span class="ml-6">{{ route.query.areaNameL2 }}宣传检查</span>
                </div>

            </div>
            <div class="table-container pb-10">

                <table>
                    <thead>

                        <tr>
                            <th class="sticky">网格名称</th>
                            <th>网格属性</th>
                            <th>点检状态</th>
                            <th>图片张数</th>
                            <th>宣传评价</th>
                            <th>备注</th>
                            <th>详情</th>
                        </tr>

                    </thead>
                    <tbody>


                        <tr v-for="(row, index) in tableData_" :key="row.id">
                            <td class="sticky">
                                <div class="store ">{{ row.gridName }}</div>
                            </td>


                            <td>{{ row.gridProperty }}</td>

                            <td class="text-primary" v-if="row.status == 1">已点检</td>
                            <td class="text-warning" v-else>未点检</td>


                            <td>{{ row.images.length }}</td>

                            <td>
                                <div class="py-5 px-1 fs12" style="border-radius: 5px;"
                                    :class="getItemClass(row.publicityEvaluation)">
                                    {{ curRowPublicityEvaluation(row.publicityEvaluation) ?? '未评价' }}
                                </div>

                            </td>
                            <td>

                                <div :class="row.remark ? 'store' : ''">
                                    {{ row.remark }}
                                </div>



                            </td>

                            <td class="detail">
                                <div @click="gotoDetail(row)" v-if="row.status == 1">查看</div>
                            </td>

                        </tr>


                    </tbody>
                </table>
            </div>
            <van-empty description="暂无数据" class="mt-8" style="background-color: #fff;border-radius: 15px;"
                v-if="tableData_.length == 0" />
        </div>

        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />
    </div>
</template>

<script setup lang="ts">
import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { getDictInfoAPI } from "@/api/common";
import { getPromoShotsListAPI, getManagementGridDetailAPI } from "@/api/promoShots";
import { useRoute, useRouter } from "vue-router";
import { formatDate } from "@/utils/loadTime";
const route = useRoute()
const router = useRouter()
interface List {
    id: string; //主键ID
    areaIdL2: string; // 二级区域 ID
    areaNameL2: string; // 二级区域名称
    createTime: string; // 创建时间，格式为 "YYYY-MM-DD HH:mm:ss.S"
    description: string; // 详细描述，如具体地址或位置
    gridId: string; // 网格 ID
    gridName: string; // 网格名称
    gridTypeName: string | null; // 网格类型名称，可能为 null
    images: string; // 相关图片的 URL
    status: number; //点检状态
    gridProperty: string
    publicityEvaluation: string,
    remark: string
}

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    onConfirm,
} = searchHooks({
    source: 'inspectionLogMangement',
    callback: loadStorePackageList
})

const areaIdL2 = computed(() => {
    return route.query.areaIdL2
})
const searchName = ref('')


const tableData = ref<List[]>([]);

const tableData_ = computed<List[]>(() => {
    const input = searchName.value.toLowerCase();

    return tableData.value.filter((item: List) => {
        const gridName = item.gridName ? item.gridName.toLowerCase() : '';
        const gridId = item.gridId ? item.gridId.toLowerCase() : '';
        return (
            gridName.includes(input) || gridId.includes(input)
        );
    });
})


const time = computed(() => {
    if (formline.startTime && formline.endTime) return `${formline.startTime} - ${formline.endTime}`
    else return ``
})

function loadStorePackageList() {
    let params = {
        startTime: formline.startTime ? `${formline.startTime} 00:00:00` : '',
        endTime: formline.endTime ? `${formline.endTime} 23:59:59` : '',
        areaIdL2: areaIdL2.value
    }
    getManagementGridDetailAPI(params).then((res: any) => {
        if (res.code == 200) {
            // 使用 reduce 方法根据 gridId 分组
            // console.log(res);

            const groupedData = res.data.reduce((acc: any, item: any) => {
                // 如果 accumulator 中没有该 gridId，初始化一个新的对象
                if (!acc[item.gridId]) {
                    acc[item.gridId] = {
                        ...item,
                        images: item.images ? item.images.split(',') : [],
                        createTime: item.createTime ? formatDate(item.createTime, 'yyyy-MM-dd hh:mm') : '',
                        photoCount: 0 // 初始化照片数量
                    };
                } else {
                    // 如果该 gridId 已经存在，则合并图片并更新照片数量
                    const existingItem = acc[item.gridId];
                    existingItem.images = [...existingItem.images, ...(item.images ?? '').split(',')];
                    existingItem.photoCount += item.images ? item.images.split(',').length : 0;
                }

                return acc;
            }, {});


            // 将合并后的数据转换回数组
            tableData.value = Object.values(groupedData);

        }
    })
}


function gotoDetail(row: List) {
    router.push({
        path: '/inspectionLogMangementDetail',
        query: {
            areaIdL2: route.query.areaIdL2,
            gridId: row.gridId,
            gridName: row.gridName,
            startTime: formline.startTime,
            endTime: formline.endTime
        }
    })
}

function getItemClass(value: string) {
    switch (value) {
        case '1':
            return 'active-green '
        case '2':
            return 'active-light-green'
        case '3':
            return 'active-yellow'
        case '4':
            return 'active-red'
        case '5':
            return 'active-blue'
        default:
            return 'active-default'
    }
}


const curRowPublicityEvaluation = (publicityEvaluation: string) => {
    return tabList.value.find(item => item.value == publicityEvaluation)?.text
}

async function initDictInfo() {
    await getDictInfoAPI('promotion_evaluation').then((res: any) => {
        if (res.code == 200) {
            tabList.value = res.data.map((item: any) => ({ text: item.dictName, value: item.dictCode }))
        }

    })
}

const tabList = ref<{ text: string, value: string }[]>([])


onMounted(() => {
    loadStorePackageList()
    initDictInfo()
})
</script>

<style lang="scss" scoped>
.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

    .selectOption-item {
        flex: 1 0 auto;
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;
        // justify-content: space-between;
        position: relative;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
            position: absolute;
            right: 10px;
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }
}


.table-container {
    width: 100%;
    overflow-x: auto;
    // padding: 8px;

    table {
        width: 100%;
        border-collapse: collapse;
        text-align: center;
        font-size: 13px;

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            white-space: nowrap; // 禁止换行
            // white-space: normal;
            /* 允许换行 */
        }

        .sticky {
            /* 固定表格内容的门店列 */
            position: sticky;
            left: 0;
            background-color: #fff;
            /* 设置背景颜色以覆盖其他列 */
            z-index: 1;
            /* 确保门店列在最上层 */
        }


        .store {
            width: 120px;
            max-width: 120px;
            white-space: normal;
        }

        .detail {
            color: #1A76FF;
            text-decoration: underline;
            text-decoration-thickness: 1px;
            /* 下划线的厚度 */
            text-underline-offset: 2px;
            /* 下划线与文字的距离 */
        }

        .main-title {
            font-size: 13px;
            font-weight: bold;
            padding: 12px;
            background-color: #2e70ba;
            color: #fff;
            text-align: center;
        }

        thead tr:nth-child(1) th {
            background-color: #2e70ba;
            color: #fff;
            font-weight: bold;
            padding: 10px;
            text-align: center;
        }

        thead tr:nth-child(2) th {
            background-color: #2e70ba;
            color: #fff;
            font-weight: bold;
            padding: 8px;
        }

        tbody tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        tbody tr:hover {
            background-color: #e6f7ff;

            .sticky {
                background-color: #e6f7ff !important;

            }
        }

        tbody td.highlight {
            background-color: #ffe58f;
            font-weight: bold;

        }

    }
}




/* 选中状态颜色定义 */
.active-green {
    background-color: #4caf50;
    color: #ffffff;
    border-color: #388e3c;
}

.active-light-green {
    background-color: #91cc75b5;
    color: #1b5e20;
    border-color: #81c784;
}

.active-yellow {
    background-color: #FAC857;
    color: #ffffff;
    border-color: #fbc02d;
}

.active-red {
    background-color: #EE6666;
    color: #ffffff;
    border-color: #d32f2f;
}

.active-blue {
    background-color: #2196f3;
    color: #ffffff;
    border-color: #1976d2;
}

.active-default {
    background-color: #a8a8a8;
    color: #fbfcff;
    border-color: #8f8f8f;
}

:deep(.van-search) {
    background-color: transparent;
    padding: 0px;
    flex: 0 0 140px;
    margin-left: 5px;
}

:deep(.van-search__action) {
    line-height: 0;
}

:deep(.van-search__content) {
    background-color: #EDF5FF;
    border-radius: 5px;


}



:deep(.van-icon-search) {
    color: #1a76ff;

}


:deep(.van-field__control) {
    font-weight: 400;
    font-size: 14px;
    color: #72ABFF;
}

:deep(.van-field__control::placeholder) {
    color: #72ABFF;
}

:deep(.van-popover__wrapper) {
    flex: 1 0 auto;
}

:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
    width: auto;
}
</style>