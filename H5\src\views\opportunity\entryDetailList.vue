<template>
    <div class="card container_">
        <div class="container__header">
            <div>
                <!-- <img src="../../template/MyDay/assets/images/dkrz.png" alt="" srcset="" /> -->
                <span class="ml-6">基础商机录入详情</span>
            </div>
        </div>
        <van-search class="custom-van-search" background="#FBFCFF" v-model="searchName"
            placeholder="请选择查询对象"></van-search>

        <van-list v-model:loading="loading" :finished="finished" :finished-text="tableData.length > 0 ? '没有更多了' : ''"
            error-text="请求失败，点击重新加载" @load="onLoad">
            <div class="content" v-for="(item, index) in tableData" :key="item.seq">
                <div class="font_row">
                    <div class="font font_label">填报时间</div>
                    <div class="font font_date ">{{ item.statDayId }}</div>
                </div>
                <div class="font_row">
                    <div class="font font_label">填报类型</div>
                    <div class="font font_title">{{ item.tsType }}</div>
                </div>
                <div class="font_row">
                    <div class="font font_label">客户类型</div>
                    <div class="font font_title">{{ item.custType }}</div>
                </div>
                <div class="font_row" v-if="item.custType === '存量'">
                    <div class="font font_label">关联号码</div>
                    <div class="font font_title">{{ item.accsNbr }}</div>
                </div>
                <div class="font_row">
                    <div class="font font_label">客户名称</div>
                    <div class="font font_title">{{ item.custName }}</div>
                </div>
                <div class="font_row">
                    <div class="font font_label">商机阶段</div>
                    <div class="font font_title">{{ item.sjState }}</div>
                </div>
                <div class="font_row">
                    <div class="font font_label">商机描述</div>
                    <div class="font font_title">
                        <van-text-ellipsis rows="2" :content="item.busiRemark" expand-text="展开" collapse-text="收起" />
                    </div>
                </div>

                <van-collapse v-model="item.activeNames">
                    <van-collapse-item :title="busi.busiType" :name="`${busi.busiType}-${busiIndex}`"
                        v-for="(busi, busiIndex) in item.infoList" :key="`${busi.busiType}-${busiIndex}`">
                        <div class="font_row">
                            <div class="font font_label">商机类型</div>
                            <div class="font font_title">{{ busi.busiType }}</div>
                        </div>
                        <div class="font_row">
                            <div class="font font_label">数量</div>
                            <div class="font font_title">{{ busi.busiCnt }}</div>
                        </div>
                        <div class="font_row" v-if="busi.vpnAccsNbr">
                            <div class="font font_label">V网参照号</div>
                            <div class="font font_title">{{ busi.vpnAccsNbr }}</div>
                        </div>
                        <div class="font_row" v-if="busi.speed">
                            <div class="font font_label">速率</div>
                            <div class="font font_title">{{ busi.speed }}M</div>
                        </div>
                        <div class="font_row" v-if="busi.busiSpec">
                            <div class="font font_label">分类</div>
                            <div class="font font_title">{{ busi.busiSpec }}</div>
                        </div>
                        <div class="font_row" v-if="busi.ifChange">
                            <div class="font font_label">是否转化</div>
                            <div class="font font_title">{{ busi.ifChange }}</div>
                        </div>
                        <div class="font_row" v-if="busi.ifLsms">
                            <div class="font font_label">异网策反</div>
                            <div class="font font_title">{{ busi.ifLsms }}</div>
                        </div>
                        <div class="font_row" v-if="busi.bipAccsNbr">
                            <div class="font font_label">接入号</div>
                            <div class="font font_title">{{ busi.bipAccsNbr }}</div>
                        </div>
                        <div class="font_row" v-if="busi.cntChange">
                            <div class="font font_label">转化数量</div>
                            <div class="font font_title">{{ busi.cntChange }}</div>
                        </div>
                        <div class="font_row" v-if="busi.city">
                            <div class="font font_label">方向</div>
                            <div class="font font_title">{{ busi.city }}</div>
                        </div>
                        <div class="font_row" v-if="busi.limitMonChrg">
                            <div class="font font_label">月租</div>
                            <div class="font font_title">{{ busi.limitMonChrg }}元</div>
                        </div>
                        <div class="font_row" v-if="busi.ghAdd">
                            <div class="font font_label">固话叠加</div>
                            <div class="font font_title">{{ busi.ghAdd }}</div>
                        </div>
                    </van-collapse-item>
                </van-collapse>

            </div>
        </van-list>
        <van-empty description="暂无数据" v-if="tableData.length == 0" />
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getZqBusiOpportunityListAPI } from "@/api/opportunity";
import { debounce } from "lodash-es";
type TableData = {

    createTime: string,
    tsType: string,           // 填报类型
    custType: string,         // 客户类型
    accsNbr?: string,          // 关联号码
    custName: string,         // 客户名称
    busiRemark: string,       // 商机描述
    sjState: string,          // 商机阶段
    activeNames: string[],
    infoList: Array<{
        busiType: string;      // 商机类型
        busiCnt: string;       // 商机数量
        vpnAccsNbr: string;    // VPN专线号
        speed: string;         // 速率
        busiSpec: string;      // 业务规格/分类
        ifChange: string;      // 是否转化
        ifLsms: string;        // 异网策反
        bipAccsNbr: string;    // 接入号
        cntChange: number;     // 转化数量
        city: string;          // 方向
        limitMonChrg: number;  // 月租费用(元)
        ghAdd: string;         // 固话叠加
    }>,
    [key: string]: any
}

const tableData = ref<TableData[]>([]);
const searchName = ref<string>('');
const loading = ref<boolean>(false)
const finished = ref<boolean>(true)

const pageSize = ref<number>(10);
const total = ref<number>(0);
const pageNum = ref<number>(1);


function loadList() {
    let params: { [key: string]: any } = {
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        custName: searchName.value,
    };

    const toast = showLoadingToast({
        message: '加载中...',
        forbidClick: true,
        loadingType: 'spinner',
        duration: 0,
        overlay: true,
        teleport: '.main-page',
    });
    loading.value = true
    getZqBusiOpportunityListAPI(params).then((res: any) => {
        if (res.code == 200) {
            for (const item of res.data.records || []) {
                item.activeNames = []
            }

            tableData.value = [...tableData.value, ...res.data.records]
            total.value = res.data.total
            if (total.value <= tableData.value.length) {
                finished.value = true
            } else {
                finished.value = false
            }
        }


    }).finally(() => {
        loading.value = false;
        toast.close()
    })


}

watch(() => searchName.value, debounce((newvalue) => {
    pageNum.value = 1
    tableData.value = []
    loadList()
}, 600))

function onLoad() {
    pageNum.value = pageNum.value + 1
    loadList()
}


onMounted(() => {
    loadList()

});
</script>

<style lang="scss" scoped>
.card {
    min-height: 100%;
    background: #fbfcff;
    // overflow-x: hidden;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px 10px 0px 0px;
}

.content {
    background: #EDF5FF;
    border-radius: 10px 10px 10px 10px;
    margin: 13px 15px;


}

.font_row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 18px
}

.font_title {
    font-weight: 400;
    font-size: 14px;
    color: #A8A8A8;
}

.font_value {
    text-align: right;
}

.font {
    font-family: Source Han Sans, Source Han Sans;
    font-style: normal;
    text-transform: none;
}

.font_date {
    font-size: 12px;
    line-height: 20px;
    color: #1a76ff;

    font-weight: 500;
}


.font_label {
    flex: 0 0 auto;
    padding: 15px 18px;
    font-weight: 500;
    font-size: 14px;
    color: #3D3D3D;
    line-height: 16px;
}


.font_value {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    place-items: center;
    padding: 15px 18px;
    padding-top: 0px;

    img {
        border-radius: 5px;
    }
}

:deep(.van-cell) {
    background-color: transparent !important;
}

:deep(.van-collapse-item__content) {
    background-color: transparent !important;
}
</style>