<template>
    <div class="card container_">
        <div class="container__header">
            <div class="ml-6 header">
                <span>我的商机</span>
                <div class="checkbox-wrapper-10  ml-4" style="display: flex;align-items: center;"
                    v-if="userInfoStore.userInfo.orgDept == UserType.Enterprise">
                    <input :checked="isGov" type="checkbox" id="cb5" @change="isGov = !isGov" class="tgl tgl-flip">
                    <label for="cb5" data-tg-on="政企" data-tg-off="公众" class="tgl-btn"></label>

                    <i class="icon-shouzhixuanzhong-copy left mb-4" style="color: #1A76FF;"></i>
                </div>
            </div>
            <div class="selectOption">
                <van-popover v-model:show="showPopover" :actions="actions" @select="onSelect" placement="bottom-end">
                    <template #reference>
                        <div class="selectOption-item">
                            <span>{{ curSelectTimeTime }}</span>
                            <van-icon name="play" class="play" color="#1A76FF" />
                        </div>
                    </template>
                </van-popover>

            </div>
        </div>
        <div class="container_body card_body">
            <div class="option">
                <div class="option-item" v-for="item, index in optionList" :key="index">
                    <div class="value" :style="{ color: item.color }">{{ myTableData[item.props] }}{{
                        item.props == 'dispatchrate' ? '%' : '' }}</div>
                    <div class="text">{{ item.text }}</div>
                </div>
            </div>

        </div>




        <div class="container__header mt-10">
            <div>
                <span class="ml-6" style="font-weight: 500;">商机录入</span>
            </div>
        </div>
        <div class="container_body card_body">
            <div class="commonOption">
                <div class=" commonOption-item" @click="goTo('普通基础商机')">
                    <img src="../assets/entering.svg" alt="" srcset="">
                    <div>
                        普通基础商机
                        <br>
                        <span class="fs10">（商机助手）</span>
                    </div>
                </div>
                <div class=" commonOption-item" @click="router.push('/opportunityEntry')">
                    <img src="../assets/opportunityEntry.svg" alt="" srcset="">
                    <div>
                        政企基础商机
                    </div>
                </div>
                <div class=" commonOption-item" @click="goToHuXiu" v-if="isGov">
                    <img src="../assets/huxiu.svg" alt="" srcset="">
                    <div>
                        新兴产数商机
                        <br>
                        <span class="fs10">（虎嗅平台）</span>
                    </div>
                </div>
            </div>
        </div>


        <div class="container__header mt-10">
            <div>
                <span class="ml-6" style="font-weight: 500;">商机处理</span>
            </div>
        </div>
        <div class="container_body card_body">
            <div class="commonOption">
                <div class=" commonOption-item" @click="goTo('商机处理')">
                    <img src="../assets/dispose.svg" alt="" srcset="">
                    <div>
                        接单宝
                    </div>
                </div>
            </div>
        </div>



        <van-share-sheet v-model:show="showShare" :options="options" title="我的商机单" @select="selectType" />
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, toRefs } from "vue";
import { getBusinessCodeAPI } from "@/api/common";

import { UserType, opportunityHooks } from "../hooks";
import router from "@/router";


type ShareSheetOption = {
    name: string;
    icon: string;
    description?: string;
    link: string;
    callAPI?: boolean;
    [key: string]: any
};
type ShareSheetOptions = ShareSheetOption[] | ShareSheetOption[][];




const optionList = ref([
    {
        text: '录入量',
        props: 'recordCount',
        color: '#004CBE',
    },
    {
        text: '接单量',
        color: '#4892FF',
        props: 'acceptCount',

    },
    {
        text: '待接单',
        color: '#FF9760',
        props: 'unacceptCount',

    },
    {
        text: '处理中',
        color: '#34C759',
        props: 'handlingCount',

    },
    {
        text: '已结单',
        color: '#48CBFF',
        props: 'finishCount',

    },
    {
        text: '转化率',
        color: '#004CBE',
        props: 'dispatchrate',

    },
])






const showShare = ref(false);
const options = ref<ShareSheetOptions>([]);

const { VITE_SALE_URL_DISPOSE, VITE_SALE_URL_ENTERING } = import.meta.env



const {
    myTableData,
    curSelectTimeType,
    curSelectTimeTime,
    showPopover,
    actions,
    isGov,
    roleCode,
    userInfoStore,
    onSelect,
    goToHuXiu,

} = opportunityHooks()


async function goTo(type: string) {
    let code = ''
    if (roleCode.value == 'sale') {

        getBusinessCodeAPI().then((res: any) => {
            if (res.code == 200) {
                code = res.data
            }
        })

        if (type == '普通基础商机') {

            options.value = [
                { name: '翼销售商机', icon: new URL(`../assets/saleIcon.png`, import.meta.url).href, link: VITE_SALE_URL_ENTERING + code, callAPI: true },
                { name: '现场填报商机', icon: new URL(`../assets/policies.png`, import.meta.url).href, link: 'http://wx.go189.cn/qywx/searchSend/homePage?corpId=wxe8c2710f51e9ee56' },
                { name: '沿街商铺商机', icon: new URL(`../assets/shopLogo.png`, import.meta.url).href, link: 'http://wx.go189.cn/qywx/searchSend/homePage?corpId=wxe8c2710f51e9ee56' },
            ]

        } else if (type == '商机处理') {
            options.value = [
                { name: '翼销售商机', icon: new URL(`../assets/saleIcon.png`, import.meta.url).href, link: VITE_SALE_URL_DISPOSE + code, callAPI: true },
                { name: '其他商机', icon: new URL(`../assets/otherSale.png`, import.meta.url).href, link: 'http://wx.go189.cn/jdb-mp/index.html' },
            ]
        }

        showShare.value = true
    } else if (roleCode.value == 'shop') {

        if (type == '普通基础商机') {
            getBusinessCodeAPI().then((res: any) => {
                if (res.code == 200) {
                    location.href = VITE_SALE_URL_ENTERING + res.data
                }
            })


        } else if (type == '商机处理') {
            location.href = `http://wx.go189.cn/jdb-mp/index.html`

        }
    } else {
        if (type == '普通基础商机') {
            location.href = `http://wx.go189.cn/qywx/searchSend/homePage?corpId=wxe8c2710f51e9ee56`

        } else if (type == '商机处理') {
            location.href = `http://wx.go189.cn/jdb-mp/index.html`

        }
    }





}
function selectType(option: ShareSheetOption, index: number) {
    if (option.callAPI) {
        const toast = showLoadingToast({
            message: '请稍等...',
            forbidClick: true,
            loadingType: 'spinner',
            duration: 0,
            overlay: true,
            teleport: '.main-page',
        });



        getBusinessCodeAPI().then((res: any) => {
            if (res.code == 200) {
                nextTick(() => {
                    window.location.href = option.link + res.data
                })
            }
        }).finally(() => {
            toast.close()
        })
    } else {
        window.location.href = option.link
    }

}





</script>


<style lang="scss" src="../index.scss" scoped></style>
<style lang="scss" src="@/views/template/Contribution/switch.scss" scoped></style>

<style lang="scss" scoped>
:deep(.van-share-sheet__options) {
    gap: 10px;
}

:deep(.van-share-sheet__image-icon) {
    border-radius: 5px;
    object-fit: contain;
}

.cusClassIcon {
    font-size: 45px;
}

.left {
    position: relative;
    /* 或者 absolute，确保能看到位置变化 */
    transform: rotate(-90deg);
    animation: identifier_ 1.5s infinite;
}
</style>
