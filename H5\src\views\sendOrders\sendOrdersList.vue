<template>
    <div class="card container_">
        <div class="container__header" style="border-bottom: 0px;">


            <div style="display: flex; align-items: center;">


                <van-icon name="arrow-left" size="4.5vw" class="mr-5" @click="router.go(-1)" />
                <img src="@/assets/sendOrdersInfo.svg" alt="" srcset="">
                <span class="ml-6" style="font-weight: 500;">派单情况</span>
                <van-popover placement="bottom-start">
                    <div class="popoverBody">
                        <li>派单总量：所有框架下的所有单量</li>
                        <li>已执行量：外呼时长大于0的数量</li>
                        <li>外呼量：外呼时长大于30秒的数量</li>
                        <li>待处理量：派单量-已执行量</li>
                        <li>以上数据均来自智慧营销，最终口径以业务为准</li>

                    </div>
                    <template #reference>
                        <van-icon class="ml-3" name="question" color="#1A76FF" />
                    </template>
                </van-popover>


            </div>
            <div class="searchBox" @click="popoverIsShow = true">
                <img class="mr-3" src="@/assets/search.svg" alt="" srcset="">
                <span class="value">{{ searchName ? searchName : '请选择查询对象' }} </span>
            </div>
        </div>


        <div class="container_body ">

            <router-link :to="{
                path: '/sendOrdersList',
                query: {
                    startTime: formline.startTime,
                    endTime: formline.endTime
                }
            }">
                <div v-for="(staffItem, index) in tableList" :key="index">
                    <div class="mb-8 mt-5" style="display: flex;align-items: center;">
                        <span class="itemName" v-getName="{ item: staffItem }">{{ staffItem.staffName }}</span>
                        <span class="ml-2 itemRoleName">{{ staffItem.staffRoleName }}</span>
                    </div>
                    <div class="optionList">

                        <div class="item" v-for="item in optionList" :key="item.text">
                            <div class="value" :style="{ color: item.color }">{{ staffItem[item.props] }}</div>
                            <div class="text">{{ item.text }}</div>

                        </div>
                    </div>

                </div>
                <van-empty description="暂无数据" class="mt-8" style="background-color: #fff;border-radius: 15px;"
                    v-if="tableList.length == 0" />


            </router-link>
        </div>


        <StaffList v-model:searchStaffName="searchName" v-model:popoverIsShow="popoverIsShow"
            @confirmStaffCode="confirmStaffCode"></StaffList>


        <!-- <van-popup v-model:show="popoverIsShow" round position="bottom" teleport="body">
            <van-picker title="人员列表" :columns="staffList" :columns-field-names="{
                text: 'staffName',
                value: 'staffCode'
            }" @confirm="confirmStaffCode" @cancel="popoverIsShow = false">
                <template #title>
                    <van-search class="mt-5 mb-5" v-model="searchStaffName" placeholder="请输入搜索关键词" />
                </template>
            </van-picker>
        </van-popup> -->
    </div>
</template>

<script setup lang="ts">
import StaffList from "@/components/staffList/index.vue";
import { useRouter, useRoute } from "vue-router";
import { getSendOrderEveryoneListAPI } from "@/api/home";
import { searchHooks } from "@/hooks_modules/changeTimeHooks.ts";
import { useUserInfo } from "@/stores/userInfo";
const router = useRouter()
const route = useRoute()
const userInfoStore = useUserInfo()

interface Option {
    text: string;  // 选项文本
    color: string;  // 颜色的十六进制代码
    props: string,
}
// 定义每个选项的类型
interface OptionList {
    dispatchNum: Option;  // 派单总量
    implementedNum: Option;  // 已执行量
    outCallNum: Option;  // 外呼量
    pengdingNum: Option;  // 待处理量
    [key: string]: any
}
// 定义列表
interface DispatchData {

    dispatchNum: string; // 调度次数
    implementedNum: string; // 已执行次数
    outCallNum: string; // 外呼次数
    pengdingNum: string; // 待处理次数
    staffName: string; // 员工姓名
    staffRoleName: string; // 员工角色名称
    successConvertionTotal: string; // 成功转化总数
    [key: string]: any
}

const {
    formline,
    listData,
    popoverIsShow,
    searchName,
    confirmStaffCode,
} = searchHooks()
const optionList = reactive<OptionList>({

    dispatchNum: {
        text: '派单总量',
        color: '#1A76FF',
        props: 'dispatchNum'
    },
    implementedNum: {
        text: '已执行量',
        color: '#6282FD',
        props: 'implementedNum'

    },
    pengdingNum: {
        text: '待处理量',
        color: '#FFA34D',
        props: 'pengdingNum'

    },
    outCallNum: {
        text: '外呼量',
        color: '#b462fd',
        props: 'outCallNum'

    },
    successResultNum: {
        text: '成功数',
        color: '#00AFA3',
        props: 'successResultNum'
    },




})



const tableList = ref<DispatchData[]>([])

const searchStaffName = ref('') // 搜索的人名字

const staffList = computed(() => {
    const input = searchStaffName.value.toLowerCase();

    return listData.value.filter(item => {
        const staffName = item.staffName ? item.staffName.toLowerCase() : '';
        const staffRoleName = item.staffRoleName ? item.staffRoleName.toLowerCase() : '';
        const staffCode = item.staffCode ? item.staffCode.toLowerCase() : '';

        // 检查是否匹配 staffName、staffRoleName 或 staffCode
        return (
            staffName.includes(input) ||
            staffRoleName.includes(input) ||
            staffCode.includes(input)
        );
    });
})

// 获取列表数据 
function laodSendOrderEveryoneList() {
    let params = {
        startTime: route.query.startTime ? `${route.query.startTime} 00:00:00` : '',
        endTime: route.query.endTime ? `${route.query.endTime} 23:59:59` : '',
        staffCode: formline.staffCode[0] == '全部' ? '' : formline.staffCode[0],
        isFrame: route.query.orderType == 'frame' ? 1 : 0   //1 框架  0 全部
    }
    getSendOrderEveryoneListAPI(params).then((res: any) => {
        if (res.code == 200) {
            if (res.code == 200) {
                tableList.value = res.data
            }
        }

    })

}







watch(() => formline.staffCode, (value) => {
    laodSendOrderEveryoneList()

})


onMounted(() => {
    formline.endTime = route.query.endTime
    formline.startTime = route.query.startTime
    console.log(listData);

    laodSendOrderEveryoneList()
})
</script>


<style lang="scss" scoped>
.card {
    font-family: 'Source Han Sans, Source Han Sans';
    height: 100%;
    background: #fbfcff;
    overflow-y: scroll;
    overflow-x: hidden;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px 10px 0px 0px;

    .container_body {
        padding-top: 0px;
        padding-bottom: 0px;

    }

    .searchBox {

        background-color: #EDF5FF;
        border-radius: 5px;
        height: 40px;
        display: flex;
        align-items: center;
        padding: 3px 10px;

        .value {
            font-weight: 400;
            font-size: 14px;
            color: #72ABFF;
        }
    }

    .itemName {
        font-weight: 700;
        font-size: 20px;
    }

    .itemRoleName {
        font-size: 12px;
        padding: 3px 6px;
        border-radius: 5px;
        background-color: #D8D8D8;
        text-align: center;
        margin-left: 5px;
    }
}

.optionList {

    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
    display: flex;
    // justify-content: space-around;
    flex-wrap: wrap;
    margin-bottom: 50px;

    .item {
        flex: 1 0 33.3333%;
        max-width: 33.33333%;
        display: flex;
        height: 100%;
        padding: 15px 5px;
        flex-direction: column;
        place-content: center;
        text-align: center;

        .text {
            font-weight: 500;
            font-size: 12px;
            color: #3D3D3D;
        }

        .value {
            font-family: 'DIN, DIN';
            font-weight: 700;
            font-size: 23px;
            color: #6282FD;
            margin-bottom: 7px;
        }
    }
}

:deep(.van-search__conten) {
    border-radius: 10px !important;

}
</style>