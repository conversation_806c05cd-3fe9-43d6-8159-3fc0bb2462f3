import myCharts from "@/components/charts/myCharts.vue";
import { getSendOrderDetailAPI } from "@/api/home";
import { useRoute } from "vue-router";
import { ref, reactive } from "vue";



interface Data {
    kangJiaName: string
    kangJiaType?: 0 | 1 | 2;  // 框架分类，0-S, 1-A, 2-B, 可选
    kangJiaCode?: string;  // 框架代码，可选
    outCallNum?: number;  // 外呼量，可选
    outCallRate?: number;  //外呼率
    successResultNum?: number;  // 成功数，可选
    dispatchNum?: number;  // 派单量，可选
    touchNum?: number;  // 接触量，可选
    touchRate?: number;  // 接触率，可选

    // transformNum?: number;  // 转化量，可选
    // transformRate?: number;  // 转化率，可选
    [key: string]: any
}

export const sendOrdersDetailHook = function () {

    const route = useRoute()

    const tableData = ref<Data[]>([]);





    function getEchartsOption(row: Data) {

        const data = [
            { value: 0, name: '接触量', props: 'touchNum' },
            { value: 0, name: '接触率', props: 'touchRate' },
            // { value: 0, name: '转化量', props: 'transformNum' },
            // { value: 0, name: '转化率', props: 'transformRate' },
            { value: 0, name: '外呼量', props: 'outCallNum' },
            { value: 0, name: '外呼率', props: 'outCallRate' },
            { value: 0, name: '成功数', props: 'successResultNum' },
        ]
        // 设置比例因子，将接触率和转化率的数值缩放
        let scaleFactor = 4; // 将接触率和转化率数据放大，使其适应 x 轴
        let mycolor = ['#1A76FF', '#27C272']
        let option = {
            grid: {
                left: "15%",
                top: "0%",
                right: '15%',
                bottom: "0%",
            },
            tooltip: {
                trigger: "item",
                formatter: function (params: any) {
                    // 反向缩放，展示实际值
                    let originalValue = params.name.includes('率') ? params.value / scaleFactor : params.value;
                    return `${params.name}: ${originalValue}${params.name.includes('率') ? '%' : ''}`;
                }
            },
            xAxis:
            {
                show: false,
                type: "value",
                max: 400, // 设置接触量和转化量的最大值
            },


            yAxis: [
                {
                    type: "category",
                    data: data.map(item => item.name),
                    axisLine: { show: false },
                    axisTick: { show: false },
                    axisLabel: {
                        color: " #3D3D3D ",
                        fontSize: 13,
                        show: true,
                        fontWeight: 500,
                        margin: 10,

                    },
                    inverse: true,
                },


                {
                    type: 'category',
                    inverse: true,
                    axisTick: 'none',
                    axisLine: 'none',
                    show: true,
                    axisLabel: {

                        align: 'left', // Align the labels to the left
                        formatter: function (params: any, index: any) {
                            return `{color${index % 2}|${params}${index % 2 == 0 ? '' : '%'}}`;
                        },
                        rich: {
                            color0: { color: mycolor[0], fontSize: 14 }, // For first label
                            color1: { color: mycolor[1], fontSize: 14 }, // For second label

                        },
                    },
                    data: data.map(item => ({ value: row[item.props], name: item.name, })
                    )
                }
            ],
            series: [
                {
                    name: "",
                    type: "bar",
                    data: data.map(item => {
                        return item.name.includes('率') ? row[item.props] * scaleFactor : row[item.props]
                    }),
                    showBackground: true,
                    backgroundStyle: { color: "#EDF5FF", borderRadius: [8, 8, 8, 8] },
                    barCategoryGap: 10, //间距

                    barWidth: 10, //宽度
                    itemStyle: {
                        normal: {
                            borderRadius: [20, 20, 20, 20],
                            color: function (d: any) {
                                return mycolor[d.dataIndex % 2];
                            },
                        },
                    },
                    yAxisIndex: 0, //定位层级，类似z-index
                },
            ],
        };
        return option
    }




    const curOrderType = ref<string>('S')  //     S  S类       A  A类     B B类


    function changeOrderType(type: string) {
        if (curOrderType.value == type) return
        curOrderType.value = type
        loadDetaild()
    }

    function loadDetaild() {
        let parmas = {
            kangJiaType: curOrderType.value,
            startTime: route.query.startTime ? `${route.query.startTime} 00:00:00` : '',
            endTime: route.query.endTime ? `${route.query.endTime} 23:59:59` : ''
        }
        getSendOrderDetailAPI(parmas).then(res => {
            for (const item of res.data || []) {
                item.outCallRate = item.dispatchNum == 0 ? 0 : Math.round((item.outCallNum / item.dispatchNum) * 100)
                item.touchRate = item.dispatchNum == 0 ? 0 : Math.round((item.touchNum / item.dispatchNum) * 100)
                item.transformRate = item.dispatchNum == 0 ? 0 : Math.round((item.transformNum / item.dispatchNum) * 100)
            }
            tableData.value = res.data || []
        })
    }



    onMounted(() => {
        loadDetaild()
    })


    return {
        tableData,
        getEchartsOption,
        curOrderType,
        changeOrderType

    }

}


