import { createApp } from "vue";
import pinia from "./stores";
import router from "./router";
import App from "./App.vue";
import "./router/permission";
import "@/styles/index.scss";
import 'normalize.css/normalize.css'
import directive from './directive'
import iconfontSvg from "@/components/iconfont/iconfont.vue";
import VConsole from 'vconsole';
import 'vant/es/toast/style'  //toast
import 'vant/es/dialog/style'  //dialog
import 'vant/es/notify/style'  //notify
import 'vant/es/image-preview/style' //imagePreview


import { ImagePreview, Lazyload } from 'vant' //vant4的懒加载
const app = createApp(App);

app.component("svg-icon", iconfontSvg)

if (import.meta.env.VITE_MODE == 'test') {    
    const vConsole = new VConsole();
}


app.use(pinia);
app.use(directive)
app.use(router).use(Lazyload, { lazyComponent: true }).use(ImagePreview).mount("#app");
