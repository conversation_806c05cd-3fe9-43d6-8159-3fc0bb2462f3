// 导入路由模块
import router from "@/router";

// 导入用户信息的Pinia存储模块
import { useUserInfo } from "@/stores/userInfo";
// 导入Pinia的storeToRefs工具，用于保持响应性
import { storeToRefs } from 'pinia'
// 导入NProgress库用于路由跳转时的加载进度条
import NProgress from "nprogress";
// 导入NProgress的CSS样式
import "nprogress/nprogress.css";
import { ElScrollbar } from "element-plus";

// 配置NProgress，设置不显示加载旋钮
NProgress.configure({ showSpinner: false });

// 定义白名单路由数组，存储无需验证即可访问的路由路径
const whiteList: any[] = ['/login', '/404']; // 白名单中的重定向

// 使用 beforeEach 钩子，在路由跳转前进行全局守卫
router.beforeEach(async (to, from, next) => {
  // 获取用户信息的Pinia存储实例
  const userInfoStore = useUserInfo()
  const url = window.location.href;

  // 创建一个新的URL对象，用于解析URL参数
  let urlObj = new URL(url);
  // 获取查询参数
  let searchParams = new URLSearchParams(urlObj.search);
  // 读取URL中的'code'参数值，用于第三方登录
  let codeValue = searchParams.get('code');
  // 读取URL中的'randomParam'参数值，用于随机参数验证
  let randomParamValue = searchParams.get('randomParam');
  // 如果存在code或randomParam参数，则先登出当前用户
  if (codeValue || randomParamValue) {
    userInfoStore.logOut()
  }

  // 开始NProgress加载进度条
  NProgress.start();

  // 通过store获取用户Token状态
  const hasToken = userInfoStore.getToken;
  // 判断用户是否已登录（是否有Token）
  if (hasToken) {
    const roles = userInfoStore.userInfo.roles || []
    if (hasPermission(roles, to.meta.roles as string[])) {
      if (to.path == '/') {
        if (roles.includes("department_dispatch")) {
          next("/activityList");
        } else if (roles.includes("area_dispatch")) {
          next("/seedOrderList");
        } else {
          //todo
          next();
        }
      } else {
        // 非根路径直接放行
        next()
      }
    } else {
      ElMessage.error('无权限访问')
      next(false);

    }


  } else {
    // 用户未登录
    // 检查即将访问的路径是否在白名单内，是则放行
    if (whiteList.indexOf(to.path) !== -1) {
      next();
    } else {
      // 如果URL中包含code或randomParam参数，尝试进行登录验证
      if (codeValue || randomParamValue) {
        try {
          // 获取用户信息
          let type = codeValue ? 'code' : 'randomParam'
          // 使用code或randomParam设置token
          await userInfoStore.setToken((codeValue || randomParamValue) as string, type)

          // 删除URL中的参数并更新URL，避免参数暴露
          if (codeValue) {
            searchParams.delete('code');
          }
          if (randomParamValue) {
            searchParams.delete('randomParam');
          }
          // 使用 replaceState 修改 URL，而不会触发页面刷新
          window.history.replaceState({}, '', `${urlObj.pathname}${urlObj.hash}`);

          // 设置完用户信息后，重新获取token状态
          const hasToken = userInfoStore.getToken

          if (hasToken) {
            // 获取用户角色列表
            const roles = userInfoStore.userInfo.roles || []
            // 使用 hasPermission 函数检查用户是否有权限访问目标路由

            if (hasPermission(roles, to.meta.roles as string[])) {
              // 如果访问的是根路径，根据用户角色重定向到不同页面

              if (to.path == '/') {
                if (roles.includes("department_dispatch")) {
                  next("/activityList");
                } else if (roles.includes("area_dispatch")) {
                  next("/seedOrderList");
                } else {
                  //todo
                  next();
                }
              } else {
                // 非根路径直接放行
                next({ path: to.path })
              }
            } else {
              // 无权限访问时显示错误信息并重定向到404页面
              ElMessage.error('无权限访问')
              next('/404')
            }
          } else {
            // token无效时重定向到404页面
            next('/404')
            NProgress.done()
          }
        } catch (error) {
          // 发生错误时重定向到404页面
          next('/404')
          NProgress.done()
        }
      } else {
        // 没有code或randomParam参数时重定向到登录页
        next('/login')
        NProgress.done()
      }
    }
  }
});

// 使用 afterEach 钩子，在路由跳转完成后进行全局守卫
router.afterEach(() => {
  // 完成NProgress加载进度条
  NProgress.done();
});

//判断是否有跳转权限 
/**
 * 判断用户是否有跳转权限
 * @param userRoles 用户拥有的角色数组
 * @param routeRoles 路由需要的角色数组
 * @returns {boolean} 是否有权限
 */
function hasPermission(userRoles: string[], routeRoles?: string[]): boolean {
  // 如果路由未设置roles，默认有权限
  if (!routeRoles || routeRoles.length === 0) {
    return true;
  }
  // 用户角色与路由所需角色有交集则有权限
  return userRoles.some(role => routeRoles.includes(role));
}
