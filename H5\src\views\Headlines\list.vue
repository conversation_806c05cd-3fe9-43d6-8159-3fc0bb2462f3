<template>
    <div>
        <headlinesHeader v-if="route.query.areaId" actionTypeNameAlign="center" :actionType="areaList"
            v-model:curSelectActionType="curSelectAreaType" @changeSelectActionType="changeSelectAreaType">
        </headlinesHeader>


        <headlinesHeader v-else actionTypeNameAlign="center" :actionType="roleList"
            v-model:curSelectActionType="curSelectRoleType" @changeSelectActionType="changeSelectRoleType">

        </headlinesHeader>



        <list :title="title" @GO="GO" :isShowGO="isShowGO" :tabList="tabList"></list>
    </div>
</template>

<script setup lang="ts">
import list from "./modules_/list.vue";
import headlinesHeader from "@/views/Headlines/modules_/header.vue";
import { useRoute, useRouter } from "vue-router";
import { getHeadlinesbyAreaCountListAPI } from "@/api/home";
import { getlistStaffRole } from "@/hooks_modules/getlistStaffRole";
import { getlistDeptOrSubOptions } from "@/hooks_modules/getlistDeptOrSubOptions";
import { useUserInfo } from "@/stores/userInfo";
import { mapSort } from "@/utils/mapSort";

const userInfo = useUserInfo()
const { roleList } = getlistStaffRole()
const { areaList } = getlistDeptOrSubOptions()
const route = useRoute()
const router = useRouter()
const isShowGO = ref(true)




// 当前选择的角色 
const curSelectRoleType = ref()
const curSelectAreaType = ref('')
function changeSelectRoleType(params: any) {
    loadHeadlinesbyAreaCountList()
}
function changeSelectAreaType() {
    loadHeadlinesbyAreaCountList()

}
function GO(params: any) {
    if (params.type == 1) return  //分局页面 不在下钻
    curSelectAreaType.value = params.areaId
    router.push({
        path: '/headlinesList', query: {
            ...route.query,
            roleID: curSelectRoleType.value ? curSelectRoleType.value : route.query.roleID,
            areaId: params.areaId,
            type: 1 //分局
        }
    })
}

watch(() => route.query.type, (val: any) => {
    if (val == 0) {
        //区域
        isShowGO.value = true
    } else {
        // 分局
        isShowGO.value = false
    }
    curSelectAreaType.value = String(route.query.areaId ?? '')
    curSelectRoleType.value = String(route.query.roleID ?? '')
    loadHeadlinesbyAreaCountList()
}, { immediate: true })



const tabList = ref([])
function loadHeadlinesbyAreaCountList() {
    let params = {
        type: route.query.type,
        areaId: curSelectAreaType.value ? curSelectAreaType.value : undefined,
        staffRoleCode: curSelectRoleType.value ? curSelectRoleType.value : undefined,
        startTime: route.query.startTime ? route.query.startTime : undefined,
        endTime: route.query.endTime ? route.query.endTime : undefined,
    }
    getHeadlinesbyAreaCountListAPI(params).then((res: any) => {
        if (res.code == 200) {
            res.data = mapSort(res.data)
            tabList.value = res.data || []
        }
    })
}

const title = computed(() => {
    let title = '南京分公司积分完成情况'
    const areaText = areaList.value.find(item => item.value == curSelectAreaType.value)?.text || ''
    const roleText = roleList.value.find(item => item.value == curSelectRoleType.value)?.text || ''
    const orgName = userInfo.userInfo.orgName || ''


    let areaName = areaText == '全部' ? '' : areaText
    let roleName = roleText == '全部' ? '' : roleText


    title = `南京分公司${orgName}${areaName}${roleName}积分完成情况`


    return title
})

onMounted(() => {

})
onActivated(() => {
})
</script>

<style lang="scss" scoped></style>