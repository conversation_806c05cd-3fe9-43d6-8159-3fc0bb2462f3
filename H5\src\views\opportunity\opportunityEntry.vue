<template>
    <div class="container_">
        <div class="container__header" style="padding: 0px 15px;">
            <div> 填报类型</div>
            <cusTabs :tabList="fillingTypeList" v-model:curSelectTab="formLine.fillingType" @changeSelectTab="">
            </cusTabs>
        </div>

        <div class="container__header">
            <div>客户类型</div>
            <van-popover :actions="customerTypeList" @select="changeCustomerType" placement="bottom-end">
                <template #reference>
                    <div class="selectOption-item ">
                        <span>{{ formLine.customerType }}</span>
                        <van-icon name="play" class="play" color="#1A76FF" />
                    </div>
                </template>
            </van-popover>
        </div>

        <div class="container__header">
            <div style="display: flex;align-items: center;justify-content: space-between;width: 100%;">
                <span>商机类型</span>
                <router-link to="/entryDetailList">
                    <span class="history-record">历史记录</span>
                </router-link>
            </div>
        </div>
        <div class="container_body">
            <div class="dictList">
                <div class="dictItem" @click="changeOpportunityType(item)" v-for="item in opportunityTypeOptions"
                    :key="item.label" :class="item.value ? 'activeItem' : ''">
                    {{ item.label }}
                </div>
            </div>
        </div>

        <div class="container__header" style="flex-direction: column;align-items: start;">
            <div>{{ formLine.customerType == '存量' ? '关联号码' : "客户名称" }}</div>
            <template v-if="formLine.customerType == '存量'">
                <van-search v-model="formLine.relationNumber"  placeholder="请输入关联号码"
                    style="width: 100%;"  background="#fff"></van-search>
                <!-- <van-number-keyboard v-model="formLine.relationNumber" :maxlength="11" :show="keyboardIsShow"
                    @blur="keyboardIsShow = false" /> -->
                <div class="cusetName">
                    <span>客户名称：</span>
                    <span>{{ formLine.customerName ? formLine.customerName : '- -' }}</span>
                </div>

            </template>
            <van-search v-model="formLine.customerName" placeholder="请输入客户名称" style="width: 100%;" background="#fff"
                v-else></van-search>
        </div>


        <van-form @submit="onSubmit">
            <template v-if="opportunityTypeOptions.some(item => item.value)">
                <div class="container__header">
                    <div>商机情况</div>
                </div>
                <div class="container_body" style="padding: 0px 0px 10px 0px;">
                    <van-collapse v-model="activeNames">
                        <van-collapse-item :title="type" :name="type" v-for="(items, type) in formLine.broadbandMap"
                            :key="type">

                            <template #title>
                                <div style="display: flex;align-items: center;">
                                    <span class="mr-15">{{ type }}</span>
                                    <div style="display: flex;align-items: center;" @click.stop="addItem(type)">
                                        <i class="icon-jia fs20 ml-20 mr-3" style="color: #1989fa;"></i>
                                        <span class="fs12">添加行</span>
                                    </div>
                                </div>
                            </template>
                            <div v-for="(item, idx) in items" :key="idx" class="broadband-item-form mb-10">
                                <van-field v-model="item.count" label="数量" type="number" placeholder="请输入数量"
                                    :rules="[{ required: true, message: '请输入数量' }]" />
                                <van-field v-model="item.category" label="分类" placeholder="请输入分类" center
                                    :rules="[{ required: true, message: '请填写分类' }]">
                                    <template #input>
                                        <div style="width: 100%;text-align: right;">
                                            <van-popover
                                                :actions="[{ text: '新装', value: '新装' }, { text: '不填', value: '不填' }]"
                                                @select="(params) => { item.category = params.value }" placement="left">
                                                <template #reference>
                                                    <div class="selectOption-item">
                                                        <span style="height: 17px;line-height: 17px;">{{
                                                            item.category}}</span>
                                                        <van-icon name="play" class="play" color="#1A76FF" />
                                                    </div>
                                                </template>
                                            </van-popover>
                                        </div>
                                    </template>
                                </van-field>

                                <!-- 宽带、eTV、天翼云眼、FTTR-B -->
                                <template v-if="type !== 'SDWAN'">
                                    <van-field v-model="item.antiNet" label="异网策反" placeholder="请输入异网策反" center
                                        :rules="[{ required: true, message: '请填写异网策反' }]">
                                        <template #input>
                                            <div style="width: 100%;text-align: right;">
                                                <van-popover
                                                    :actions="[{ text: '是', value: '是' }, { text: '否', value: '否' }]"
                                                    @select="(params) => { item.antiNet = params.value }"
                                                    placement="left">
                                                    <template #reference>
                                                        <div class="selectOption-item">
                                                            <span style="height: 17px;line-height: 17px;">{{
                                                                item.antiNet}}</span>
                                                            <van-icon name="play" class="play" color="#1A76FF" />
                                                        </div>
                                                    </template>
                                                </van-popover>
                                            </div>
                                        </template>
                                    </van-field>
                                </template>
                                <!-- 数字电路、专线 -->
                                <template v-else-if="['数字电路', '专线'].includes(type)">
                                    <van-field v-model="item.rate" label="速率/M" type="number" placeholder="请输入速率"
                                        :rules="[{ required: true, message: '请输入速率' }]" />
                                </template>
                                <!-- SDWAN -->
                                <template v-else-if="type === 'SDWAN'">
                                    <van-field v-model="item.rate" label="速率/M" type="number" placeholder="请输入速率"
                                        :rules="[{ required: true, message: '请输入速率' }]" />
                                    <van-field v-model="item.direction" label="方向" placeholder="请输入方向"
                                        :rules="[{ required: true, message: '请输入方向' }]" />
                                </template>
                                <!-- 固话 -->
                                <template v-else-if="type === '固话'">
                                    <van-field v-model="item.addition" label="叠加" placeholder="请输入叠加"
                                        :rules="[{ required: true, message: '请输入叠加' }]" />
                                </template>
                                <!-- 天翼团单 -->
                                <template v-else-if="type === '天翼团单'">
                                    <van-field v-model="item.vnetNo" label="V网参照号" placeholder="请输入V网参照号"
                                        :rules="[{ required: true, message: '请输入V网参照号' }]" />
                                </template>

                                <van-field v-model="item.rent" label="月租/元" type="number" placeholder="请输入月租"
                                    :rules="[{ required: true, message: '请输入月租' }]" />
                                <div style="display: flex;align-items: center; justify-content: flex-end;width: 100%;"
                                    class="mt-3" @click="deleteItem(type, idx)" v-if="items.length > 1">

                                    <span class="fs12 text-danger mr-20"
                                        style="text-decoration:underline ;font-style: italic;">删除行</span>

                                </div>

                            </div>
                        </van-collapse-item>

                    </van-collapse>
                </div>
            </template>


            <van-field v-model="formLine.description" rows="2" autosize label="商机描述" type="textarea" maxlength="100"
                placeholder="请输入商机描述" show-word-limit :rules="[{ required: true, message: '请输入商机描述' }]" />
            <div class="stage">
                <div style="font-weight: 700;" class="fs14">商机阶段</div>
                <van-popover :actions="stageOptions" @select="(params) => formLine.stage = params.value"
                    placement="bottom-end" class="custom-van-popover">
                    <template #reference>
                        <div class="selectOption-item ">
                            <span>{{ formLine.stage ? formLine.stage : '请选择商机阶段' }}</span>
                            <van-icon name="play" class="play" color="#1A76FF" />
                        </div>
                    </template>
                </van-popover>
            </div>


            <div class="button-wrapper" style="padding: 60px 0;">
                <van-button type="primary" block style="width: 90%; margin: 0 auto;"
                    native-type="submit">提交</van-button>
            </div>

        </van-form>
    </div>
</template>

<script setup lang="ts">
import cusTabs from "@/components/cusTabs/index.vue";
import { reactive, ref, computed, watch } from 'vue';
import { cloneDeep, debounce } from "lodash-es";
import { showFailToast, showSuccessToast } from "vant";
import { basicOpportunityEntryAPI, queryCustNameAPI } from "@/api/opportunity";
const router = useRouter()

// 商机信息类型
interface ZqInfo {
    busiType: string;      // 商机类型
    busiCnt: string;       // 商机数量
    vpnAccsNbr: string;    // VPN专线号
    speed: string;         // 速率
    busiSpec: string;      // 业务规格/分类
    ifChange: string;      // 是否转化
    ifLsms: string;        // 异网策反
    bipAccsNbr: string;    // 接入号
    cntChange: number;     // 转化数量
    city: string;          // 方向
    limitMonChrg: number;  // 月租费用(元)
    ghAdd: string;         // 固话叠加
}


// 请求参数类型
interface RequestData {
    tsType: string;
    custType: string;
    busiRemark: string;
    accsNbr: string;
    custName: string;
    sjState: string;
    zqInfos: ZqInfo[];
}


// 基础接口
interface BaseItem {
    count: string;
    category: '新装' | '不填';
    rent: string;
    type: string;
    antiNet?: '是' | '否';
    rate?: string;
    direction?: string;
    addition?: string;
    vnetNo?: string;
}


// 类型模板
const broadbandItemTemplate: Record<string, any> = {
    '宽带': { type: '宽带', count: '', category: '新装', antiNet: '否', rent: '' },
    'ETV': { type: 'ETV', count: '', category: '新装', antiNet: '否', rent: '' },
    '天翼云眼': { type: '天翼云眼', count: '', category: '新装', antiNet: '否', rent: '' },
    'FTTR-B': { type: 'FTTR-B', count: '', category: '新装', antiNet: '否', rent: '' },
    '数字电路': { type: '数字电路', count: '', rate: '', category: '新装', antiNet: '否', rent: '' },
    '专线': { type: '专线', count: '', rate: '', category: '新装', antiNet: '否', rent: '' },
    'SDWAN': { type: 'SDWAN', count: '', rate: '', category: '新装', direction: '', rent: '' },
    '固话': { type: '固话', count: '', addition: '', category: '新装', antiNet: '否', rent: '' },
    '天翼团单': { type: '天翼团单', count: '', vnetNo: '', category: '新装', antiNet: '否', rent: '' }
};




interface FormType {
    fillingType: string;
    customerType: string;
    relationNumber: string;
    customerName?: string;
    broadbandMap: Record<string, BaseItem[]>;
    description: string;
    stage: string;
}


const activeNames = ref<string[]>([]);
const formLine = reactive<FormType>({
    fillingType: '商机',
    customerType: '存量',
    relationNumber: '',
    customerName: '',
    broadbandMap: {},
    description: '',
    stage: '',
});


const opportunityTypeOptions = ref([
    { label: '宽带', value: false },
    { label: 'ETV', value: false },
    { label: '数字电路', value: false },
    { label: '天翼云眼', value: false },
    { label: 'SDWAN', value: false },
    { label: '专线', value: false },
    { label: '固话', value: false },
    { label: 'FTTR-B', value: false },
    { label: '天翼团单', value: false }
]);


const fillingTypeList = ref([
    { text: '商机', value: '商机' },
    { text: '风险', value: '风险' },
]);


const customerTypeList = computed(() => {
    if (formLine.fillingType === '风险') {
        return [{ text: '存量', value: '存量' }];
    }
    return [
        { text: '存量', value: '存量' },
        { text: '新客', value: '新客' }
    ];
});


function changeCustomerType(params: any) {
    if (formLine.customerType == params.value) return;
    formLine.customerType = params.value;
}


const keyboardIsShow = ref(false);


// 选择商机类型时，动态增删表单
function changeOpportunityType(item: any) {
    item.value = !item.value;
    if (item.value) {
        if (!formLine.broadbandMap[item.label]) {
            formLine.broadbandMap[item.label] = [cloneDeep(broadbandItemTemplate[item.label])];
            setTimeout(() => {
                activeNames.value = Array.from(new Set([...activeNames.value, item.label]));
            }, 200);
        }
    } else {
        delete formLine.broadbandMap[item.label];
        activeNames.value = activeNames.value.filter((t: any) => t != item.label);
    }
}


// 商机阶段选项
const stageOptions = [
    { text: '需求沟通', value: '需求沟通' },
    { text: '深化关系', value: '深化关系' },
    { text: '方案提交', value: '方案提交' },
    { text: '商务磋商', value: '商务磋商' },
    { text: '招标阶段', value: '招标阶段' },
    { text: '签约受理', value: '签约受理' },
    { text: '已结束', value: '已结束' },
    { text: '不选', value: '不选' }
];


const addItem = (type: any) => {
    formLine.broadbandMap[type].push(cloneDeep(broadbandItemTemplate[type]));
};


const deleteItem = (type: any, index: any) => {
    formLine.broadbandMap[type].splice(index, 1);
};


function onSubmit() {
    if (!formLine.stage) {  // 检查是否选择了商机阶段
        showFailToast('请选择商机阶段');
        return;
    }
    if (!formLine.customerName) {
        showFailToast('请输入客户名称');
        return;
    }

    // 构建请求参数
    const requestData: RequestData = {
        tsType: formLine.fillingType,        // 填报类型
        custType: formLine.customerType,     // 客户类型
        busiRemark: formLine.description || '',  // 商机描述
        accsNbr: formLine.relationNumber || '',  // 关联号码
        custName: formLine.customerName || '',   // 客户名称
        sjState: formLine.stage,            // 商机阶段 需求沟通、深化关系、方案提交、商务磋商、招标阶段、签约受理、已结束、不选 不选为空
        zqInfos: []  // 商机信息数组
    };

    // 处理商机信息
    Object.entries(formLine.broadbandMap).forEach(([type, items]) => {
        items.forEach(item => {
            const zqInfo: ZqInfo = {
                busiType: type,              // 商机类型
                busiCnt: item.count || '0',  // 商机数量
                vpnAccsNbr: item.vnetNo || '',      // vpn专线号
                speed: item.rate || '',             // 速率
                busiSpec: item.category || '',      // 接入号
                ifChange: '',                      // 是否转化
                ifLsms: item.antiNet || '',        // 异网策反
                bipAccsNbr: '',                    // 接入号
                cntChange: 0,                      // 转化数量
                city: item.direction || '',         // 方向
                limitMonChrg: parseFloat(item.rent) || 0,  // 月租/元
                ghAdd: item.addition || ''          // 固话叠加
            };
            requestData.zqInfos.push(zqInfo);
        });
    });

    // 调用接口
    basicOpportunityEntryAPI(requestData).then((res: any) => {

        if (res.code == 200) {  // 请求成功
            router.push({ path: '/entryDetailList' })
            showSuccessToast('提交成功');
            // 这里可以添加提交成功后的跳转逻辑
        } else {  // 请求失败
            showFailToast(res.msg || '提交失败');
        }
    }).catch(err => {  // 接口调用异常
        showFailToast('提交失败');
    });
}

watch(() => formLine.relationNumber, debounce((newvalue) => {
    if (newvalue) {
        queryCustNameAPI({ accsNbr: newvalue }).then((res: any) => {
            if (res.code == 200) {
                formLine.customerName = res.data;
            }
        });
    }
}, 1000));


watch(() => formLine.fillingType, (newvalue) => {
    if (newvalue === '风险') {
        formLine.customerType = '存量';
    }
});
</script>

<style lang="scss" scoped>
.container_ {
    min-height: 100%;
    background: #fff;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px;

}

.selectOption-item {
    flex: 1 0 auto;
    background: #EDF5FF;
    border-radius: 5px;
    font-weight: 400;
    font-size: 14px;
    color: #1A76FF;
    padding: 8px 25px 8px 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    .play {
        transform: rotate(90deg);
        margin-left: 3px;
        position: absolute;
        right: 10px;
    }

    &:active {
        background: #d8dee6;
    }
}

.cusetName {
    font-size: 13px;
    padding: 3px var(--van-search-padding);
}

.stage {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: var(--van-cell-vertical-padding) var(--van-cell-horizontal-padding);
}

.history-record {
    font-size: 12px;
    color: #1A76FF;
    text-decoration: underline;
}

:deep(.van-collapse-item__content) {
    padding: 0;
}
</style>