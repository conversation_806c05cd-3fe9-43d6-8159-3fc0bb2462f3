<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />


  <link rel="icon" type="image/svg+xml" href="/taskHubIcon.svg" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0,user-scalable=no" />

  <title>标准化工作台</title>

  <link rel="stylesheet" href="//at.alicdn.com/t/c/font_4646322_lq12amjvbf7.css" />
  <!-- <script src="//at.alicdn.com/t/c/font_4646322_zj8yf0xg3uq.js"></script> -->

  <!-- 企业微信sdk -->
  <!-- <script src="https://unpkg.com/@wecom/jssdk"></script> -->

  <script type="text/javascript">
    window._AMapSecurityConfig = {
      securityJsCode: "d802d3c074125716cb737396406501d9",
    }
    async function getConfigSignature(url) {

      // 根据 url 生成企业签名
      // 生成方法参考 https://developer.work.weixin.qq.com/document/14924
      return { timestamp, nonceStr, signature }
    }
  </script>
  <script src="https://webapi.amap.com/loader.js"></script>

  <!-- 腾讯地图 -->
  <script src="https://3gimg.qq.com/lightmap/components/geolocation/geolocation.min.js"></script>
  <script src="https://map.qq.com/api/gljs?v=1.exp&key=SULBZ-D2ER4-HD4US-FQJXQ-G7YYV-CLFS6"></script>
  <script type="text/javascript"
    src="https://mapapi.qq.com/web/mapComponents/static/common/static/js/mod_0d3c97a.js"></script>
  <script type="text/javascript"
    src="https://mapapi.qq.com/web/mapComponents/geoLocation/v2/static/geolocation/static/pkg/geolocation_libs_b115dfc.js"></script>
  <style>
    [class*="icon-"],
    [name*="icon-"] {
      font-family: "iconfont";
      font-size: 16px;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
  </style>
</head>

<body>
  <div id="app"></div>
  <script type="module" src="/src/main.ts"></script>
</body>

</html>