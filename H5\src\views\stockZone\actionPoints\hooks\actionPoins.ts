

import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { getActionPointsCountAPI } from "@/api/home";
import { useRouter } from "vue-router";

export const actionPoinsHook = function () {
    const router = useRouter()
    const {
        formline,
        defaultDate,
        maxDate,
        calendarIsShow,
        onConfirm,
    } = searchHooks({
        source: 'actionPoins',
        callback: loadActionPointsCount
    })

     interface List {

    }




    const tableData = reactive({
        arrearsWinBack: 5520,
        dismantleOrder: 555,
        dismantleRetention: 785,
        noAppointmentAdd: 135,
        totalPoints: 0,
    })
    const resultOption = computed(() => {
        const maxValue = Math.max(
            tableData.noAppointmentAdd,
            tableData.arrearsWinBack,
            tableData.dismantleOrder,
            tableData.dismantleRetention
        ) * 1.2;

        return {
            title: {
                text: `总积分:${tableData.totalPoints}`,
                left: "center",
                bottom: 20,
                textStyle: {
                    fontSize: 16,
                    fontWeight: 500,
                    color: '#3D3D3D'
                },
            },
            textStyle: {
                color: '#3D3D3D',
                fontSize: 12,
            },
            tooltip: {
                trigger: 'item',
                axisPointer: {
                    type: 'line',
                },
            },
            radar: {
                shape: 'polygon',
                splitNumber: 3,
                radius: '50%',
                center: ['50%', '40%'],
                splitArea: { show: false },
                splitLine: {
                    lineStyle: { color: ['#D9D9D9'] },
                },
                axisLine: {
                    lineStyle: { color: '#D9D9D9' },
                    show: false
                },
                indicator: [
                    { name: '无约加约', min: 0, max: maxValue },
                    { name: '欠费赢回', min: 0, max: maxValue },
                    { name: '拆机录单', min: 0, max: maxValue },
                    { name: '拆机挽留', min: 0, max: maxValue },
                ],
            },
            series: [
                {
                    name: '雷达图',
                    type: 'radar',
                    symbol: 'circle',
                    symbolSize: 0, // 数据点的大小
                    areaStyle: {
                        color: '#F68018',
                        opacity: 0.4,
                    },
                    lineStyle: {
                        color: '#F68018',
                        width: 2,
                    },
                    emphasis: {
                        areaStyle: { opacity: 0.6 },
                    },
                    label: {
                        show: true,
                        fontSize: 12,
                        color: '#F68018',

                        rich: {
                            // 自定义样式的 "rich" 样式
                            pos1: {
                                //无约加约
                                padding: [-20, 0, 0, 0] 

                            },
                            pos2: {
                                //欠费赢回
                                padding: [0, 40, -10, 0] // 左偏移 10 像素
                            },
                            pos3: {
                                //拆机录单
                                padding: [0, 0, -30, 0] // 上偏移 10 像素
                            },
                            pos4: {
                                //拆机挽留
                                padding: [0, 0, -10, 40] 
                            },
                        },
                        formatter: (params: any) => {
                            const offsetMap = ['{pos1|', '{pos2|', '{pos3|', '{pos4|'];

                            console.log(params);

                            const offsetTag = offsetMap[params.dimensionIndex % offsetMap.length];
                            return `${offsetTag}${params.value}}`;
                        }
                    },
                    data: [
                        {
                            value: [
                                tableData.noAppointmentAdd,
                                tableData.arrearsWinBack,
                                tableData.dismantleOrder,
                                tableData.dismantleRetention
                            ],

                        },
                    ],
                },
            ],
        }
    })















    const actionOption = computed(() => {
        return {
            title: {
                text: "总积分:56565",
                left: "center",
                bottom: 20,
                textStyle: {
                    fontSize: 16,
                    fontWeight: 500,
                    color: '#3D3D3D'
                },
            },
            textStyle: {
                // 全局字体样式
                color: '#3D3D3D',
                fontSize: 12,
            },

            tooltip: {
                // 提示框组件
                trigger: 'item',
                axisPointer: {
                    type: 'line',
                    // shadowStyle: {
                    //     color: 'rgba(204, 214, 235, 0.247059)',
                    // },
                },
            },
            radar: {
                shape: 'polygon',
                splitNumber: 3,
                radius: '55%',

                splitArea: {
                    show: false,
                },
                splitLine: {
                    lineStyle: {
                        color: ['#D9D9D9'],
                    },
                },
                axisLine: {
                    lineStyle: {
                        color: '#D9D9D9',
                    },
                    show: false
                },
                indicator: [
                    {
                        name: '欠料',
                        max: 100,
                    },
                    {
                        name: '人员不够',
                        max: 100,
                    },
                    {
                        name: '机械故障',
                        max: 100,
                    },
                    {
                        name: '模具异常',
                        max: 100,
                    },
                    {
                        name: '来料不良',
                        max: 100,
                    },
                ],
            },
            series: [
                {
                    name: '雷达图',
                    type: 'radar',
                    symbol: 'circle', //  circle
                    symbolSize: 0, // 数据点的大小

                    areaStyle: {
                        color: '#F68018', // 调色盘颜色列表。
                        opacity: 0.4,
                    },

                    lineStyle: {
                        color: '#F68018', // 边框线颜色
                        width: 2,
                    },
                    emphasis: {
                        areaStyle: {
                            opacity: 0.6,
                        },
                    },
                    label: {
                        show: true, // 显示标签
                        fontSize: 12,
                        color: '#F68018',
                        // formatter: (params: any) => `${params.value}`, // 显示具体的值
                        formatter: (params: any, index: number) => {
                            const offsets = [10, -10, 15, -15] // 通过偏移量控制每个点的标签
                            const offset = offsets[index % offsets.length]
                            return `{a|${params.value}}`
                        },
                        rich: {
                            a: {
                                fontSize: 12,
                                color: '#F68018',
                                padding: [2, 4, 2, 4],
                            }
                        }
                    },
                    data: [
                        {
                            value: [50, 46, 80, 30, 60],
                        },
                    ],
                },
            ],
        }
    })

    function goToDetail() {
        router.push({
            path: '/actionPointsList',
            query: {
                startTime: formline.startTime ? formline.startTime : '',
                endTime: formline.endTime ? formline.endTime : '',
            },
        })
    }

    function loadActionPointsCount() {
        let params = {
            startTime: formline.startTime ? `${formline.startTime} 00:00:00` : undefined,
            endTime: formline.endTime ? `${formline.endTime} 23:59:59` : undefined,
        }
        getActionPointsCountAPI(params).then((res: any) => {
            tableData.arrearsWinBack = res.data.arrearsWinBack
            tableData.dismantleOrder = res.data.dismantleOrder
            tableData.dismantleRetention = res.data.dismantleRetention
            tableData.noAppointmentAdd = res.data.noAppointmentAdd
            tableData.totalPoints = res.data.totalPoints

        })

    }



    return {
        resultOption,
        actionOption,
        tableData,
        goToDetail,

        formline,
        defaultDate,
        maxDate,
        calendarIsShow,
        onConfirm,
        loadActionPointsCount,
    }

}


