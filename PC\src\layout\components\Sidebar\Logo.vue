<template>
  <div class="sidebar-logo-container" :class="{ collapse: collapse }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <img src="@/assets/logo.svg" class="sidebar-logo" />
        <!-- <svg-icon icon='zu' :size="18" style="height: 45px;"></svg-icon>
        <h1 class="sidebar-title">{{ title }} </h1> -->
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <img src="@/assets/logo.svg" class="sidebar-logo" />
        <!-- <svg-icon icon='zu' :size="18" style="height: 45px;"></svg-icon> -->
        <h1 class="sidebar-title">{{ title }} </h1>
      </router-link>
    </transition>
  </div>
</template>

<script setup>
import { ref, reactive } from "vue";
const props = defineProps({
  collapse: {
    type: Boolean,
    required: true,
  },
});

// console.log(props.collapse);

const title = "标准工作台";
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 50px;
  line-height: 50px;
  //   background: #2b2f3a;
  // text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    // display: flex !important;
    // align-items: center;
    // justify-content: center;
    // flex-wrap: wrap;
    margin-left: 15px;

    & .sidebar-logo {
      width: 26px;
      height: 45px;
      line-height: 45px;
      // vertical-align: middle;
      margin-right: 5px;
    }

    & .sidebar-title {
      display: inline-block;
      margin: 0;
      margin-left: 0px;
      color: #fff;
      font-weight: 600;
      font-size: 23px;
      height: 45px;
      line-height: 45px;
      font-style: italic;
      font-family: Source Han Sans, Source Han Sans;
      vertical-align: text-bottom;
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0px;
    }
  }
}
</style>
