<template>
    <div>
        <div class="card-header">
            <div class="selectOption-item" @click="calendarIsShow = true">
                <span>{{ time ? time : '请选择时间' }}</span>
                <van-icon name="play" class="play" color="#1A76FF" />
            </div>
        </div>
        <div class=' container_ mt-6'>
            <div class="container__header">
                <div>
                    <img src="@/assets/computed.png" alt="" srcset="">
                    <span class="ml-6">以店包片</span>
                </div>

            </div>
            <div class="table-container">

                <table>
                    <thead>

                        <tr>
                            <th rowspan="2" class="sticky">门店</th>


                            <th colspan=" 2">指标</th>
                            <th colspan=" 6">控流失</th>
                            <th colspan=" 6">提价值</th>
                            <th colspan="2">激励试算</th>


                        </tr>
                        <tr>
                            <th>控流失</th>
                            <th>提价值</th>

                            <th>高网</th>
                            <th>低迁50-</th>
                            <th>低迁50+</th>
                            <th>低迁转非新装高套</th>
                            <th>高/低折算</th>
                            <th>三天赢回+</th>


                            <th>20+</th>
                            <th>{{ `【10,20)` }}</th>
                            <th>(0,10)</th>
                            <th>平续</th>
                            <th>提/续新装高套</th>
                            <th>提/续折算</th>


                            <th>控流失</th>
                            <th>提价值</th>


                        </tr>
                    </thead>
                    <tbody>


                        <tr v-for="(row, index) in tableData" :key="index">
                            <td class="sticky">
                                <div class="store ">{{ row.channelName }}</div>
                            </td>
                            <td>{{ row.controlLossTarget }}</td>
                            <td>{{ row.valueIncreaseTarget }}</td>
                            <td>{{ row.kdcjNum }}</td>
                            <td>{{ row.diqian50m }}</td>
                            <td>{{ row.diqian50a }}</td>
                            <td>{{ row.diqian50To129 }}</td>
                            <td>{{ row.lowMigrationDiscount }}</td>
                            <td>{{ row.pastDueOrderRecoveryRate }}</td>
                            <td>{{ row.increaseValueAbove20 }}</td>
                            <td>{{ row.increaseValue10To20 }}</td>
                            <td>{{ row.increaseValue0To10 }}</td>
                            <td>{{ row.migration }}</td>
                            <td>{{ row.increaseRenewMidHighValue }}</td>
                            <td>{{ row.increaseRenewDiscount }}</td>
                            <td>{{ row.incentiveTrialCalculationControlLoss }}</td>
                            <td>{{ row.incentiveTrialCalculationValueIncrease }}</td>
                        </tr>


                    </tbody>
                </table> 
            </div>
            <van-empty description="暂无数据" class="mt-8" style="background-color: #fff;border-radius: 15px;"
                v-if="tableData.length == 0" />
        </div>

        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="single" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />
    </div>
</template>

<script setup lang="ts">
import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { getStorePackageListAPI } from "@/api/home";

interface List {
    channelName: string;
    controlLossTarget: string; // 指标-控流失
    valueIncreaseTarget: string; // 指标-提价值
    kdcjNum: number; // 控流失-离网
    diqian50m: number; // 控流失-低迁50-
    diqian50a: number; // 控流失-低迁50+
    diqian50To129: number; // 控流失-低迁转非新装高套
    lowMigrationDiscount: number; // 控流失-离/低折算
    pastDueOrderRecoveryRate: number; // 控流失-欠费派单赢回率
    increaseValueAbove20: number; // 提价值-20+
    increaseValue10To20: number; // 提价值-10-20
    increaseValue0To10: number; // 提价值-0-10
    migration: number; // 提价值-平续
    increaseRenewMidHighValue: number; // 提价值-提/续新装高套
    increaseRenewDiscount: number; // 提价值-提/续折算
    incentiveTrialCalculationValueIncrease: number; // 激励试算-提价值
    incentiveTrialCalculationControlLoss: number; // 激励试算-控流失
}

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    onConfirm,


} = searchHooks({
    source: 'storePackage',
    callback: loadStorePackageList
})




const tableData = ref<List[]>([]);



const time = computed(() => {
    if (formline.startTime && formline.endTime) return `${formline.startTime}`
    else return ``
})

function loadStorePackageList() {
    let params = {
        startTime: formline.startTime ? `${formline.startTime} 00:00:00` : '',
        endTime: formline.endTime ? `${formline.endTime} 23:59:59` : '',
    }
    getStorePackageListAPI(params).then((res: any) => {
        if (res.code == 200) {
            tableData.value = res.data || []
        }
    })
}


onMounted(() => {
    loadStorePackageList()
})
</script>

<style lang="scss" scoped>
.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

    .selectOption-item {
        flex: 1 0 auto;
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;
        // justify-content: space-between;
        position: relative;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
            position: absolute;
            right: 10px;
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }
}


.table-container {
    width: 100%;
    overflow-x: auto;
    // padding: 8px;

    table {
        width: 100%;
        border-collapse: collapse;
        text-align: center;
        font-size: 13px;

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            white-space: nowrap; // 禁止换行
            // white-space: normal;
            /* 允许换行 */
        }

        .sticky {
            /* 固定表格内容的门店列 */
            position: sticky;
            left: 0;
            background-color: #fff;
            /* 设置背景颜色以覆盖其他列 */
            z-index: 1;
            /* 确保门店列在最上层 */
        }


        .store {
            width: 120px;
            white-space: normal;
        }

        .main-title {
            font-size: 13px;
            font-weight: bold;
            padding: 12px;
            background-color: #2e70ba;
            color: #fff;
            text-align: center;
        }

        thead tr:nth-child(1) th {
            background-color: #2e70ba;
            color: #fff;
            font-weight: bold;
            padding: 10px;
            text-align: center;
        }

        thead tr:nth-child(2) th {
            background-color: #2e70ba;
            color: #fff;
            font-weight: bold;
            padding: 8px;
        }

        tbody tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        tbody tr:hover {
            background-color: #e6f7ff;

            .sticky {
                background-color: #e6f7ff !important;
            }
        }

        tbody td.highlight {
            background-color: #ffe58f;
            font-weight: bold;

        }

    }
}
</style>