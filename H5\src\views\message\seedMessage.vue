<template>
    <div class="container_">
        <div class="container__header">
            <div>
                <cusTabs style="gap: 10px;" :tabList="tabList" v-model:curSelectTab="curSelectTab"
                    @changeSelectTab="changeSelectTab">
                </cusTabs>

            </div>


            <div style="display: flex;align-items: center;" @click="seedMessage">
                <span class="fs14" style="color: #5F8CE6;">

                    <van-icon name="plus" size="16" />
                    <span>发消息</span>
                </span>
            </div>
        </div>
        <div class="container_body ">
            <div v-if="messageList.length > 0" class="message-list">
                <van-list v-model:loading="loading" :finished="finished"
                    :finished-text="messageList.length > 0 ? '没有更多了' : ''" @load="onLoad">
                    <div v-for="(item, index) in messageList" :key="index" class="message-item"
                        @click="gotoDetail(item)">
                        <div class="message-icon">
                            <img src="@/assets/defaultAvatar.png" alt="">
                        </div>
                        <div style="flex: 1 0 auto;">
                            <div class="message-content">
                                <div class="message-title">{{ item.title }}</div>

                                <div class="message-action" v-if="curSelectTab == '草稿箱'">
                                    <span @click="handleRead(item)">继续编辑</span>
                                    <van-icon name="arrow" color="#327FF0" />
                                </div>


                            </div>
                            <div style="display: flex;justify-content: space-between;align-items: center;">
                                <div class="message-time mb-0">{{ item.createTime }}</div>
                                <span v-if="curSelectTab == '已发送'"> <van-icon size="12" color="#919191"
                                        name="browsing-history-o" /> <span style="color: #919191;" class="fs12">{{
                                            item.readCount ?? 0 }}人已读</span></span>
                                <van-icon v-else-if="curSelectTab == '发送中'" name="delete-o" size="19" color="#666"
                                    @click="removeMessage(item)" />
                            </div>

                            <div class="message-desc mt-4">{{ item.content }}</div>
                        </div>


                    </div>
                </van-list>
            </div>

            <van-empty v-else image-size="160" description="暂时没有更多提醒啦~">
                <template #image>
                    <img src="./assets//emptyMessage.svg" alt="" srcset="">
                </template>
            </van-empty>

        </div>


    </div>
</template>

<script setup lang="ts">
import { getMessageListAPI, deleteDraftAPI } from "@/api/message";
import { formatDate } from "@/utils/loadTime";
import cusTabs from "@/components/cusTabs/index.vue";
import { ref, onMounted } from 'vue';
import { useRouter } from "vue-router";
import { showToast } from 'vant';
interface MessageItem {
    id: number;
    title: string;
    content: string;
    type: string;
    status: number; // 0: 未读, 1: 已读
    createTime: string;
    readCount: number //已读次数
}

const router = useRouter()



const props = defineProps({
    seedMessageType: {
        type: String,
        default: '已发送'
    }
})
const loading = ref(false)
const finished = ref(true)
const total = ref(0);
const pageNum = ref(1);



const tabList = ref([
    { text: '已发送', value: '已发送' },
    { text: '发送中', value: '发送中' },
    { text: '草稿箱', value: '草稿箱', isShowBadge: true, messageCount: 0 },
])
const curSelectTab = ref('已发送')
const messageList = ref<MessageItem[]>([])



enum IsUnread {
    '发送中' = 2,
    '已发送' = 1,
    '草稿箱' = 0,
}
// 获取消息列表
const getMessageList = async () => {

    let params = {
        sendFlag: IsUnread[curSelectTab.value as keyof typeof IsUnread],
        pageNum: pageNum.value,
        pageSize: 10
    }
    loading.value = true
    getMessageListAPI(params).then((res: any) => {
        if (res.code == 200) {
            messageList.value = messageList.value.concat(res.data.records)
            total.value = res.data.total
            if (curSelectTab.value == '草稿箱') {
                tabList.value[2].messageCount = res.data.total
            }
            if (total.value <= messageList.value.length) {
                finished.value = true
            } else {
                finished.value = false
            }

        }

    }).finally(() => {
        loading.value = false
    })

}





// 继续编辑
const handleRead = async (item: MessageItem) => {
    router.push({ path: '/editMessage', query: { id: item.id } })
}

// 删除发送中的消息
function removeMessage(row: any) {
    deleteDraftAPI({ id: row.id }).then((res: any) => {
        if (res.code == 200) {
            showToast('删除成功')
            messageList.value = messageList.value.filter((item: any) => item.id != row.id)
            if (messageList.value.length == 0) {
                pageNum.value = 1;
                getMessageList()
            }
        } else {
            showToast(res.msg)
        }
    })
}

// 发送消息
const seedMessage = async () => {
    router.push({ path: '/editMessage', })

}

// 切换标签
function changeSelectTab(params: any) {
    total.value = 0;
    pageNum.value = 1;
    messageList.value = []
    getMessageList()
}

function gotoDetail(row: any) {
    if (curSelectTab.value == '已发送') {
        router.push({ path: '/personalMessageDetail', query: { id: row.id } })
    }


}



function onLoad() {
    pageNum.value = pageNum.value + 1
    getMessageList()
}

onMounted(() => {


    curSelectTab.value = props.seedMessageType


    getMessageList()
})
</script>

<style lang="scss" scoped src="./index.scss">
.message-item {
    padding: 14px 0px;
}
</style>