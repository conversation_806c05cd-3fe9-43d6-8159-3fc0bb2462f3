import axios, { AxiosInstance, AxiosRequestConfig, InternalAxiosRequestConfig, AxiosResponse } from "axios";
import { showNotify } from "vant";
import { getToken, removeToken } from "@/utils/auth.ts";
import { CryptoParams } from '@/utils/crypto/index'
import router from "@/router/index";
import { useUserInfo } from "@/stores/userInfo.ts";
import configAxios from "@/api/config"
import { extractPathPart } from '@/utils/extractPathPart'
import { Decrypt, DecryptWithHKDF } from "@/utils/crypto/index";

// 配置新建一个 axios 实例
const service: AxiosInstance = axios.create({
    timeout: 50000,
    headers: {
        "Content-Type": "application/json",
        'terminal-type': "H5"
    },
});

// 添加请求拦截器
service.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
        const userInfoStore = useUserInfo()
        let crypto = extractPathPart(Object.values(configAxios), config.url || "")
        // 在发送请求之前做些什么 token
        let TokenKey = userInfoStore.getToken;
        config.headers!["Authorization"] = TokenKey || '';

        let params = config.params || {};
        let data = config.data || {}
        let result: Object
        // 根据缓存中的加密字段进行匹配进行加密
        if (Object.keys(params).length) {
            result = params
        } else {
            result = data
        }

        // 组装数据
        let configParams = {
            ...result,
            url: crypto,
        }
        // 加密合并请求
        Object.assign(config.headers, CryptoParams(configParams))
        // console.log('请求suc拦截--ceptor(可以根据不同场景往header中新增字段，比如签名)', config)
        return config
    },
    (error) => {
        // 对请求错误做些什么
        return Promise.reject(error);
    }
);

// 添加响应拦截器
const responseWhiteList = ['/o/']  //响应白名单

service.interceptors.response.use(
    async (response: AxiosResponse) => {
        const userInfoStore = useUserInfo()


        const _response = response.data



        // Owbi2eNNgLoL
        if (_response.code == 200) {

            if (_response.encrypted == 1) {
                const AESKEY = userInfoStore.AESKEY
                _response.data = JSON.parse(await DecryptWithHKDF(_response.data, AESKEY))
            }
            console.log(_response);
            
            return _response
        }
        if (_response.code == 420) {
            // removeToken();
            userInfoStore.removeToken()
            return
        }
        if (_response.code == 'B005') {
            showNotify({
                message: `${_response.msg}`
            })

            return Promise.reject(_response);
        }

        if (_response.code == 401) {
            showNotify({
                message: '权限失效！'
            })
            userInfoStore.logOut()

            // 理应直接关闭当前页面，提醒企业微信重新登陆---待开发
            router.push('/IDNotFound')
            return Promise.reject(_response);
        }

        return _response || '';
    },
    (error) => {
        const userInfoStore = useUserInfo()
        if (error.response.data.code == 420) {
            userInfoStore.removeToken()
        }
        return Promise.reject(error);
    }
);

// 导出 axios 实例
export default service;
