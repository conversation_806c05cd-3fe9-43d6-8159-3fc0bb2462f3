<template>
    <div class="card container_">
        <div class="container__header">
            <div>
                <img src="../assets/touchpointDetail.svg" alt="" srcset="" />
                <span class="ml-6">工作写实记录</span>
            </div>
        </div>
        <van-list v-model:loading="loading" :finished="finished" :finished-text="tableData.length > 0 ? '没有更多了' : ''"
            @load="onLoad">
            <div class="content" v-for="(item, index) in tableData" :key="index">
                <div class="header">
                    <div class="font font_date">{{ item.date }}</div>
                    <div class="tag" :class="item.auditStatus === 1 ? 'pass' : 'fail'">
                        {{ item.auditStatus === 1 ? '已通过' : '未通过' }}
                    </div>
                </div>
                <div class="font_row">
                    <div class="font font_label">工作时间</div>
                    <div class="font font_title">{{ item.time }}</div>
                </div>
                <div class="font_row">
                    <div class="font font_label">工作地址 <van-icon name="location-o" color="#1A76FF" /></div>
                    <div class="font font_title">{{ item.address }}</div>
                </div>
                <div class="imgList">
                    <div class="font font_label">工作照片</div>
                    <div class="font font_value">
                        <van-image width="100" :radius="5" v-imgPreview="[item.images, indexE]" lazy-load height="100"
                            :src="el" v-for="el, indexE in item.images" />
                    </div>
                </div>
                <div class="font_row">
                    <div class="font font_label">工作写实备注</div>
                    <div class="font font_title">{{ item.description }}</div>
                </div>
            </div>
        </van-list>
        <van-empty description="暂无数据" style="background-color: #fff;" v-if="tableData.length == 0" />
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { touchHooks } from "@/views/touchpoint/touchHooks.ts";



const {
    loadList,
    onLoad,
    tableData,
    loading,
    finished,
} = touchHooks()



onMounted(() => {
    loadList()
})

</script>

<style lang="scss" scoped>
.card {
    min-height: 100%;
}

.container_ {
    background-color: transparent;
    box-shadow: none;
}

.container__header {
    background: #fbfcff;
}

.content {
    margin-bottom: 16px;
    background: #fbfcff;
    border-radius: 0px 0px 10px 10px;
    padding-bottom: 30px;

    .header {
        padding: 15px 18px;
        border-bottom: 1px solid #edf5ff;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .tag {


            box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
            border-radius: 5px;
            padding: 5px 11px;
            font-weight: 500;
            font-size: 14px;

        }

        .pass {
            background: #EDF5FF;
            color: #247CFF;
        }

        .fail {
            background: #FFEEED;
            color: #FF2424;
        }

    }

}

.content:not(:first-child) {
    border-radius: 10px;
}

.font_value {
    text-align: right;
}



.font {
    font-family: Source Han Sans, Source Han Sans;
    font-style: normal;
    text-transform: none;
}

.font_date {
    font-size: 18px;
    line-height: 20px;
    color: #1a76ff;
    text-align: center;
    font-weight: 500;
}



.font_label {
    padding: 15px 18px;
    font-weight: 500;
    font-size: 14px;
    color: #3D3D3D;
    line-height: 16px;
}

.imgList {
    border-bottom: 1px solid #EEF5FF;
    padding-bottom: 30px;
}

.font_value {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    place-items: center;
    padding: 15px 18px;
    padding-top: 0px;

    img {
        border-radius: 5px;
    }
}


.font_row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #EDF5FF;
    padding-right: 18px
}

.font_title {
    font-weight: 400;
    font-size: 14px;
    color: #A8A8A8;
}
</style>