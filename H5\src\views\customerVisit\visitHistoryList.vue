<template>
    <div>
        <div class="card-header">
            <div class="selectOption">
                <van-popover :actions="tabList" @select="onSelect" placement="bottom">
                    <template #reference>
                        <div class="selectOption-item">
                            <span>{{ curSelectType.text }}</span>
                            <van-icon name="play" class="play" color="#1A76FF" />
                        </div>
                    </template>
                </van-popover>
            </div>
            <div class="selectOption-item" @click="calendarIsShow = true">
                <span class="van-ellipsis">{{ time ? time : '请选择时间' }}</span>
                <van-icon name="play" class="play" color="#1A76FF" />
            </div>
        </div>
        <div class=' container_ mt-6' :style="{
            background: tableList.length == 0 ? '#FBFCFF' : 'transparent',
            boxShadow: tableList.length == 0 ? '0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF' : 'none'
        }">
            <div class="list-container">
                <van-list v-model:loading="loading" :finished="finished"
                    :finished-text="tableList.length > 0 ? '没有更多了' : ''" error-text="请求失败，点击重新加载" @load="onLoad">
                    <div class="solution-card" v-for="(item, index) in tableList" :key="item.id">
                        <div class="company-name">
                            <span class="name van-multi-ellipsis--l2"> {{ item.workAddress }}</span>
                            <span class="date">{{ item.createTime }}</span>
                        </div>
                        <div style="display: flex;align-items: center;justify-content: space-between;">
                            <div class="item-type">{{ item.workNoteType }}</div>
                            <div class="mt-8 fs13" v-if="item.customerName">客户名称：{{ item.customerName }}</div>
                        </div>

                        <div class="mt-8 fs13" v-if="item.customerName">客户名称：{{ item.customerName }}</div>


                        <div class="images">
                            <van-image fit="cover" v-for="el, i in item.images" lazy-load :src="el"
                                v-imgPreview="[item.images, i]" />
                        </div>

                        <div class="description">
                            <van-text-ellipsis rows="2" :content="item.workNote" expand-text="展开" collapse-text="收起"
                                position="middle" />
                        </div>
                    </div>
                </van-list>
                <van-empty description="暂无数据" class="mt-8" style="background-color: #FBFCFF;border-radius: 15px;"
                    v-if="tableList.length == 0" />
            </div>


        </div>

        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />
    </div>
</template>

<script setup lang="ts">
interface Solution {
    id: string;
    workAddress: string;
    createTime: string;
    workNoteType: string;
    customerName: string;
    images: string[];
    workNote: string;
}
import { getVisitListAPI } from "@/api/customerVisit";
import { getDictInfoAPI } from "@/api/common";
import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { useRoute } from "vue-router";
const route = useRoute()

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    onConfirm,
} = searchHooks({
    source: 'visitHistoryList',
    callback: changeTime
})




const loading = ref(false)
const finished = ref(true)
const pageSize = ref(10);
const total = ref(0);
const pageNum = ref(1);


const tableList = ref<Solution[]>([])


function changeTime() {
    tableList.value = []
    loadList()
}

function loadList() {
    let params = {
        workNoteTypeCodes: curSelectType.value ? [curSelectType.value] : [],
        startTime: formline.startTime ? `${formline.startTime} 00:00:00` : undefined,
        endTime: formline.endTime ? `${formline.endTime} 23:59:59` : undefined,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
    }
    loading.value = true
    getVisitListAPI(params).then((res: any) => {
        if (res.code == 200) {
            for (const item of res.data.records || []) {
                item.images = (item.images ? item.images.split(',') : []).map((el: string) => el)
            }

            tableList.value = [...tableList.value, ...res.data.records]
            total.value = res.data.total
            if (total.value <= tableList.value.length) {
                finished.value = true
            } else {
                finished.value = false
            }
        }
    }).finally(() => {
        loading.value = false
    })
}







const time = computed(() => {
    if (formline.startTime && formline.endTime) return formline.startTime == formline.endTime ? `${formline.startTime}` : `${formline.startTime}~${formline.endTime}`
    else return ``
})


const tabList = ref<{ text: string, value: string }[]>([])
const curSelectType = reactive<{ text: string, value: string }>({
    text: '全部类型',
    value: ''
})

function onSelect(item: any) {
    curSelectType.value = item.value
    curSelectType.text = item.text
    pageNum.value = 1
    pageSize.value = 10
    total.value = 0
    tableList.value = []
    loadList()
}
function onLoad() {
    pageNum.value = pageNum.value + 1
    loadList()
}

async function initDictInfo() {
    await getDictInfoAPI('customerVisit').then((res: any) => {
        if (res.code == 200) {
            tabList.value = res.data.map((item: any) => ({ text: item.dictName, value: item.dictCode }))
            tabList.value.unshift({
                text: '全部类型',
                value: ''
            })
        }

    })
}



onMounted(() => {

    initDictInfo()
    loadList()
})
</script>

<style lang="scss" scoped>
.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 10px;

    &>div {
        width: 50%;
        max-width: 50%;
    }

    .selectOption-item {
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;

        display: flex;
        justify-content: space-between;
        align-items: center;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;

        .play {
            transform: rotate(90deg);
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }
}




.solution-card {
    border-radius: 10px;
    background: #FFFFFF;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    padding: 11px 15px 15px;
    margin-bottom: 10px;

}

.company-name {
    font-weight: 700;
    font-size: 14px;
    color: #3D3D3D;
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.date {
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    flex: 0 0 auto;
}

.item-type {
    display: inline-block;
    font-weight: 400;
    font-size: 12px;
    line-height: 13px;
    color: #217BFF;
    background: #EDF5FF;
    border-radius: 2px;
    padding: 3px;
    margin-top: 9px;
}

.images {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 5px;
    margin-top: 14px;
    margin-bottom: 11px;

    &>.van-image {
        width: 100%;
        height: 100px;
        border-radius: 5px;
        overflow: hidden;
    }
}

.description {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 13px;
}










:deep(.van-popover__wrapper) {
    display: block;
}

:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
    width: auto;
}
</style>