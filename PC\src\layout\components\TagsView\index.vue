<template>
  <div id="tags-view-container" class="tags-view-container">
    <ScrollPane ref="scrollPane" class="tags-view-wrapper" @scroll="handleScroll">
      <router-link v-for="tag in visitedViews" :key="tag.path" :class="{ active: isActive(tag) }"
        :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }" tag="span" class="tags-view-item"
        @click.middle="!isAffix(tag) ? closeSelectedTag(tag) : ''" @contextmenu.prevent="openMenu(tag, $event)">
        <span>
          {{ tag.meta.title }}
        </span>
        <el-icon v-if="!isAffix(tag)" @click.prevent.stop="closeSelectedTag(tag)" class="icon-close">
          <Close />
        </el-icon>
      </router-link>
    </ScrollPane>
    <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <li @click="refreshSelectedTag(selectedTag)">
        刷新
      </li>
      <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">
        关闭
      </li>
      <li @click="closeOthersTags">关闭其他</li>
      <li @click="closeAllTags(selectedTag)">关闭所有</li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import ScrollPane from './ScrollPane.vue';
import { useViewsStore } from '@/stores/layoutSetting/tagsView.ts';
import path from 'path-browserify';

const route = useRoute();
const router = useRouter();
const tagsViewStore = useViewsStore();

interface Tag {
  path: string;
  name: string;
  meta: {
    title?: string;
    noCache?: boolean;
    affix?: boolean;
  };
  query?: Record<string, any>;
  fullPath?: string;
}

const visible = ref(false);
const top = ref(0);
const left = ref(0);
const selectedTag = ref<Tag | null>(null);
const affixTags = ref<Tag[]>([]);
const visitedViews = computed(() => tagsViewStore.visitedViews);



const isActive = (tag: Tag) => tag?.path === route.path;
const isAffix = (tag: Tag) => tag?.meta?.affix || false;

const filterAffixTags = (routes: any, basePath = '/') => {
  let tags: Tag[] = [];
  routes.forEach((route: any) => {
    if (route.meta && route.meta.affix) {
      const tagPath = path.resolve(basePath, route.path);
      tags.push({
        fullPath: tagPath,
        path: tagPath,
        name: route.name,
        meta: { ...route.meta },
      });
    }
    if (route.children) {
      const tempTags = filterAffixTags(route.children, route.path);
      if (tempTags.length >= 1) {
        tags = [...tags, ...tempTags];
      }
    }
  });
  return tags;
};

const initTags = () => {
  const affixTagsList = filterAffixTags(router.options.routes);
  affixTags.value = affixTagsList;
  affixTagsList.forEach(tag => {
    if (tag.name) {
      tagsViewStore.addVisitedView(tag);
    }
  });
};

const addTags = () => {
  const { name } = route;
  if (name) {
    tagsViewStore.addView({
      path: route.path,
      name: name, // 确保这里是一个 string
      meta: route.meta,
      query: route.query,
      fullPath: route.fullPath,
    });
  }
}


const moveToCurrentTag = () => {
  const tags = document.querySelectorAll('.tags-view-item');
  tags.forEach(tag => {
    const tagElement = tag as HTMLAnchorElement;
    if (tagElement.getAttribute('href') === route.fullPath) {
      const scrollPane = document.querySelector('.scroll-pane') as HTMLElement;
      if (scrollPane) {
        scrollPane.scrollTo({ top: tagElement.offsetTop, behavior: 'smooth' });
      }
    }
  });
};

const refreshSelectedTag = (view: Tag) => {
  tagsViewStore.delCachedView(view)
  router.replace(view.fullPath!);
};

const closeSelectedTag = (view: Tag) => {
  tagsViewStore.delView(view).then(({ visitedViews }) => {
    if (isActive(view)) {
      toLastView(visitedViews, view);
    }
  });
};

const closeOthersTags = () => {
  router.push(selectedTag.value?.fullPath!);
  tagsViewStore.delOthersViews(selectedTag.value!).then(()=>{
    moveToCurrentTag()
  })

};

const closeAllTags = (view: Tag) => {
  tagsViewStore.delAllViews().then(({ visitedViews }) => {
    if (!affixTags.value.some(tag => tag.path === view.path)) {
      toLastView(visitedViews, view);
    }
  });
};

const toLastView = (visitedViews: Tag[], view: Tag) => {
  const latestView = visitedViews.slice(-1)[0];
  if (latestView) {
    router.push(latestView.fullPath!);
  } else {
    if (view.name === 'Home') {
      router.replace('/Home');
    } else {
      router.push('/');
    }
  }
};
const openMenu = (tag: Tag, e: MouseEvent) => {
  const tagsViewContainer = document.querySelector('#tags-view-container') as HTMLElement
  const menuMinWidth = 105;
  const offsetLeft = tagsViewContainer.getBoundingClientRect().left 
  const offsetWidth = tagsViewContainer?.offsetWidth;
  const maxLeft = offsetWidth - menuMinWidth;
  const leftValue = e.clientX - offsetLeft + 15;
  left.value = leftValue > maxLeft ? maxLeft : leftValue;
  top.value = e.clientY;
  visible.value = true;
  selectedTag.value = tag;
};

const closeMenu = () => {
  visible.value = false;
};

const handleScroll = () => {
  closeMenu();
};

watch(route, () => {
  addTags();
  moveToCurrentTag();
});

watch(visible, (value) => {
  if (value) {
    document.body.addEventListener('click', closeMenu);
  } else {
    document.body.removeEventListener('click', closeMenu);
  }
});

onMounted(() => {
  initTags();
  addTags();
});
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: 34px;
  width: 100%;
  background: #fff;
  border-bottom: 1px solid #d8dce5;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);

  .tags-view-wrapper {
    .tags-view-item {
      display: inline-block;
      position: relative;
      cursor: pointer;
      height: 26px;
      line-height: 26px;
      border: 1px solid #d8dce5;
      color: #495060;
      background: #fff;
      padding: 0 8px;
      font-size: 12px;
      margin-left: 5px;
      margin-top: 4px;

      &:first-of-type {
        margin-left: 15px;
      }

      &:last-of-type {
        margin-right: 15px;
      }

      &.active {
        background-color: #42b983;
        color: #fff;
        border-color: #42b983;

        &::before {
          content: "";
          background: #fff;
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          position: relative;
          margin-right: 2px;
        }
      }
    }
  }

  .contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);

    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;

      &:hover {
        background: #eee;
      }
    }
  }
}
</style>
<style lang="scss">
//reset element css of el-icon-close
.tags-view-wrapper {
  .tags-view-item {
    .icon-close {
      width: 16px;
      height: 16px;
      // vertical-align: 2px;
      vertical-align: middle;
      margin-left: 5px;
      border-radius: 50%;
      text-align: center;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transform-origin: 100% 50%;

      &:before {
        transform: scale(0.6);
        display: inline-block;
        vertical-align: -3px;
      }

      &:hover {
        background-color: #b4bccc;
        color: #fff;
      }
    }
  }
}
</style>
