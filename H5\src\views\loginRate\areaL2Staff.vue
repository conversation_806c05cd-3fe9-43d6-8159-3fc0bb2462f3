<template>
    <div>
        <div class="card-header">
            <van-popover :actions="areaList" v-if="route.query.areaL2Id" @select="changArea" placement="bottom-start">
                <template #reference>
                    <div class="selectOption-item">
                        <span>{{ curSelectArea.areaL2Name }}</span>
                        <van-icon name="play" class="play" color="#1A76FF" />
                    </div>
                </template>
            </van-popover>



            <van-search v-model="searchName" show-action class="searchName" placeholder="搜索姓名">
                <template #action>
                    <div class="button" @click="searchByName">搜索</div>
                </template>
                <template #left-icon>

                </template>
            </van-search>


        </div>
        <div class=' container_ mt-6'>
            <div class="container__header">
                <div>
                    <img src="@/assets/resultOptimization.svg" alt="" srcset="">
                    <span class="ml-6">{{ title }}</span>
                </div>
            </div>
            <div class="container_body " v-loading="loadingConfig">
                <div class="optionList">
                    <div class="list-item" v-for="item, index in tabList" :key="index">
                        <img src="@/assets/defaultAvatar.png" alt="">
                        <div class="ml-6">
                            <div class="name">
                                <span v-getName="{ item: item }">{{ item.staffName }}</span>
                                <span class="fs10 ml-5"> {{ item.staffCode }}</span>
                            </div>
                            <div class="staffRole">{{ item.staffRole }}</div>
                        </div>
                        <div class="islogin" :class="[item.status == 1 ? 'primary' : 'danger']">
                            {{ item.status == 1 ? '今日已登录' : '今日未登录' }}
                        </div>
                    </div>

                    <van-empty description="暂无数据" v-if="tabList.length == 0" />

                </div>

            </div>




        </div>
    </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { getlistDeptOrSubOptions } from "@/hooks_modules/getlistDeptOrSubOptions";
import { mapSort } from "@/utils/mapSort";
import { getSignByAreaL2ListAPI } from "@/api/morning";
import { debounce } from "@/utils/debounce";
import { useUserInfo } from "@/stores/userInfo";
import { getLoginDetailAPI } from "@/api/home";

interface LoginData {
    loginCount: number; // 登录次数
    loginRate: number;  // 登录率（百分比）
    totalCount: number; // 总人数
    unLoginCount: number; // 未登录人数
    unLoginRate: number; // 未登录率（百分比）
    [key: string]: any
}
interface AreaData extends LoginData {
    areaId: string | null;     // 区域 ID（可能为空）
    areaIdL2: string | null;          // 二级区域 ID
    areaName: string | null;   // 区域名称（可能为空）
    areaNameL2: string;        // 二级区域名称
    staffName: string | null;  // 员工姓名（可能为空）
    staffRole: string | null;  // 员工角色（可能为空）
    status: string | number;     // 状态（可能为空）
}
const userInfoStore = useUserInfo()
const route = useRoute()
const { areaList } = getlistDeptOrSubOptions(
    {
        areaId: String(route.query.areaId ?? ''),
        isPushAll: true,
        isNotSort: true
    })

const loadingConfig = reactive<LoadingDirectiveOptions>({
    visible: false, // 控制加载状态
    text: '请稍等...', // 自定义加载文本
    iconSize: '36',
    iconColor: '#fff',
    borderRadius: '10px',
});

const searchName = ref('')
const curSelectArea = reactive({
    areaId: String(route.query.areaId ?? ''),
    areaName: String(route.query.areaName ?? ''),
    areaL2Id: String(route.query.areaL2Id ?? ''),
    areaL2Name: String(route.query.areaL2Name ?? ''),
})

const tabList = ref<AreaData[]>([])

const searchByName = debounce(() => {
    loadLoginDetail()
}, 300, true)


function changArea(params: any) {
    searchName.value = ''
    curSelectArea.areaL2Id = params.value
    curSelectArea.areaL2Name = params.text
    loadLoginDetail()
}


const title = computed(() => {
    const showOrgName = userInfoStore.userInfo.showOrgName
    return `南京分公司${route.query.areaName ?? showOrgName}区${curSelectArea.areaL2Name}登录情况`
})

//获取今日登录列表
async function loadLoginDetail() {
    loadingConfig.visible = true
    let params = {
        areaIdL2: curSelectArea.areaL2Id == '' ? undefined : curSelectArea.areaL2Id,
        staffName: searchName.value
    }
    const res: any = await getLoginDetailAPI(params)
    loadingConfig.visible = false
    if (res.code == 200) {
        if (res.data.length > 0) {

            res.data = mapSort(res.data, 'staffRole')

        }

        tabList.value = res.data
    }
}


onMounted(() => {
    loadLoginDetail()
})
</script>

<style lang="scss" scoped>
.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 7px;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 700;
        font-size: 16px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

    .selectOption-item {
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;
        position: relative;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
            position: absolute;
            right: 10px;
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }

    .searchName {
        display: flex;
        align-items: center;
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        padding: 3px;
        font-weight: 400;
        font-size: 14px;


        .button {
            background: #1A76FF;
            border-radius: 5px;
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
            padding: 4px 13px;
            margin-left: 5px;
        }
    }


}

.optionList {

    .list-item {
        display: grid;
        grid-template-columns: 43px 1fr max-content;
        align-items: center;
        border-bottom: 1px solid #EEF5FF;
        padding: 16px 0px;

        img {
            width: 43px;
            height: 43px;

        }

        .name {
            margin-top: 3px;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            color: #3D3D3D;
        }

        .staffRole {
            font-weight: 400;
            margin-top: 3px;
            font-size: 12px;
            color: #999999;
        }

        .islogin {
            font-weight: 500;
            font-size: 18px;
            line-height: 26px;
        }

        .primary {
            color: #199964;
        }

        .danger {
            color: #C92528;
        }
    }

}




:deep(.van-search) {
    padding: 0px;
    flex: 1 0 45%;

    .van-search__content {
        background-color: transparent;
        padding-left: 5px;
    }

    .van-field__control {
        color: #999999;
    }

    .van-search__field {
        padding: 0px;
        height: 20px
    }

    .van-search__action {
        line-height: inherit;
    }
}



:deep(.van-popover__wrapper) {
    flex: 1 0 auto;
}

:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
    width: auto;
}
</style>