import axios, { AxiosInstance, AxiosRequestConfig, InternalAxiosRequestConfig, AxiosResponse } from "axios";
import { ElMessage, ElMessageBox } from "element-plus";

import { getToken, removeToken } from "@/utils/auth.ts";
import router from "@/router/index";
import { CryptoParams } from '@/utils/crypto/index'
import configAxios from "@/api/config"
import { extractPathPart } from '@/utils/extractPathPart.ts'
import { DecryptWithHKDF } from "@/utils/crypto/index";
import { useUserInfo } from "@/stores/userInfo.ts";

// 配置新建一个 axios 实例
const service: AxiosInstance = axios.create({
    baseURL: import.meta.env.VITE_API_URL,
    timeout: 50000,
    headers: {
        "Content-Type": "application/json",
        'terminal-type': "PC"
    },

});

// 添加请求拦截器
service.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
        const userInfoStore = useUserInfo()
        let crypto = extractPathPart(Object.values(configAxios), config.url || "")
        // 在发送请求之前做些什么 token
        let TokenKey = userInfoStore.getToken;
        config.headers!["Authorization"] = TokenKey || '';

        let params = config.params || {};
        let data = config.data || {}
        let result: Object

        // 根据缓存中的加密字段进行匹配进行加密
        if (Object.keys(params).length) {
            result = params
        } else {
            result = data
        }

        // 组装数据
        let configParams = {
            ...result,
            url: crypto,
        }
        // 加密合并请求
        Object.assign(config.headers, CryptoParams(configParams))
        return config
    },
    (error) => {
        // 对请求错误做些什么
        return Promise.reject(error);
    }
);

// 添加响应拦截器
const responseWhiteList = ['/o/']  //响应白名单

service.interceptors.response.use(
    async (response: AxiosResponse) => {
        const userInfoStore = useUserInfo()


        const _response = response.data

        // Owbi2eNNgLoL
        if (_response.code == 200) {

            if (_response.encrypted == 1) {
                const AESKEY = userInfoStore.AESKEY
                _response.data = JSON.parse(await DecryptWithHKDF(_response.data, AESKEY))
            }
            return _response
        }

        if (_response.code == 420) {
            removeToken();
            return
        } else if (_response.code == 401) {
            removeToken();
            ElMessage.error('认证过期,请重新登录!')
            router.replace('/login')
            return
        }





        return _response || '';
    },
    (error) => {

        ElMessage.error(error.response.data.message || error.message);
        if (error.response.data.code == 420) {
            removeToken();
        }
        return Promise.reject(error);
    }
);

// 导出 axios 实例
export default service;
