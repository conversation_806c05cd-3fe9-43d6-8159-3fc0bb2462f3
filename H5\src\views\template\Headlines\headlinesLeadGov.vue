<template>
    <div>
        <Gov_Biz_Template :dataList="dataList" v-model:current="curIndex" staffType="分局" />
    </div>
</template>



<script setup lang="ts">
import Gov_Biz_Template from "./modules_/index_Gov_Biz.vue";
import {HeadlinesHooks} from "./hooks/hook";

const curIndex = ref(0)
const { dataList } = HeadlinesHooks({
    staffType: '分局',
    isGov: true
})


</script>

<style lang="scss" scoped></style>