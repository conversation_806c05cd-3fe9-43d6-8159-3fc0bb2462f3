<template>
    <div id="container"></div>
</template>

<script setup lang="ts">
declare const AMapLoader: any;

// 地图数据
const dataMap = reactive({
    latitude: 32.061515, // 纬度
    lngitude: 118.781288, // 经度
});

// 事件发射
const emit = defineEmits(['getBuildingName']);

// 高德地图开发者 Key
const KEY = 'af467c8393980ededd55c565654d7081';

// 高德地图实例和 Geolocation 实例
let map: any = null;
let geolocation: any = null;

// 初始化地图
async function initMap() {
    try {
        const AMap = await loadAMap();

        // 创建地图实例
        map = new AMap.Map('container', {
            viewMode: '2D',
            zoom: 16,
            center: [dataMap.lngitude, dataMap.latitude],
        });

        map.set('scrollWheel', true);
        map.set('willReadFrequently', true); // 添加此行以提高性能
        // 添加定位插件
        AMap.plugin('AMap.Geolocation', () => {
            geolocation = new AMap.Geolocation({
                resizeEnable: true,
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 0,
                convert: true,
                showButton: true,
                buttonPosition: 'RB',
                showMarker: true,
                showCircle: true,
                panToLocation: true,
                zoomToAccuracy: true,
            });

            map.addControl(geolocation);

            // 获取当前位置信息
            geolocation.getCurrentPosition((status: string, result: any) => {
                if (status === 'complete') {
                    const { lng, lat } = result.position;
                    console.log('定位成功:', lng, lat);

                    // 更新地图中心点和数据
                    map.setCenter([lng, lat]);
                    dataMap.lngitude = lng;
                    dataMap.latitude = lat;

                    // 获取建筑名称
                    getBuildingName(lng, lat);
                } else {
                    console.error('定位失败:', result);
                    showFailToast('定位失败，请检查权限或网络！');
                }
            });
        });
    } catch (error) {
        console.error('地图初始化失败:', error);
        showFailToast('地图加载失败，请刷新重试！');
    }
}

// 加载高德地图
async function loadAMap() {
    return await AMapLoader.load({
        key: KEY,
        version: '2.0',
    });
}

// 获取建筑名称
async function getBuildingName(lng: number, lat: number) {
    try {
        const AMap = await loadAMap();

        AMap.plugin('AMap.Geocoder', () => {
            const geocoder = new AMap.Geocoder({
                radius: 50,
                extensions: 'all',
            });

            geocoder.getAddress([lng, lat], (status: string, result: any) => {
                if (status === 'complete' && result.regeocode) {
                    const buildingName = result.regeocode.formattedAddress;
                    const assistName = result.regeocode.addressComponent.township + result.regeocode.addressComponent.streetNumber 

                    // 触发事件传递建筑名称和坐标
                    emit('getBuildingName', { buildingName, lng, lat, assistName });
                } else {
                    console.error('逆地理编码失败:', result);
                    showFailToast('获取建筑信息失败！');
                }
            });
        });
    } catch (error) {
        console.error('逆地理编码加载失败:', error);
        showFailToast('加载地理编码服务失败！');
    }
}

// 销毁地图实例
onUnmounted(() => {
    if (map) {
        map.destroy();
        map = null;
    }
});

onMounted(() => {
    initMap();
});

</script>

<style scoped lang="scss">

:deep(.amap-logo) {
    display: none !important;
}

:deep(.amap-copyright) {
    display: none !important;
}

:deep(.amap-zoom-control) {
    display: none !important;
}
</style>
