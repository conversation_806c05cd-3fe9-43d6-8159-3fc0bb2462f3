import request from "../utils/request";
import configAxios from './config'

//复盘保存
export function reviewSaveAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/review/save`,
        method: "post",
        data,
    });
}



//复盘查询
export function getReviewListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/review/page`,
        method: "post",
        data,
    });
}



//过程管控 午间复盘 晚间复盘
export function getProcessControReviewListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/processContro/review`,
        method: "post",
        data,
    });
}

