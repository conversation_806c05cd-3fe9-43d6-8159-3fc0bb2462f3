

import { ref, reactive } from "vue";
import { getBusinessCountAPI } from "@/api/opportunity";
import { formatDate } from "@/utils/loadTime";
import { PopoverAction } from "vant";
import { useUserInfo } from "@/stores/userInfo";
import { getEntryProtocolPageUrlAPI } from "@/api/common";
type tableData = {
    type: number,
    recordCount: number,
    acceptCount: number,
    unacceptCount: number,
    handlingCount: number,
    finishCount: number,
    successCount: number,
    dispatchCount: number,
    dispatchrate: number
    [key: string]: any
}
export enum UserType {
    Other = 0,  // 其他
    Public = 1, // 公众
    Enterprise = 2 // 政企
}

export const opportunityHooks = function () {


    const userInfoStore = useUserInfo()
    const roleCode = computed(() => {
        return userInfoStore.userInfo?.primaryRoleCode
    })
    const isShowBusinessAudit = computed(() => {
        const roleCode = userInfoStore.userInfo.primaryRoleCode;
        if (roleCode) {
            return ['sub_area_leader', 'acc_manager'].includes(roleCode);
        } else {
            return false;

        }
    })
    const isGov = ref(false)


    const myTableData = ref<Partial<tableData>>({
        type: 0,
        recordCount: 0,
        acceptCount: 0,
        unacceptCount: 0,
        handlingCount: 0,
        finishCount: 0,
        successCount: 0,
        dispatchCount: 0,
        dispatchrate: 0
    });
    const leadTableData = ref<Partial<tableData>>({
        type: 1,
        recordCount: 0,
        acceptCount: 0,
        unacceptCount: 0,
        handlingCount: 0,
        finishCount: 0,
        successCount: 0,
        dispatchCount: 0,
        dispatchrate: 0,
    });

    const showPopover = ref(false);
    const curSelectTimeType = ref<number>(1) // 0 当天  1 当月
    const curSelectTimeTime = ref<string>('当月')


    const actions = [
        { text: '当天' },
        { text: '当月' },
    ];




    function onSelect(action: PopoverAction) {
        if (curSelectTimeTime.value == action.text) return
        curSelectTimeType.value = action.text == '当天' ? 0 : 1
        curSelectTimeTime.value = action.text
        loadList()
    }
    function loadList() {
        const params = {
            type: curSelectTimeType.value
        };

        getBusinessCountAPI(params).then((res: any) => {
            for (const item of res.data || []) {


                if (item.dispatchCount == 0) {
                    item.dispatchrate = 0
                } else {
                    item.dispatchrate = ((item.marketSuccessCount / item.dispatchCount) * 100).toFixed(2)
                }

                if (item.type == 1) {
                    leadTableData.value = item
                } else {
                    myTableData.value = item
                }
            }
        })

    }


    watch(() => userInfoStore.userInfo.orgDept, (newValue) => {

        switch (newValue) {
            case UserType.Public:
                isGov.value = false
                break;
            case UserType.Enterprise:
                isGov.value = true
                break;
            case UserType.Other:
                isGov.value = true
                break;
            default:
                isGov.value = false

                break;
        }
    }, { deep: true, immediate: true })




    function goToHuXiu() {
        getEntryProtocolPageUrlAPI().then((res: any) => {
            if (res.code == 200) {
                location.href = res.data
            } else {
                showFailToast('获取虎嗅商机录入地址失败')
            }
        }).catch(() => {
            showFailToast('获取虎嗅商机录入地址失败')
        })
    }



    onMounted(() => {
        loadList()
    });

    return {
        myTableData,
        leadTableData,
        curSelectTimeType,
        curSelectTimeTime,
        showPopover,
        actions,
        roleCode,
        isShowBusinessAudit,
        isGov,
        userInfoStore,
        onSelect,
        loadList,
        goToHuXiu,
    }
}   
