import CryptoJS from 'crypto-js'

import { randomString } from "./foundation";
import jsonStableStringify from 'json-stable-stringify'
const ckey = 'okou74hs53z69hj7jy7wim5gqinzqx7i';//密钥
const appId = 'njdx_task_hub_2024'

interface CryptoParamsResult {
  timeStamp: string;
  nonce: string;
  appId: string;
  sign: string;
}

//加密方法
export const CryptoParams = (params: Record<string, any>): CryptoParamsResult => {
  let nonce = randomString(6);
  let timeStamp = Date.parse(String(new Date())).toString()
  let Obj = { timeStamp, nonce, appId }
  Object.assign(params, Obj)
  let queryObj = jsonStableStringify(queryNodes(params))
  if (!queryObj) {
    throw new Error('Failed to stringify query parameters')
  }
  const Hmac = CryptoJS.HmacSHA256(queryObj.toString(), ckey)
  const sign = CryptoJS.enc.Base64.stringify(Hmac)
  let AES = {
    ...Obj,
    sign
  }
  // console.log(AES, 'aes==============')
  return AES
}

const queryNodes = (obj: Record<string, any>): Record<string, any> => {
  Object.keys(obj).forEach((key) => {
    let value = obj[key];
    value && typeof value === 'object' && queryNodes(value);
    (value === 'null' || value === null || value === undefined) && delete obj[key];
  });
  return obj;
};




export function Encrypt(data: string, aes_key = 'RHjKzzL7n64eVR9V') {
  let key = CryptoJS.enc.Utf8.parse(aes_key);
  let encrypted = CryptoJS.AES.encrypt(data, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  });
  return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Hex.parse(encrypted.ciphertext.toString()));
}


export function Decrypt(word: string, aes_key = 'RHjKzzL7n64eVR9V') {
  let key = CryptoJS.enc.Utf8.parse(aes_key);
  let decrypt = CryptoJS.AES.decrypt(word, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });
  let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
  return decryptedStr;
}




/**
 * HKDF implementation (based on RFC 5869)
 * @param {string} ikmBase64 - Base64 encoded input key material
 * @param {string} saltBase64 - Base64 encoded salt
 * @param {string} info - Info string
 * @param {number} length - Output key length (bytes)
 * @returns {string} - Base64 encoded derived key
 */
export function hkdfDerive(ikmBase64: string, saltBase64: string, info: string, length: number): string {
  // Decode Base64 input
  const ikmWordArray = base64ToWordArray(ikmBase64);
  const saltWordArray = base64ToWordArray(saltBase64);
  const infoWordArray = CryptoJS.enc.Utf8.parse(info);

  // Step 1: Extract
  const prk = hmacSha256(saltWordArray, ikmWordArray);

  // Step 2: Expand
  const okm = expand(prk, infoWordArray, length);

  // Convert to Base64
  return okm.toString(CryptoJS.enc.Base64);
}

/**
 * HMAC-SHA256 function
 */
function hmacSha256(key: CryptoJS.lib.WordArray, data: CryptoJS.lib.WordArray): CryptoJS.lib.WordArray {
  return CryptoJS.HmacSHA256(data, key);
}

/**
 * Expand function
 */
function expand(
  prk: CryptoJS.lib.WordArray,
  info: CryptoJS.lib.WordArray,
  length: number
): CryptoJS.lib.WordArray {
  const blocksNeeded = Math.ceil(length / 32); // SHA-256 output length is 32 bytes
  let t = CryptoJS.lib.WordArray.create();
  let okm = CryptoJS.lib.WordArray.create();

  for (let i = 1; i <= blocksNeeded; i++) {
    let prev = (i === 1) ? CryptoJS.lib.WordArray.create() : t;
    let input = CryptoJS.lib.WordArray.create();
    input.concat(prev);
    input.concat(info);
    input.concat(CryptoJS.enc.Utf8.parse(String.fromCharCode(i)));

    t = hmacSha256(prk, input);
    okm.concat(t);
  }

  // Manually truncate to required byte length
  return truncateWordArray(okm, length);
}

/**
 * Base64 to WordArray conversion
 */
function base64ToWordArray(base64: string): CryptoJS.lib.WordArray {
  return CryptoJS.enc.Base64.parse(base64);
}

/**
 * Manually truncate WordArray to specified byte length
 */
function truncateWordArray(wordArray: CryptoJS.lib.WordArray, byteLength: number): CryptoJS.lib.WordArray {
  const wordCount = Math.ceil(byteLength / 4);
  const words = wordArray.words.slice(0, wordCount);
  const partialBytes = byteLength % 4;
  if (partialBytes > 0 && words.length > 0) {
    const mask = 0xFFFFFFFF >>> (32 - partialBytes * 8);
    words[words.length - 1] &= mask;
  }

  return CryptoJS.lib.WordArray.create(words, byteLength);
}



/**
 * Decrypt using HKDF derived key
 */
export async function DecryptWithHKDF(word: string, saltBase64: string): Promise<string> {
  return new Promise<string>(async (resolve, reject) => {
    try {
      const info = "hkdf";
      const length = 16; // 16 bytes = 128 bits
      const aes_key = hkdfDerive('lxlnqe2spl6zelhjdzjv85f5ml18uf8xklk6', saltBase64, info, length);
      const key = CryptoJS.enc.Utf8.parse(aes_key);

      const decrypt = CryptoJS.AES.decrypt(word, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      });

      const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
      resolve(decryptedStr);
    } catch (error) {
      reject(error);
    }
  });
}
