<template>
    <div>
        <div class="card-header">
            <van-popover :actions="areaList" v-if="!isAreaNameL2" @select="changArea" placement="bottom-start">
                <template #reference>
                    <div class="selectOption-item">
                        <span>{{ curSelectArea.areaNameL2 }}</span>
                        <van-icon name="play" class="play" color="#1A76FF" />
                    </div>
                </template>
            </van-popover>
            <!-- <div class="selectOption-item" v-else style="width: 100%;">
                <span>{{ areaNameL2 }}dgfdfdfgdgfdgf</span>
                <van-icon name="play" class="play" color="#1A76FF" />
            </div> -->



            <van-search v-model="searchName" show-action class="searchName" placeholder="搜索姓名">
                <template #action>
                    <div class="button" @click="searchByName">搜索</div>
                </template>
                <template #left-icon>

                </template>
            </van-search>


        </div>
        <div class=' container_ mt-6'>
            <div class="container__header">
                <div>
                    <img src="@/assets/resultOptimization.svg" alt="" srcset="">
                    <span class="ml-6">{{ route.query.areaNameL2 ?? props.areaNameL2 }}</span>
                </div>
            </div>
            <div class="container_body " v-loading="loadingConfig">
                <div class="optionList">
                    <div class="list-item" v-for="item, index in progressData" :key="index">
                        <img src="@/assets/defaultAvatar.png" alt="">
                        <div class="ml-6">
                            <div class="name">
                                <span v-getName="{ item: item }">{{ item.staffName }}</span>
                                <span class="fs12 ml-3">{{ item.staffCode }}</span>
                            </div>
                            <div class="staffRole">{{ item.roleName }}</div>
                        </div>
                        <div class="islogin" :class="[item.sign == 1 ? 'primary' : 'danger']">
                            {{ item.sign == 1 ? '今日已出征' : '今日未出征' }}
                        </div>
                    </div>

                    <van-empty description="暂无数据" v-if="progressData.length == 0" />

                </div>

            </div>




        </div>
    </div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from "vue-router";
import { getlistDeptOrSubOptions } from "@/hooks_modules/getlistDeptOrSubOptions";
import { mapSort } from "@/utils/mapSort";
import { getSignByAreaL2ListAPI } from "@/api/morning";
import { debounce } from "@/utils/debounce";
const route = useRoute()

const props = defineProps({
    areaNameL2: {
        type: String,
        default: ''
    },
    isAreaNameL2: {
        type: Boolean,
        default: false
    }
})

const { isAreaNameL2, areaNameL2 } = toRefs(props)

const { areaList } = getlistDeptOrSubOptions(
    {
        areaId: route.query.areaId ? String(route.query.areaId) : undefined,
        isPushAll: true,
        isNotSort: true
    })

const loadingConfig = reactive<LoadingDirectiveOptions>({
    visible: false, // 控制加载状态
    text: '请稍等...', // 自定义加载文本
    iconSize: '36',
    iconColor: '#fff',
    borderRadius: '10px',
});



const searchName = ref('')
const curSelectArea = reactive({
    areaId: String(route.query.areaId ?? ''),
    areaName: String(route.query.areaName),
    areaIdL2: String(route.query.areaIdL2 ?? ''),
    areaNameL2: String(route.query.areaNameL2),
})

const progressData = ref<{ staffName?: string, roleName?: string, sign?: number, staffCode?: string }[]>([])



watch(() => isAreaNameL2.value, (value) => {


}, { immediate: true })



const searchByName = debounce(() => {
    loadList()
}, 300, true)


function changArea(params: any) {
    searchName.value = ''
    curSelectArea.areaIdL2 = params.value
    curSelectArea.areaNameL2 = params.text
    loadList()
}

function loadList() {
    let params = {
        areaIdL2: curSelectArea.areaIdL2 ? curSelectArea.areaIdL2 : undefined,
        staffName: searchName.value ? searchName.value : undefined
    }
    loadingConfig.visible = true
    getSignByAreaL2ListAPI(params).then((res: any) => {
        if (res.code == 200) {
            res.data = res.data.map((item: any) => {
                return {
                    staffName: item.staffName,
                    roleName: item.staffRoleName,
                    staffCode: item.staffCode,
                    sign: item.sign
                }
            })

            res.data = mapSort(res.data, 'roleName')
            progressData.value = res.data
        }
    }).finally(() => {
        loadingConfig.visible = false
    })

}

onMounted(() => {
    loadList()

})
</script>

<style lang="scss" scoped>
.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 7px;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 700;
        font-size: 16px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

    .selectOption-item {
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400 !important;
        font-size: 14px !important;
        color: #1A76FF !important;
        padding: 8px;
        display: flex;
        align-items: center;
        position: relative;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
            position: absolute;
            right: 10px;
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }

    .searchName {
        display: flex;
        align-items: center;
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        padding: 3px;
        font-weight: 400;
        font-size: 14px;


        .button {
            background: #1A76FF;
            border-radius: 5px;
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
            padding: 4px 13px;
            margin-left: 5px;
        }
    }


}

.optionList {

    .list-item {
        display: grid;
        grid-template-columns: 43px 1fr max-content;
        align-items: center;
        border-bottom: 1px solid #EEF5FF;
        padding: 16px 0px;

        img {
            width: 43px;
            height: 43px;

        }

        .name {
            margin-top: 3px;
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            color: #3D3D3D;
        }

        .staffRole {
            font-weight: 400;
            margin-top: 3px;
            font-size: 12px;
            color: #999999;
        }

        .islogin {
            font-weight: 500;
            font-size: 18px;
            line-height: 26px;
        }

        .primary {
            color: #199964;
        }

        .danger {
            color: #C92528;
        }
    }

}




:deep(.van-search) {
    padding: 0px;
    flex: 1 0 45%;
    min-width: 45%;

    .van-search__content {
        background-color: transparent;
        padding-left: 5px;
    }

    .van-field__control {
        color: #999999;
    }

    .van-search__field {
        padding: 0px;
        height: 20px
    }

    .van-search__action {
        line-height: inherit;
    }
}



:deep(.van-popover__wrapper) {
    flex: 1 0 auto;
}

:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
    width: auto;
}
</style>