
/**
 * 创建一个防抖函数，在 `wait` 毫秒之后执行 `func`。如果在这段时间内再次调用该函数，
 * 计时器将重新开始计时。可以选择在计时器开始时立即调用 `func`。
 *
 * @param {Function} func - 需要防抖处理的函数。
 * @param {number} wait - 等待时间，单位为毫秒。
 * @param {boolean} [immediate=false] - 如果为 `true`，则在等待时间开始时立即调用函数，而不是结束时。
 * @returns {DebouncedFunction} 返回一个新的防抖函数，该函数带有一个 `cancel` 方法，可以取消防抖操作。
 * @example
 * // 防抖处理窗口调整大小事件，每次调整结束后 200 毫秒触发一次回调
 * function onResize() {
 *   console.log('Window resized!');
 * }
 * const debouncedResize = debounce(onResize, 200);
 * window.addEventListener('resize', debouncedResize);
 *
 * @example
 * // 创建一个防抖函数，在首次调用时立即执行
 * const debouncedFunc = debounce(() => {
 *   console.log('Executed!');
 * }, 200, true);
 * debouncedFunc();
 *
 * @example
 * // 取消防抖操作
 * const debouncedResize = debounce(onResize, 200);
 * debouncedResize();
 * debouncedResize.cancel();
 */
type DebouncedFunction<T extends (...args: any[]) => any> = {
    (...args: Parameters<T>): ReturnType<T> | undefined;
    cancel: () => void;
  };
  
  export const debounce = <T extends (...args: any[]) => any>(
    func: T,
    wait: number,
    immediate: boolean = false
  ): DebouncedFunction<T> => {
    let timeout: ReturnType<typeof setTimeout> | null;
    let result: ReturnType<T> | undefined;
  
    const debounced: DebouncedFunction<T> = function (this: unknown, ...args: Parameters<T>): ReturnType<T> | undefined {
      const context = this;
  
      const later = function () {
        timeout = null;
        if (!immediate) result = func.apply(context, args);
      };
  
      const callNow = immediate && !timeout;
  
      if (timeout) clearTimeout(timeout);
      timeout = setTimeout(later, wait);
  
      if (callNow) result = func.apply(context, args);
      return result;
    };
  
    debounced.cancel = function () {
      if (timeout) clearTimeout(timeout);
      timeout = null;
    };
  
    return debounced;
  };
  