<template>
    <div class="card">
        <div class="card-header">
            <div>
                <img src="@/assets/orderType.svg" alt="" srcset="">
                <div v-if="route.query.staffName" v-getName="{ item: { staffCode: route.query.staffCode } }">{{
                    route.query.staffName }}</div>
                <div v-else>派单类别</div>
                <van-popover placement="bottom-start">
                    <div class="popoverBody">
                        <li>接触量：外呼时长大于0的数量</li>
                        <li>接触率：接触量/派单量</li>
                        <li>外呼量：外呼时长大于30秒的数量</li>
                        <li>外呼率：外呼量/派单量</li>
                        <li>以上数据均来自智慧营销，最终口径以业务为准</li>

                    </div>
                    <template #reference>

                        <van-icon class="ml-3" name="question" color="#1A76FF" />
                    </template>
                </van-popover>
            </div>
            <div class="card-header-right">
                <div class="item" @click="changeOrderType('S')" :class="curOrderType == 'S' ? 'activeItem' : ''">S类
                </div>
                <div class="item" @click="changeOrderType('A')" :class="curOrderType == 'A' ? 'activeItem' : ''">A类
                </div>
                <div class="item" @click="changeOrderType('B')" :class="curOrderType == 'B' ? 'activeItem' : ''">B类
                </div>
                <div class="underline"></div>
            </div>
        </div>
        <div class="mt-8 card-body">
            <div class="table-container" @touchstart.stop>
                <table>
                    <thead>

                        <tr>
                            <th class="sticky">派单名称</th>

                            <th>派单量</th>
                            <th>接触量</th>
                            <th>接触率</th>
                            <th>外呼量</th>
                            <th>外呼率</th>
                            <th>成功数</th>
                            <th>完成数</th>
                            <th>未完成数</th>

                        </tr>

                    </thead>
                    <tbody>
                        <tr v-for="(row, index) in tableData" :key="index">
                            <td class="sticky">
                                <div class="areaNameL2">
                                    {{ row.activityName }}
                                </div>
                            </td>
                            <td>{{ row.dispatchNum }}</td>
                            <td>{{ row.touchNum }}</td>
                            <td>{{ row.touchRate }}%</td>
                            <td>{{ row.callNum }}</td>
                            <td>{{ row.callRate }}%</td>
                            <td>{{ row.successNum }}</td>
                            <td>{{ row.touchNum }}</td>
                            <td>{{ row.unTouchNum }}</td>
                        </tr>
                    </tbody>



                </table>

            </div>
            <van-empty description="暂无数据" class="mt-8" style="background-color: #FBFCFF;border-radius: 15px;"
                v-if="tableData.length == 0" />
        </div>



    </div>
</template>

<script setup lang="ts">
import myCharts from "@/components/charts/myCharts.vue";
import { getSendOrderDetailAPI, getSendOrderTableStaffAPI } from "@/api/home";
import { useRoute } from "vue-router";
import { ref, reactive } from "vue";
const route = useRoute()


interface Data {

    activityName?: string;  // 框架代码，可选
    callNum?: number;  // 外呼量，可选
    callRate?: number;  //外呼率
    successNum?: number; // 成功数

    dispatchNum?: number;  // 派单量，可选
    touchNum?: number;  // 接触量，可选
    touchRate?: number;  // 接触率，可选

    // transformNum?: number;  // 转化量，可选
    // transformRate?: number;  // 转化率，可选
    [key: string]: any
}

const tableData = ref<Data[]>([]);







const curOrderType = ref<string>('S')  //     S  S类       A  A类     B B类


function changeOrderType(type: string) {
    if (curOrderType.value == type) return
    curOrderType.value = type
    loadDetaild()
}

function loadDetaild() {
    let parmas = {
        frameType: curOrderType.value,
        startTime: route.query.startTime ? `${route.query.startTime} 00:00:00` : '',
        endTime: route.query.endTime ? `${route.query.endTime} 23:59:59` : '',
        staffCode: route.query.staffCode
    }
    getSendOrderTableStaffAPI(parmas).then(res => {
        for (const item of res.data || []) {
            item.callRate = item.dispatchNum == 0 ? 0 : Math.round((item.callNum / item.dispatchNum) * 100)
            item.touchRate = item.dispatchNum == 0 ? 0 : Math.round((item.touchNum / item.dispatchNum) * 100)
        }
        tableData.value = res.data || []

    })

}



onMounted(() => {
    if (route.query.type) curOrderType.value = String(route.query.type)

    loadDetaild()
})
</script>

<style lang="scss" scoped>
.card {
    font-family: 'Source Han Sans, Source Han Sans';
    min-height: 100%;
    overflow-y: scroll;
    overflow-x: hidden;
    font-family: Source Han Sans, Source Han Sans;

    .card-header {
        height: 46px;
        padding: 2px 20px;
        background: #FBFCFF;
        border-radius: 10px 10px 10px 10px;
        box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: space-between;

        &>div:first-child {
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 16px;
            color: #3D3D3D;
            line-height: 18px;

            img {
                margin-right: 8px;
            }

        }

        .card-header-right {
            position: relative;
            height: 46px;
            display: flex;
            align-items: center;
            justify-content: space-around;
            touch-action: manipulation;
            gap: 5px;
            /* 提升移动端触摸体验 */

            &>.item {
                width: 45px;
                height: 46px;
                text-align: center;
                line-height: 46px;
                font-weight: 400;
                font-size: 16px;
                color: #919191;
                cursor: pointer;
                position: relative;
                transition: color 0.3s;
                user-select: none;
                /* 防止在移动端选中文本 */
            }

            &>.item.activeItem {
                font-weight: 500;
                font-size: 16px;
                color: #1A76FF;
            }
        }

        .underline {
            position: absolute;
            bottom: 5px;
            height: 4px;
            background: #1a76ff;
            border-radius: 26px;
            transition: all 0.3s ease;
            width: 25px;
            left: 10px;
            /* 初始位置 */
        }

        /* 下划线的移动控制 */
        .card-header-right .item:nth-child(1).activeItem~.underline {
            transform: translateX(0);
            left: 10px;
        }

        .card-header-right .item:nth-child(2).activeItem~.underline {
            transform: translateX(-100%);
            left: 58%;
        }

        .card-header-right .item:nth-child(3).activeItem~.underline {
            transform: translateX(-140%);
            left: 100%;
        }

    }

    .card-body {
        background: #FBFCFF;
        box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
        background: #FBFCFF;
        border-radius: 10px 10px 10px 10px;

        .table-container {
            width: 100%;
            overflow-x: auto;
            margin-top: 10px;

            padding-bottom: 10px;

            table {
                width: 100%;
                border-collapse: collapse;
                text-align: center;
                font-size: 13px;

                th,
                td {
                    padding: 8px;
                    border: 1px solid #ddd;

                    white-space: nowrap; // 禁止换行
                }

                .areaNameL2 {
                    min-width: 120px;
                    white-space: normal;
                }

                .sticky {
                    /* 固定表格内容的门店列 */
                    position: sticky;
                    left: 0px;
                    background-color: #fff;
                    /* 设置背景颜色以覆盖其他列 */
                    z-index: 1;
                    border: 1px solid #ddd;

                    /* 确保门店列在最上层 */
                }

                .main-title {
                    font-size: 13px;
                    font-weight: bold;
                    padding: 12px;
                    background-color: #2e70ba;
                    color: #fff;
                    text-align: center;
                }

                thead tr:nth-child(n) th {
                    background-color: #2e70ba;
                    color: #fff;
                    font-weight: bold;
                    padding: 10px;
                    text-align: center;
                }

                thead tr:nth-child(3) th {
                    background-color: #2e70ba;
                    color: #fff;
                    font-weight: bold;
                    padding: 8px;
                }



                tbody tr:nth-child(even) {
                    background-color: #f2f2f2;
                }




                tbody tr:hover {
                    background-color: #e6f7ff;
                }

                tbody td.highlight {
                    background-color: #ffe58f;
                    font-weight: bold;
                }
            }


        }
    }
}

.popoverBody {
    font-size: 13px;
    padding: 5px 10px;
}
</style>