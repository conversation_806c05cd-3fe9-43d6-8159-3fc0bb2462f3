import { uploadVoiceAPI, byAudioUrlQueryTextAPI, getAudioUrlQueryTextIdAPI } from "@/api/common";
import { showFailToast, showSuccessToast, showLoadingToast } from 'vant'

type Params = {
    recordingTime?: number // 可选参数，录音时间限制（秒）
}
// 添加 AudioSegment 接口定义
interface AudioSegment {
    blob: Blob;
    timestamp: number;
}

export interface UploadAudioHooksReturn {
    /** 开始录音方法 */
    startRecording: () => Promise<void>;
    /** 停止录音方法 */
    stopRecording: () => Promise<void>;
    /** 暂停录音方法 */
    pauseRecording: () => void;
    /** 继续录音方法 */
    resumeRecording: () => void;
    /** 取消录音方法 */
    cancelRecording: () => void;
    /** 上传录音文件方法 */
    uploadAudio: () => void;
    /** 是否正在录音中 */
    recording: Ref<boolean>;
    /** 是否处于暂停状态 */
    isPaused: Ref<boolean>;
    /** 录音文件URL */
    audioUrl: Ref<string | null>;
    /** 当前录音时长（秒） */
    currentTime: Ref<number>;
    /** 加载配置 */
    loadingConfig: LoadingDirectiveOptions;
    /** 消息回调 */
    messageCallBack: Ref<string>;
    /** 请求ID */
    reqId: Ref<string>;
}

export const uploadAudioHooks = function (params?: Params) {
    // 默认录音时间限制为2分钟（120秒）
    const MAX_RECORDING_TIME = (params?.recordingTime || 120) * 1000;
    const recording = ref(false)
    const isPaused = ref(false)
    const audioUrl = ref<string | null>(null)
    const audioBlob = ref<Blob | null>(null)
    const currentTime = ref(0) // 当前录音时间（秒）
    let mediaRecorder: MediaRecorder | null = null
    const audioSegments: AudioSegment[] = []
    let recordingTimer: ReturnType<typeof setInterval> | null = null;
    let audioContext: AudioContext | null = null;
    let audioStream: MediaStream | null = null;

    const loadingConfig = reactive<LoadingDirectiveOptions>({
        visible: false, // 控制加载状态
        text: '转换中，请稍等...', // 自定义加载文本
        textSize: '12',
        iconSize: '24',
        iconColor: '#fff',
        borderRadius: '10px',
    });
    const reqId = ref('')
    let pollTimer: ReturnType<typeof setTimeout> | null = null; // 添加轮询定时器引用
    const messageCallBack = ref('')   //语音转文本 成功后的结果
    let isProcessing = ref(false);

    // 🔥 检查麦克风权限
    const checkPermission = async () => {
        try {
            // 检查浏览器是否支持 permissions API
            if (!navigator.permissions) {
                // 如果不支持 permissions API，直接尝试获取麦克风权限
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                stream.getTracks().forEach(track => track.stop());
                return true;
            }

            const permission = await navigator.permissions.query({ name: 'microphone' as PermissionName });
            if (permission.state === 'denied') {
                showFailToast('麦克风权限被拒绝，请在设置中打开权限');
                return false;
            }
            return true;
        } catch (error) {
            console.error('权限检查失败:', error);
            showFailToast('无法访问麦克风，请检查权限设置');
            return false;
        }
    }

    // ⏱️ 开始计时
    const startTimer = () => {
        if (!recordingTimer) {
            recordingTimer = setInterval(() => {
                currentTime.value++
                // 如果超过最大录音时间，自动停止
                if (currentTime.value * 1000 >= MAX_RECORDING_TIME) {
                    stopTimer() // 先停止计时器
                    stopRecording().then(() => {
                        showToast('已达到最大录音时间')
                    })
                }
            }, 1000)
        }
    }

    // ⏹️ 停止计时
    const stopTimer = () => {
        if (recordingTimer) {
            clearInterval(recordingTimer)
            recordingTimer = null
        }
    }

    // ⏸️ 暂停录音
    const pauseRecording = async () => {
        if (mediaRecorder && recording.value && !isPaused.value) {
            try {
                // 先请求当前数据
                mediaRecorder.requestData();

                // 等待一小段时间确保数据被收集
                await new Promise(resolve => setTimeout(resolve, 100));

                mediaRecorder.pause();
                stopTimer();
                isPaused.value = true;

                console.log('暂停录音，当前片段数:', audioSegments.length);
            } catch (error) {
                console.error('暂停录音失败:', error);
                showFailToast('暂停录音失败');
            }
        }
    }

    // ▶️ 继续录音
    const resumeRecording = async () => {
        if (recording.value && isPaused.value && mediaRecorder) {
            try {
                mediaRecorder.resume();
                isPaused.value = false;
                startTimer();
                console.log('继续录音');
            } catch (error) {
                console.error('继续录音失败:', error);
                showFailToast('继续录音失败，请重试');
            }
        }
    }
    // 新增取消录音方法
    const cancelRecording = () => {
        if (recording.value) {
            stopTimer(); // 停止计时器
            if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                mediaRecorder.stop(); // 停止录音
            }
            if (audioStream) {
                audioStream.getTracks().forEach(track => track.stop()); // 停止音频流
            }
            resetState(); // 重置状态
            console.log('录音已取消');
        }
    };
    // 🚀 开始录音
    const startRecording = async () => {
        const permissionGranted = await checkPermission()
        if (!permissionGranted) return

        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            showFailToast('当前浏览器不支持录音功能')
            return
        }

        try {
            // 开始新录音前重置状态
            resetState()
            audioSegments.length = 0; // 清空音频片段

            const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
            audioStream = stream;

            // 创建 MediaRecorder 实例
            // mediaRecorder = new MediaRecorder(stream, {
            //     mimeType: 'audio/webm;codecs=opus',
            //     audioBitsPerSecond: 128000
            // });
            mediaRecorder = new MediaRecorder(stream,)
            // 改进数据收集逻辑
            mediaRecorder.ondataavailable = (event: BlobEvent) => {
                if (event.data && event.data.size > 0) {
                    audioSegments.push({
                        blob: event.data,
                        timestamp: Date.now()
                    });
                    console.log('收集到音频片段:', event.data.size, '当前总片段:', audioSegments.length);
                }
            };

            // 添加错误处理
            mediaRecorder.onerror = (event) => {
                console.error('录音错误:', event);
                showFailToast('录音出错，请重试');
                resetState();
            };

            // 使用较小的时间片收集数据
            mediaRecorder.start(200); // 每200ms收集一次数据
            recording.value = true;
            startTimer();

            console.log('开始录音');
        } catch (error) {
            console.error('录音失败:', error);
            showFailToast('录音失败，请检查录音权限');
            resetState();
        }
    }

    // 🛑 停止录音
    const stopRecording = () => {
        return new Promise<void>((resolve) => {
            if (!recording.value || isProcessing.value) {
                resolve();
                return;
            }

            isProcessing.value = true;
            stopTimer();

            const processAudio = async () => {
                try {
                    console.log('开始处理音频，总片段数:', audioSegments.length);

                    if (audioSegments.length === 0) {
                        throw new Error('没有收集到音频数据');
                    }

                    // 合并所有音频片段
                    const blobs = audioSegments.map(segment => segment.blob);
                    const webmBlob = new Blob(blobs, { type: 'audio/webm;codecs=opus' });

                    console.log('合并后的音频大小:', webmBlob.size);

                    // 转换为 WAV 格式
                    audioContext = new AudioContext({
                        sampleRate: 16000,
                        latencyHint: 'interactive'
                    });

                    const audioData = await webmBlob.arrayBuffer();
                    const audioBuffer = await audioContext.decodeAudioData(audioData);

                    // 压缩音频
                    const compressedBuffer = await compressAudioBuffer(audioBuffer);
                    const wavBlob = audioBufferToWav(compressedBuffer);

                    // 保存并上传
                    audioBlob.value = new Blob([wavBlob], { type: 'audio/wav' });
                    await uploadAudio();

                } catch (error) {
                    console.error('处理音频失败:', error);
                    showFailToast('处理音频失败，请重试');
                } finally {
                    isProcessing.value = false;
                    resetState();
                    resolve();
                }
            };

            // 如果录音器还在活动状态，先停止它
            if (mediaRecorder && mediaRecorder.state !== 'inactive') {
                mediaRecorder.onstop = () => {
                    setTimeout(processAudio, 200); // 给足够的时间收集最后的数据
                };
                mediaRecorder.stop();
            } else {
                processAudio();
            }

            // 停止音频流
            if (audioStream) {
                audioStream.getTracks().forEach(track => track.stop());
            }
        });
    };

    // 添加音频压缩函数
    const compressAudioBuffer = async (buffer: AudioBuffer): Promise<AudioBuffer> => {
        // 创建离线音频上下文
        const offlineContext = new OfflineAudioContext(
            1, // 单声道
            buffer.length,
            16000 // 16kHz采样率
        );

        // 创建音频源
        const source = offlineContext.createBufferSource();
        source.buffer = buffer;

        // 创建压缩器
        const compressor = offlineContext.createDynamicsCompressor();
        compressor.threshold.value = -24;
        compressor.knee.value = 30;
        compressor.ratio.value = 12;
        compressor.attack.value = 0.003;
        compressor.release.value = 0.25;

        // 连接节点
        source.connect(compressor);
        compressor.connect(offlineContext.destination);

        // 开始渲染
        source.start();
        return await offlineContext.startRendering();
    }

    // 📤 上传录音
    const uploadAudio = () => {
        if (!audioBlob.value) return
        const formData = new FormData()

        const file = new File([audioBlob.value], 'recording.wav', { type: 'audio/wav' });
        formData.append('file', file, 'recording.wav')

        return new Promise<void>((resolve, reject) => {


            uploadVoiceAPI(formData).then((res: any) => {
                if (res.code == 200) {
                    setTimeout(() => {
                        // 立刻回调会导致 录制完成的 提示被上传成功的提示挤掉
                        showSuccessToast('上传成功')
                    }, 1000);
                    byAudioUrlQueryText(res.data.name)
                    resolve(res)
                } else {
                    reject('上传失败')
                }

            }).catch(error => {
                console.error('上传失败:', error)
                reject(error)
                showFailToast('上传失败')
            })


        })



    }
    const { VITE_IMAGE_URL } = import.meta.env
    const byAudioUrlQueryText = function (url: string) {
        getAudioUrlQueryTextIdAPI({ voiceUrl: VITE_IMAGE_URL + url }).then((res: any) => {
            if (res.code == 200) {
                reqId.value = res.data
                loadingConfig.visible = true

                // 添加轮询机制
                let pollCount = 0;
                const maxPolls = 10;
                const pollInterval = 3000; // 3秒

                const poll = () => {
                    if (pollCount >= maxPolls) {
                        loadingConfig.visible = false;
                        showFailToast('语音识别超时，请重试');
                        return;
                    }

                    byAudioUrlQueryTextAPI({ reqId: reqId.value }).then((resques: any) => {
                        console.log(resques);
                        if (resques.code == 200 && resques.data) {
                            // 如果识别成功，停止轮询
                            loadingConfig.visible = false;
                            messageCallBack.value = resques.data
                        
                            
                            showSuccessToast('识别成功');
                            // 清除定时器
                            if (pollTimer) {
                                clearTimeout(pollTimer);
                                pollTimer = null;
                            }
                        } else {
                            // 继续轮询
                            pollCount++;
                            pollTimer = setTimeout(poll, pollInterval);
                        }
                    }).catch(() => {
                        // 发生错误时继续轮询
                        pollCount++;
                        pollTimer = setTimeout(poll, pollInterval);
                    });
                };

                // 开始第一次轮询
                poll();
            }
        })
    }

    // 重置状态函数优化
    const resetState = () => {
        recording.value = false;
        isPaused.value = false;
        isProcessing.value = false;
        audioUrl.value = null;
        audioBlob.value = null;
        currentTime.value = 0;
        messageCallBack.value = '';
        reqId.value = '';
        loadingConfig.visible = false;

        // 清理音频相关资源
        if (mediaRecorder && mediaRecorder.state !== 'inactive') {
            try {
                mediaRecorder.stop();
            } catch (e) {
                console.error('停止 mediaRecorder 失败:', e);
            }
        }
        mediaRecorder = null;

        if (audioStream) {
            audioStream.getTracks().forEach(track => {
                try {
                    track.stop();
                } catch (e) {
                    console.error('停止音轨失败:', e);
                }
            });
        }
        audioStream = null;

        if (audioContext) {
            audioContext.close().catch(console.error);
            audioContext = null;
        }

        // 清空音频片段
        audioSegments.length = 0;
    }

    // 清理函数
    onUnmounted(() => {
        stopTimer()
        if (mediaRecorder && mediaRecorder.state !== 'inactive') {
            mediaRecorder.stop()
            mediaRecorder.stream.getTracks().forEach(track => track.stop())
        }
        if (audioStream) {
            audioStream.getTracks().forEach(track => track.stop())
        }
        // 清除轮询定时器
        if (pollTimer) {
            clearTimeout(pollTimer);
            pollTimer = null;
        }
    })

    return {
        /** 开始录音方法 */
        startRecording,
        /** 停止录音方法 */
        stopRecording,
        /** 暂停录音方法 */
        pauseRecording,
        /** 继续录音方法 */
        resumeRecording,
        /**取消录音 */
        cancelRecording,
        /** 上传录音文件方法 */
        uploadAudio,
        /** 是否正在录音中 */
        recording,
        /** 是否处于暂停状态 */
        isPaused,
        /** 录音文件URL */
        audioUrl,
        /** 当前录音时长（秒） */
        currentTime,
        /** 加载配置 */
        loadingConfig,
        /** 消息回调 */
        messageCallBack,
        /** 请求ID */
        reqId,
    }
}

// 修改 audioBufferToWav 函数，移除压缩选项
function audioBufferToWav(buffer: AudioBuffer): Uint8Array {
    const numChannels = 1; // 强制使用单声道
    const sampleRate = 16000; // 使用16kHz采样率
    const format = 1; // PCM
    const bitDepth = 16;

    const bytesPerSample = bitDepth / 8;
    const blockAlign = numChannels * bytesPerSample;

    const wav = new ArrayBuffer(44 + buffer.length * blockAlign);
    const view = new DataView(wav);

    // WAV 文件头
    writeString(view, 0, 'RIFF');
    view.setUint32(4, 36 + buffer.length * blockAlign, true);
    writeString(view, 8, 'WAVE');
    writeString(view, 12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, format, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * blockAlign, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, bitDepth, true);
    writeString(view, 36, 'data');
    view.setUint32(40, buffer.length * blockAlign, true);
    // 写入音频数据
    const data = new Float32Array(buffer.length);
    const channel = buffer.getChannelData(0);
    for (let i = 0; i < buffer.length; i++) {
        data[i] = channel[i];
    }

    let offset = 44;
    for (let i = 0; i < data.length; i++) {
        const sample = Math.max(-1, Math.min(1, data[i]));
        view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
        offset += 2;
    }

    return new Uint8Array(wav);
}

function writeString(view: DataView, offset: number, string: string) {
    for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
    }
}