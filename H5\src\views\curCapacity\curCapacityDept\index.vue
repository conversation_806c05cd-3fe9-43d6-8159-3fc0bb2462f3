<template>
    <div>
        <div class="card-header">
            <van-popover v-if="tabItem == 1" :actions="roleList" @select="changRole" placement="bottom-start">
                <template #reference>
                    <div class="selectOption-item">
                        <span>{{ curSelectRole.text }}</span>
                        <van-icon name="play" class="play" color="#1A76FF" />
                    </div>
                </template>
            </van-popover>
            <!-- <van-popover v-else :actions="areaList" @select="changArea" placement="bottom-start">
                <template #reference>
                    <div class="selectOption-item">
                        <span>{{ curSelectArea.text }}</span>
                        <van-icon name="play" class="play" color="#1A76FF" />
                    </div>
                </template>
            </van-popover> -->


            <cusTabs :tabList="tabList" v-model:curSelectTab="tabItem" @changeSelectTab="changeSelectTab">
            </cusTabs>
        </div>
        <div class=' container_ mt-6'>
            <div class="container__header">
                <div>
                    <img src="@/assets/resultOptimization.svg" alt="" srcset="">
                    <span class="ml-6">{{ title }}</span>
                </div>
            </div>


            <div class="container_body ">
                <div v-for="(item, index) in progressData" :key="index" class="item">
                    <div style="display: flex;align-items: center;justify-content: space-between;">
                        <div class="label">{{ item.name }}</div>
                        <div class="go-button" @click="GO(item)" v-if="!item.areaIdL2">
                            <span>GO</span>
                            <van-icon name="arrow" color="#fff" />
                        </div>
                    </div>
                    <div class="numbers">

                        <span class="jf">
                            <div>{{ item.points }}</div>
                            <div>积分</div>
                        </span>
                        <span class="ty">
                            <div>{{ item.tianYi }}</div>
                            <div>天翼</div>
                        </span>
                        <span class="kd">
                            <div>{{ item.kuanDai }}</div>
                            <div>宽带</div>
                        </span>
                        <span class="rh">
                            <div>{{ item.ronghe }}</div>
                            <div>融合</div>
                        </span>
                        <span class="zgz">
                            <div>{{ item.xinliangZgz }}</div>
                            <div>新装高套</div>
                        </span>

                    </div>


                </div>

            </div>




        </div>
    </div>
</template>

<script setup lang="ts">
import cusTabs from "@/components/cusTabs/index.vue";
import { getcurCapacityAPI } from "@/api/home";
import { getlistDeptOrSubOptions } from "@/hooks_modules/getlistDeptOrSubOptions";
import { getlistStaffRole } from "@/hooks_modules/getlistStaffRole";
import { useUserInfo } from "@/stores/userInfo";
import { mapSort } from "@/utils/mapSort";
interface Progress {
    name: string;
    points?: number
    tianYi: number
    kuanDai: number;
    ronghe: number;
    xinliangZgz: number;
    [key: string]: any
}
interface SelectType {
    text: string,
    value: number | string | undefined
}
const userInfoStore = useUserInfo()
const { areaList } = getlistDeptOrSubOptions()
const { roleList } = getlistStaffRole()


const progressData = ref<Progress[]>([


])


const tabList = ref([
    { text: '条线', value: 1 },
    { text: '分局', value: 2 },
])






const curSelectRole = reactive<SelectType>({
    text: '全部',
    value: undefined
})
const curSelectArea = reactive<SelectType>({
    text: '全部',
    value: undefined
})


function changRole(params: any) {
    curSelectRole.text = params.text
    curSelectRole.value = params.value
    loadCurCapacity()


}
function changArea(params: any) {
    curSelectArea.text = params.text
    curSelectArea.value = params.value
    loadCurCapacity()
}



function GO(item: Progress) {

    if (item.staffRole) {
        curSelectRole.value = item.staffRole
        curSelectRole.text = item.name
    } else if (item.areaId) {
        curSelectArea.value = item.areaId
        curSelectArea.text = item.name
    }

    loadCurCapacity()
}


//当前选择的是 分局 条线 还是区域
const tabItem = ref(1)
function changeSelectTab() {
    curSelectArea.value = undefined
    curSelectArea.text = '全部'

    curSelectRole.value = undefined
    curSelectRole.text = '全部'

    loadCurCapacity()


}


function loadCurCapacity() {
    let params: any = {
        type: tabItem.value,
        areaIdL2: curSelectArea.value,
        staffRole: curSelectRole.value
    }
    getcurCapacityAPI(params).then((res: any) => {
        if (res.code == 200) {
            res.data = mapSort(res.data)
            progressData.value = res.data || []
        }
    })
}



const title = computed(() => {
    let title = '南京分公司当前产能'
    const orgName = userInfoStore.userInfo.orgName
    let areaName = curSelectArea.text == '全部' ? '' : curSelectArea.text
    let roleName = curSelectRole.text == '全部' ? '' : curSelectRole.text


    if (tabItem.value == 1) {
        title = roleName == '' ? `${orgName}分公司各条线当前产能` : `${orgName}分公司${roleName}当前产能`
    } else {
        title = areaName == '' ? `${orgName}分公司各分局当前产能` : `${orgName}分公司${areaName}当前产能`
    }


    return title
})


onMounted(() => {
    loadCurCapacity()
})
</script>

<style lang="scss" scoped>
.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    justify-content: flex-end;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 700;
        font-size: 16px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

    .selectOption-item {
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;
        // justify-content: space-between;
        position: relative;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
            position: absolute;
            right: 10px;
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }
}









.item {
    padding: 7px 14px;
    background: #EDF5FF;
    border-radius: 5px;
    margin-bottom: 8px;

    &:last-child {
        border-bottom: none;
    }

    .label {
        font-weight: 700;
        font-size: 14px;
        color: #3D3D3D;
    }

    .numbers {
        display: flex;
        align-items: center;
        justify-content: space-around;
        margin-top: 8px;

        span {
            display: flex;
            flex-direction: column;
            align-items: center;

            &>div:first-child {
                font-weight: 700;
                font-size: 18px;

            }

            &>div:last-child {
                margin-top: 2px;
                font-weight: 400;
                font-size: 12px;

            }
        }

        .jf {
            color: #34C759;
        }

        .ty {
            color: #FF6A1A;
        }

        .kd {
            color: #477BF3;
        }

        .rh {
            color: #FFC34A;
        }

        .zgz {
            color: #E950E1;
        }

    }
}



:deep(.van-popover__wrapper) {
    flex: 1 0 auto;
}

:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
    width: auto;
}
</style>