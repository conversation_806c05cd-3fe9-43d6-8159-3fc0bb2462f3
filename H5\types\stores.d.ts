declare global {
    //store
    type MenuItem = {
        id: number;                  // 菜单项的唯一标识符
        menuCode: string;            // 菜单编码
        menuName: string;            // 菜单名称
        parentCode: string;          // 父级菜单编码
        sort?: number;                // 排序字段
        type?: number;                // 菜单类型
        children?: MenuItem[];       // 可选的子菜单项
        icon?: string;
        icon_?: string;
        isLink?: boolean;
        path?: string;
    };

    interface UserInfosState {
        userInfo: Organization;
        token: string;
        isShowLh: boolean;
        AESKEY: string;
        menuList: MenuItem[];
        staffList: StaffVO[];
        isShowChatBi: boolean;
        chatBiUrl: string;
    }

    interface Organization {
        orgId?: string; // 组织 ID
        areaIdL2?: string; //分局id
        areaNameL2?: string; //分局名称
        orgName?: string; // 组织名称
        primaryRoleCode?: string; // 主要角色编码
        primaryRoleName?: string; // 主要角色名称
        staffName?: string; // 员工名称
        havePq?: number; //分局长是否有片区
        orgDept?: number //分局类型 1公众  2政企
        staffCode?: string;
        showOrgName?: string
    }
    interface StaffVO {
        staffCode?: string;
        staffName?: string;
        staffRoleName?: string;
        // staffId?: string;
        // orgId?: string;
    }




    interface ContributionItem {
        addFans: number;           // 增加粉丝数
        businessRecords: number;   // 商机录入
        channelName: string;       // 门店名称
        huoli: number;             // 活力值
        kuanDai: number;           // 宽带
        kuandaiXuyue: number;      // 宽带续约
        name: string;              // 名称
        outCall: number;           // 外呼
        points: number;            // 积分
        pqName: string;            // 片区名称
        repairOrder: number;       // 装维工单
        repairOrderTotal: number;   // 装维工单总量
        roleCode: string;          // 角色编码
        roleName: string;          // 角色名称
        ronghe: number;            // 融合
        tianYi: number;            // 天翼
        tianyiXuyue: number;       // 天翼续约
        type: number;              // 类型 (0片区 1门店 2个人)
        xinliangZgz: number;       // 新装高套

        gov5g: number; // 5G 相关数据
        govCloudComputer: number; // 云计算相关数据
        govFttrb: number; // FTTR-B 相关数据
        govHuZhuan: number; // 互转相关数据
        govShangZhuan: number; // 商转相关数据
        govTianyi: number; // 天翼相关数据
        govTianyiShilian: number; // 天翼视联相关数据
        govXiaowei: number; // 小微相关数据
        orgDept: number;            // 部门编号  2是政企
        [key: string]: any
    }
    interface ContributionLeader {
        type: number;             // 类型 (0片区 1门店 2个人)
        points: number;           // 积分
        tianYi: number;           // 天翼
        kuanDai: number;          // 宽带
        outCall: number;          // 外呼
        signCount: number;         //打卡
        repairOrder: number;      // 装维工单
        repairOrderTotal: number;  //装维工单总量
        businessRecords: number;  // 商机录入
        xinliangZgz: number;      // 新装高套
        ronghe: number;           // 融合
        tianyiXuyue: number;      // 天翼续约
        kuandaiXuyue: number;     // 宽带续约


        gov5g?: number; // 5G 相关数据
        govCloudComputer?: number; // 云计算相关数据
        govFttrb?: number; // FTTR-B 相关数据
        govHuZhuan?: number; // 互转相关数据
        govShangZhuan?: number; // 商转相关数据
        govTianyi?: number; // 天翼相关数据
        govTianyiShilian?: number; // 天翼视联相关数据
        govXiaowei?: number; // 小微相关数据
        orgDept?: number;            // 部门编号  2是政企
        [key: string]: any
    }




    //自定义lading
    interface LoadingDirectiveOptions {
        /** 控制加载状态的可见性 */
        visible: boolean;

        /** 自定义加载文本 */
        text?: string;

        /** 自定义图标，可以是字符串（HTML）或 Vue 组件 */
        icon?: string | Component;

        /** 自定义图标颜色 */
        iconColor?: string;

        /** 自定义图标大小 */
        iconSize?: string;

        /** 自定义文字颜色 */
        textColor?: string;

        /** 自定义文字大小 */
        textSize?: string;

        /** 自定义遮罩的背景颜色 */
        backgroundColor?: string;

        /** 自定义遮罩的圆角 */
        borderRadius?: string;

        /** 自定义样式类 */
        customClass?: string;

        /** 锁定滚动 */
        lock?: boolean;

        /** 自定义加载器样式类 */
        spinner?: string;

        /** 用于控制指令行为的附加属性 */
        modifiers?: {
            /** 是否将加载遮罩添加到 body 中 */
            body?: boolean;
        };

    }




    interface RowItem {
        span: number;
        name: string;
        props: string;
        default: any;
        [key: string]: any
    }


    //今日头条
    interface GoalCompletion {
        month: number; // 月份 (格式: YYYYMM)，默认值: 202409
        id: number; // 唯一标识符，可选
        points: number; // 积分目标，默认值: 0
        rongHe: number; // 融合目标，默认值: 0
        xinliangZgz: number; // 新装高套目标，默认值: 0
        completeDayPoints: number; // 当日完成积分，默认值: 0
        completeDayRongHe: number; // 当日完成融合，默认值: 0
        completeDayXinliangZgz: number; // 当日完成新装高套，默认值: 0
        completeMonthPoints: number; // 当月完成积分，默认值: 0
        completeMonthRongHe: number; // 当月完成融合，默认值: 0
        completeMonthXinliangZgz: number; // 当月完成新装高套，默认值: 0
        dailyPointsDenominator: number;  // 每日积分目标分母
        dailyRongHeDenominator: number;  // 每日融合目标分母
        dailyXinliangZgzDenominator: number;  //  每日新装高套目标分母

        [key: string]: any; // 动态键值，允许扩展其他属性
    }


    // 接口响应类型
    interface ApiResponse {
        code: number;
        msg: string;
        data?: any;
    }


}








export { }