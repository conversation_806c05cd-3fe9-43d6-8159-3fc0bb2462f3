<template>
  <div class="searchBox" :style="{
    height: activeName == 'signInForm' ? '100%' : ''
  }">
    <van-tabs v-model:active="activeName" title-active-color="#1A76FF" animated swipeable>
      <van-tab :title="item.title" :name="item.moudule_name" v-for="item in componentOption" :key="item.moudule_name">
        <template #title>
          <div style="display: flex;align-items: center;">
            <!-- <img :src="item.icon" alt="" srcset="" class="card_icon" /> -->
            <span class="card-content-title">{{ item.title }}</span>
          </div>

        </template>

        <component class="mt-5" :is="item.moudule_" :key="item.moudule_name" v-bind="item.props"></component>

      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup lang="ts">
import signInBranchDetail from "../signInBranchDetail/index.vue";
import signInForm from "./signInForm.vue";
import { useUserInfo } from "@/stores/userInfo";
const userInfoStore = useUserInfo()
const activeName = ref('signInForm')

const componentOption = [
  {
    title: '出征打卡',
    // icon: new URL(`../../template/MyDay/assets/images/chczIcon.png`, import.meta.url).href,
    moudule_: signInForm,
    moudule_name: 'signInForm',
    ref_name: 'signInForm',

  },
  {
    title: '出征总览',
    // icon: new URL(`../../template/MyDay/assets/images/signInBranchDetail.png`, import.meta.url).href,
    moudule_: signInBranchDetail,
    moudule_name: 'signInBranchDetail',
    ref_name: 'signInBranchDetail',
    props: {
      areaNameL2: userInfoStore.userInfo.showOrgName,
      isAreaNameL2: true,
    }

  }
]
onMounted(() => {



})

</script>


<style lang="scss" scoped>
.searchBox {
  overflow-y: scroll;
  overflow-x: hidden;
  background: #FBFCFF;
  border-radius: 10px 10px 0px 0px;
  min-height: 100%;
}

.card_icon {
  width: 17px;
  height: 17px
}

.card-content-title {
  margin-left: 3px;
  font-size: 14px;

  font-weight: 700;
  color: #3D3D3D;
  line-height: 18px;
}

:deep(.van-tabs) {
  height: 100%;

}

:deep(.van-tab__panel) {
  height: 100%;
}

:deep(.van-tabs__content) {
  height: calc(100% - var(--van-tabs-line-height));
  overflow-y: auto;

}




:deep(.van-tabs__nav) {
  // background-color: transparent;
}



:deep(.van-tabs__line) {
  height: 4px;
  background: #1A76FF;
  border-radius: 26px 26px 26px 26px;
}
</style>