<template>
    <div class="card-header">


        <van-popover :actions="actionType" @select="changeActionType" placement="bottom">
            <template #reference>
                <div class="selectOption-item" :style="{
                    justifyContent: actionTypeNameAlign
                }">
                    <span>{{ curActionTypeName }}</span>
                    <van-icon name="play" class="play" color="#1A76FF" />
                </div>
            </template>
        </van-popover>


        <cusTabs :tabList="tabList" v-model:curSelectTab="curSelectType" @changeSelectTab="changeSelectTab">
        </cusTabs>
    </div>
</template>

<script setup lang='ts'>
import cusTabs from "@/components/cusTabs/index.vue";
import { ref, reactive, onMounted, onUnmounted, computed, toRefs } from 'vue';
import { PopoverAction } from "vant";

type TabItem = {
    text: string,
    value: string | number
}

const emits = defineEmits(['changeSelectTab', 'changeSelectActionType'])
const props = defineProps({
    tabList: {
        type: Array as PropType<TabItem[]>,
        default: (): TabItem[] => []
    },
    actionType: {
        type: Array as PropType<PopoverAction[]>,
        default: () => []
    },
    actionTypeNameAlign: {
        type: String,
        default: 'left'
    }
})
const { tabList, actionType, actionTypeNameAlign } = toRefs(props)
const curSelectType = defineModel('curSelectTab', {
    type: Number,
    default: 0
})
const curSelectActionType = defineModel('curSelectActionType', {
    type: [String, Number],
    default: (): string | number  => {
        return ''
    }
})

const curActionTypeName = computed(() => {
    return actionType.value.find(item => item.value == curSelectActionType.value)?.text
})

function changeSelectTab(item: TabItem) {
    emits('changeSelectTab', item)
}
function changeActionType(item: any) {
    if (curSelectActionType.value == item.value) return
    curSelectActionType.value = item.value
    emits('changeSelectActionType', item)
}

// 生命周期钩子
onMounted(() => {

});


</script>

<style lang='scss' scoped>
.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 700;
        font-size: 16px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

}




.selectOption-item {
    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
    font-weight: 400;
    font-size: 14px;
    color: #1A76FF;
    padding: 8px;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    position: relative;

    .play {
        transform: rotate(90deg);
        margin-left: 3px;
        position: absolute;
        right: 10px;
    }
}

.selectOption-item:active {
    background: #d8dee6;
}

:deep(.van-popover__wrapper) {
    flex: 1 0 auto;
}

:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}
</style>