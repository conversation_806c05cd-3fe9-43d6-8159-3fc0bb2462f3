<template>
    <div class="box container_">
        <div class="box_top" style="height: auto;">
            <div class="Branch_name">
                {{ showOrgName }}
            </div>

        </div>
        <div class="selectOptionList">
            <div @click="changeSelete(item.value)" class="item" v-for="item in selectOptionList" :key="item.value"
                :class="item.value == curSelect ? 'activeItem' : ''">
                {{ item.text }}
            </div>
        </div>
        <div class="selectTime pb-20" @click="selectTime">
            <div>
                <div class="startTime">起始日期</div>
                <div class="timeValue" :class="formline.startTime ? 'activeTime' : ''">{{ formline.startTime || '选择日期'
                    }}
                </div>
            </div>
            <div>
                <img src="@/assets/arrows_right.svg" alt="" srcset="">
            </div>
            <div>
                <div class="startTime">截止日期</div>
                <div class="timeValue" :class="formline.endTime ? 'activeTime' : ''">{{ formline.endTime || '选择日期' }}
                </div>
            </div>
        </div>

        <div class="container__header" style="border-bottom: 0px;">
            <div>
                <van-popover placement="right-start">
                    <div class="popoverBody">
                        <li>派单总量：所有框架下的所有单量</li>
                        <li>已执行量：外呼时长大于0的数量</li>
                        <li>外呼量：外呼时长大于30秒的数量</li>
                        <li>待处理量：派单量-已执行量</li>
                        <li>以上数据均来自智慧营销，最终口径以业务为准</li>

                    </div>
                    <template #reference>
                        <img src="@/assets/sendOrdersInfo.svg" alt="" srcset="">
                        <span class="ml-6" style="font-weight: 500;">派单情况</span>
                        <van-icon class="ml-3" name="question" color="#1A76FF" />
                    </template>
                </van-popover>


            </div>
            <div class="orderType">
                <div class="orderType-item" :class="orderType == 'all' ? 'activeItem' : ''"
                    @click="changeOrderType('all')">全部派单
                </div>
                <div class="orderType-item ml-8" :class="orderType == 'frame' ? 'activeItem' : ''"
                    @click="changeOrderType('frame')">框架派单</div>
            </div>
        </div>
        <div class="container_body ">
            <div class="viewDetail mt-10 mb-10" v-if="orderType == 'frame'">
                <router-link class="van-haptics-feedback" :to="{
                    path: '/sendOrdersDetailLead',
                    query: {
                        startTime: formline.startTime,
                        endTime: formline.endTime,
                        orderType: orderType
                    }
                }">
                    查看重点派单<van-icon name="arrow" />
                </router-link>
            </div>

            <myEcharts class="myEcharts" :options="options"></myEcharts>
            <div class="viewDetail" v-if="orderType == 'frame'">
                <router-link :to="{
                    path: '/sendOrdersZoneContact',
                    query: {
                        startTime: formline.startTime,
                        endTime: formline.endTime,
                        orderType: orderType
                    }
                }">
                    <div class="van-haptics-feedback pt-10 pb-10">
                        查看战区触点<van-icon name="arrow" />
                    </div>
                </router-link>
            </div>

            <router-link :to="{
                path: '/sendOrdersList',
                query: {
                    startTime: formline.startTime,
                    endTime: formline.endTime,
                    orderType: orderType
                }
            }">
                <div class="optionList">
                    <div class="item" v-for="item in optionList" :key="item.text">
                        <div class="value" :style="{ color: item.color }">{{ item.value }}</div>
                        <div class="text">{{ item.text }}</div>
                    </div>
                </div>
            </router-link>
        </div>




        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />
    </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { searchHooks } from "@/hooks_modules/changeTimeHooks.ts";
import myEcharts from "@/components/charts/myCharts.vue";
import { getSendOrderListAPI } from "@/api/home";
import { useUserInfo } from "@/stores/userInfo";
import { sendOrdersHooks } from "./hooks/sendOrdersHooks";



const {
    showOrgName,
    options,
    optionList,
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    curSelect,
    selectOptionList,
    orderType,
    changeOrderType,
    changeSelete,
    selectTime,
    onConfirm
} = sendOrdersHooks()

onMounted(() => {

})


</script>

<style lang="scss" src="@/views/template/search/index.scss" scoped></style>
<style lang="scss" scoped>
.viewDetail {
    font-weight: 500;
    font-size: 12px;
    color: #1A76FF;
    line-height: 13px;
    text-align: right;

}

.startTime,
.timeValue {
    margin-bottom: 8px !important;
}



.selectOptionList {
    grid-template-columns: repeat(auto-fit, minmax(35px, 1fr));
    gap: 5px;
}

.orderType {
    display: flex;

    .orderType-item {
        font-weight: 500;

        border-radius: 20px;
        border: 1px solid #2079FF;
        font-weight: 400;
        font-size: 12px;
        color: #2079FF;
        line-height: 17px;
        padding: 3px 10px;
    }

    .activeItem {
        background: #2079FF;
        border: 1px solid #2079FF;
        color: #FFFFFF;
    }

}




.optionList {

    background: #EDF5FF;
    border-radius: 5px;
    // display: grid;
    // grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    display: flex;
    // justify-content: space-around;
    flex-wrap: wrap;
    // gap: 10px;
    margin-top: 10px;
    margin-bottom: 0px;

    .item {
        flex: 1 0 33.3333%;
        max-width: 33.33333%;
        display: flex;
        height: 100%;
        padding: 15px 5px;
        flex-direction: column;
        place-content: center;
        text-align: center;

        .text {
            font-weight: 500;
            font-size: 12px;
            color: #3D3D3D;
        }

        .value {
            font-family: 'DIN, DIN';
            font-weight: 700;
            font-size: 23px;
            color: #6282FD;
            margin-bottom: 7px;
        }
    }
}

.myEcharts {
    width: 100%;
    height: 180px;
}

.popoverBody {
    font-size: 13px;
    padding: 5px 10px;
}
</style>