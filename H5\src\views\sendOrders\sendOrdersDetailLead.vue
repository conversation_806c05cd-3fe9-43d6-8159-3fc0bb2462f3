<template>

    <div class="card container_">
        <div class="container__header">
            <div style="display: flex; align-items: center;">
                <img src="@/assets/sendOrdersInfo.svg" alt="" srcset="">
                <span class="ml-6" style="font-weight: 500;">重点派单</span>
            </div>


        </div>
        <div class="container_body ">
            <div class="table-container" @touchstart.stop>
                <table>
                    <thead>

                        <tr>
                            <th class="sticky">派单人员</th>

                            <th>S级</th>
                            <th>A级</th>
                            <th>B级</th>
                            <th>未接触</th>
                            <th>总计</th>
                        </tr>

                    </thead>
                    <tbody>
                        <tr v-for="(row, index) in tableData" :key="index">
                            <td class="sticky">
                                <div class="areaNameL2">
                                    {{ row.staffName }}
                                </div>
                            </td>
                            <td @click="gotoDetail(row, 'S')" :style="{
                                color: row.staffName == '总计' ? '' : '#0020F4',
                                textDecoration: row.staffName == '总计' ? '' : 'underline'
                            }">{{
                                row.scount }}</td>
                            <td @click="gotoDetail(row, 'A')" :style="{
                                color: row.staffName == '总计' ? '' : '#0020F4',
                                textDecoration: row.staffName == '总计' ? '' : 'underline'
                            }">{{
                                row.acount }}</td>
                            <td @click="gotoDetail(row, 'B')" :style="{
                                color: row.staffName == '总计' ? '' : '#0020F4',
                                textDecoration: row.staffName == '总计' ? '' : 'underline'
                            }">{{
                                row.bcount }}</td>
                            <td>{{ row.waitCount }}</td>
                            <td>{{ row.count }}</td>
                        </tr>

                    </tbody>



                </table>

            </div>
            <van-empty description="暂无数据" class="mt-8" style="background-color: #FBFCFF;border-radius: 15px;"
                v-if="tableData.length == 0" />
        </div>

    </div>


</template>

<script setup lang="ts">
import { searchHooks } from "@/hooks_modules/changeTimeHooks";

import {
    getSendOrderTableListAPI
} from "@/api/home.ts";

import { useRoute } from 'vue-router';
const route = useRoute();
const router = useRouter();
// 定义接口
interface TableRow {
    acount: number;
    bcount: number;
    scount: number;
    staffCode: string;
    staffName: string;
    waitCount: number;
    count: number;
}

// 在组件中引用接口
const tableData = ref<TableRow[]>([]);

function loadList() {
    let params = {
        startTime: route.query.startTime ? `${route.query.startTime} 00:00:00` : undefined,
        endTime: route.query.endTime ? `${route.query.endTime} 23:59:59` : undefined
    }
    getSendOrderTableListAPI(params).then((res: any) => {
        if (res.code == 200) {
            for (const item of res.data) {
                item.count = item.acount + item.bcount + item.scount
            }
            if (res.data.length > 0) {
                let sumAcount = 0, sumBcount = 0, sumScount = 0;
                for (const item of res.data) {
                    sumAcount += item.acount;
                    sumBcount += item.bcount;
                    sumScount += item.scount;
                }
                res.data.push({ staffName: '总计', staffCode: '', acount: sumAcount, bcount: sumBcount, scount: sumScount, waitCount: 0, count: sumAcount + sumBcount + sumScount })

            }

            tableData.value = res.data
        }
    })
}


function gotoDetail(row: TableRow, type: string) {
    if (row.staffCode) {
        router.push({
            path: '/sendOrdersDetail',
            query: {
                staffCode: row.staffCode,
                staffName: row.staffName,
                startTime: route.query.startTime,
                endTime: route.query.endTime,
                type: type
            }
        })
    }

}

onMounted(() => {

    loadList();
})
</script>

<style lang="scss" scoped>
.card {
    font-family: 'Source Han Sans, Source Han Sans';
    min-height: 100%;
    background: #fbfcff;
    border-radius: 10px ;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);





    .selectOption-item {
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;
        position: relative;
        padding-right: 25px;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
            position: absolute;
            right: 10px;
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }

    .table-container {
        width: 100%;
        overflow-x: auto;
        margin-top: 10px;

        padding-bottom: 10px;

        table {
            width: 100%;
            border-collapse: collapse;
            text-align: center;
            font-size: 13px;

            th,
            td {
                padding: 8px;
                border: 1px solid #ddd;

                white-space: nowrap; // 禁止换行
            }

            .areaNameL2 {
                min-width: 50px;
                white-space: normal;
            }

            .sticky {
                /* 固定表格内容的门店列 */
                position: sticky;
                left: 0px;
                background-color: #fff;
                /* 设置背景颜色以覆盖其他列 */
                z-index: 1;
                border: 1px solid #ddd;

                /* 确保门店列在最上层 */
            }

            .main-title {
                font-size: 13px;
                font-weight: bold;
                padding: 12px;
                background-color: #2e70ba;
                color: #fff;
                text-align: center;
            }

            thead tr:nth-child(n) th {
                background-color: #2e70ba;
                color: #fff;
                font-weight: bold;
                padding: 10px;
                text-align: center;
            }

            thead tr:nth-child(3) th {
                background-color: #2e70ba;
                color: #fff;
                font-weight: bold;
                padding: 8px;
            }



            tbody tr:nth-child(even) {
                background-color: #f2f2f2;
            }




            tbody tr:hover {
                background-color: #e6f7ff;
            }

            tbody td.highlight {
                background-color: #ffe58f;
                font-weight: bold;
            }
        }


    }


}
</style>