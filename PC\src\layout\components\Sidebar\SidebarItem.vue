<template>
  <div v-if="!item.hidden" :id="item.meta.title">
    <!-- 没有子路由的 -->
    <template
      v-if="
        isHaveChild &&
        (!onlyOneChild.children || onlyOneChild.noShowingChildren) &&
        !item.alwaysShow
      "
    >
      <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
        <el-menu-item
          :index="resolvePath(onlyOneChild.path)"
          :class="{ 'submenu-title-noDropdown': !isNest }"
        >
          <template v-if="onlyOneChild.meta.icon && onlyOneChild.meta.icon.includes('el-')">
            <el-icon>
              <component :is="onlyOneChild.meta.icon.replace('el-', '')" />
            </el-icon>
          </template>
          <svg-icon
            v-else
            :icon="onlyOneChild.meta.icon"
            class="sub-el-icon"
            :size="onlyOneChild.meta.size"
          ></svg-icon>
          <template #title>{{
            onlyOneChild.meta.title
          }}</template>
        </el-menu-item>
      </app-link>
    </template>

    <!-- 有子路由的 -->
    <el-sub-menu
      v-else
      ref="subMenu"
      :index="resolvePath(item.path)"
      :teleported="true"
    >
      <template #title>
        <el-icon v-if="(item.meta && item.meta.icon).includes('el-')">
          <component :is="(item.meta && item.meta.icon).replace('el-', '')" />
        </el-icon>

        <svg-icon
          v-else
          :icon="item.meta && item.meta.icon"
          class="sub-el-icon"
          :size="item.meta.size"
        ></svg-icon>
        <span>{{ item.meta.title}}</span>
      </template>

      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
        v-show="!child.meta.hidden"
      />
    </el-sub-menu>
  </div>
</template>

<script setup>
import path from "path-browserify";
import { isExternal } from "@/utils/validate";
// import Routeritem from "./Item";
import AppLink from "./Link.vue";
import { computed, onMounted, toRefs, ref } from "vue";
import { useSidebarStore } from "@/stores/layoutSetting/app.ts";
const SidebarState = useSidebarStore();
const device = computed(() => SidebarState.device);
const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  isNest: {
    type: Boolean,
    default: false,
  },
  basePath: {
    type: String,
    default: "",
  },
});
const { item, isNest, basePath } = toRefs(props);


let onlyOneChild = null;
const isHaveChild = computed(() => {
  let children = item.value.children || [];
  let showingChildren = children.filter((el) => {
    if (el.hidden) {
      return false;
    } else {
      onlyOneChild = el;
      return true;
    }
  });

  // 当只有一个子路由器时，默认显示子路由器
  if (showingChildren.length === 1) {
    return true;
  }
  // 如果没有要显示的子路由器，则显示父路由器
  if (showingChildren.length === 0) {
    onlyOneChild = { ...item.value, path: "", noShowingChildren: true };
    return true;
  }
  return false;
});

function resolvePath(routePath) {
  if (isExternal(routePath)) {
    return routePath;
  }
  if (isExternal(basePath.value)) {
    return basePath.value;
  }
  return path.resolve(basePath.value, routePath);
}

const subMenu = ref();
function fixBugIniOS() {
  const $subMenu = subMenu.value;
  if ($subMenu) {
    const handleMouseleave = $subMenu.handleMouseleave;
    $subMenu.handleMouseleave = (e) => {
      if (device.value === "mobile") {
        return;
      }
      handleMouseleave(e);
    };
  }
}

onMounted(() => {
  fixBugIniOS();
  //   console.log(item.value, 'item');
  //   console.log(onlyOneChild, 'onlyOneChild');
  //   console.log(isHaveChild.value, 'isHaveChild');
});
</script>
