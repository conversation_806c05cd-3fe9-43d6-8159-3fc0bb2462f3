import JSEncrypt from "jsencrypt";
var decrypt = new JSEncrypt(); //创建解密对象实例
//之前生成的秘钥(把自己生成的密钥钥粘到对应位置)
var priKey =
  "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAId4xEhwZ8s49HCUbl6of4D80rmIUEoUDoJw77RH3ePoiorK3PvZqnVXcGY7LcdHHVTSX1DH+/5nvgyUrsn89XvB4G63PGWZHKq+a96WBYPDll37ERoDDOwv/rtk7gpQ+TQJrQKNDVhQ4sktMVWAYnyoPmE1h6bNHz+nDYja7JSlAgMBAAECgYAKa9phnjAF3JxFOs3gv0KLuCc3uYo/oJpbI3f1HOAvY5igw6lLQHvR6geN/bhwT4Ksn0BQ1QmIEQ/4JMnrZOe3YFMhDRz17UtKjzViat5UlkE/tt7/+eXE3jP2VUuVRtjfj/iGS9qZjMvTCXroCme5FhpG58axJ4VFTIHN4OCZswJBAL2YtVxWt/5a/NYvgwEW8VqQLNTbcFzsw+Aa9jgjDcvOhIyXrqbVD/vnxjcoFZ5wfFZmpP6qu1I9MqQm6jkziRsCQQC26zY7inPUvAzjcKc6nBWeHxm1eGs4z/b07biRnjIyVt4PeTIPwxCvF+m77EeCSmG4xxrWYPkT7u6utG5OhfU/AkAhrs6RkApyEsIWXyx79hJ8z15XloCY0ate1gtmH7wkuBTOUWhC1VlYnG2XgPSOSFeele+8GqLa0mp7xToZXXF/AkBjzO+ZCtBp+HQbv8IuEy0kSZmwTlRJ+gWm2vEB2ktO9eBQlvJ+cRJwRd7MHn1hlA2/UDrPTrd3bbyia2VnyGTnAkBp3WL0k+UEJfadIBmpgKwMQ/VEKWU7h8x02GILCnMmw8kMufkldfrtMvlpbrf8oZynrW+XUlcTB9gjxzXmpDK9";
decrypt.setPrivateKey(priKey); //设置秘钥
export function uncryptedFun(data:string) {
  var uncrypted = decrypt.decrypt(data); //解密之前拿公钥加密的内容
  return uncrypted;
}

// const uncryptedFun = (data:string) => {

//   let decrypt = new JSEncrypt()
//   //之前生成的秘钥(把自己生成的密钥钥粘到对应位置)
//   const priKey = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAId4xEhwZ8s49HCUbl6of4D80rmIUEoUDoJw77RH3ePoiorK3PvZqnVXcGY7LcdHHVTSX1DH+/5nvgyUrsn89XvB4G63PGWZHKq+a96WBYPDll37ERoDDOwv/rtk7gpQ+TQJrQKNDVhQ4sktMVWAYnyoPmE1h6bNHz+nDYja7JSlAgMBAAECgYAKa9phnjAF3JxFOs3gv0KLuCc3uYo/oJpbI3f1HOAvY5igw6lLQHvR6geN/bhwT4Ksn0BQ1QmIEQ/4JMnrZOe3YFMhDRz17UtKjzViat5UlkE/tt7/+eXE3jP2VUuVRtjfj/iGS9qZjMvTCXroCme5FhpG58axJ4VFTIHN4OCZswJBAL2YtVxWt/5a/NYvgwEW8VqQLNTbcFzsw+Aa9jgjDcvOhIyXrqbVD/vnxjcoFZ5wfFZmpP6qu1I9MqQm6jkziRsCQQC26zY7inPUvAzjcKc6nBWeHxm1eGs4z/b07biRnjIyVt4PeTIPwxCvF+m77EeCSmG4xxrWYPkT7u6utG5OhfU/AkAhrs6RkApyEsIWXyx79hJ8z15XloCY0ate1gtmH7wkuBTOUWhC1VlYnG2XgPSOSFeele+8GqLa0mp7xToZXXF/AkBjzO+ZCtBp+HQbv8IuEy0kSZmwTlRJ+gWm2vEB2ktO9eBQlvJ+cRJwRd7MHn1hlA2/UDrPTrd3bbyia2VnyGTnAkBp3WL0k+UEJfadIBmpgKwMQ/VEKWU7h8x02GILCnMmw8kMufkldfrtMvlpbrf8oZynrW+XUlcTB9gjxzXmpDK9";
//   decrypt.setPrivateKey(priKey); //设置秘钥
//   const uncrypted = decrypt.decrypt(data); //解密之前拿公钥加密的内容
//   return uncrypted;
// }

// const decryptLongString = (data:any) => {
//   // const maxLength = 117;  // 对于 1024 位密钥
//   let decrypt = new JSEncrypt()
//   const priKey = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAId4xEhwZ8s49HCUbl6of4D80rmIUEoUDoJw77RH3ePoiorK3PvZqnVXcGY7LcdHHVTSX1DH+/5nvgyUrsn89XvB4G63PGWZHKq+a96WBYPDll37ERoDDOwv/rtk7gpQ+TQJrQKNDVhQ4sktMVWAYnyoPmE1h6bNHz+nDYja7JSlAgMBAAECgYAKa9phnjAF3JxFOs3gv0KLuCc3uYo/oJpbI3f1HOAvY5igw6lLQHvR6geN/bhwT4Ksn0BQ1QmIEQ/4JMnrZOe3YFMhDRz17UtKjzViat5UlkE/tt7/+eXE3jP2VUuVRtjfj/iGS9qZjMvTCXroCme5FhpG58axJ4VFTIHN4OCZswJBAL2YtVxWt/5a/NYvgwEW8VqQLNTbcFzsw+Aa9jgjDcvOhIyXrqbVD/vnxjcoFZ5wfFZmpP6qu1I9MqQm6jkziRsCQQC26zY7inPUvAzjcKc6nBWeHxm1eGs4z/b07biRnjIyVt4PeTIPwxCvF+m77EeCSmG4xxrWYPkT7u6utG5OhfU/AkAhrs6RkApyEsIWXyx79hJ8z15XloCY0ate1gtmH7wkuBTOUWhC1VlYnG2XgPSOSFeele+8GqLa0mp7xToZXXF/AkBjzO+ZCtBp+HQbv8IuEy0kSZmwTlRJ+gWm2vEB2ktO9eBQlvJ+cRJwRd7MHn1hlA2/UDrPTrd3bbyia2VnyGTnAkBp3WL0k+UEJfadIBmpgKwMQ/VEKWU7h8x02GILCnMmw8kMufkldfrtMvlpbrf8oZynrW+XUlcTB9gjxzXmpDK9";
//   decrypt.setPrivateKey(priKey); //设置秘钥
//   let decrypted = "";
//   for (let i = 0; i < data.length; i += 172) {  // 128 字节加密后 Base64 编码约为 172 字符
//       const segment = data.substring(i, i + 172);
//       decrypted += decrypt.decrypt(segment);
//   }

//   return decrypted;

// }