<template>
    <div class='highPlanLock container_ mt-16'>
        <div class="container__header">
            <div style="position: relative;flex: 1;">
                <img src="@/views/template/processContro/assets/processContro.svg" alt="" srcset="">
                <span class="ml-6">高套封闭</span>
                <div class="tips">
                    <!-- <van-icon name="info-o" color="#6A98FB" /> -->
                    数据统计日期为：{{ (highPlanLockData.day || '').replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3') }}
                </div>
            </div>
            <div style="display: flex;align-items: center;justify-content: space-between;">
                <van-popover :actions="actionsList" @select="changeSelete" placement="bottom-end">
                    <template #reference>
                        <div class="selectOption-item">
                            <span>{{ curSelectTime.text }}</span>
                            <van-icon name="play" class="play" color="#1A76FF" />
                        </div>
                    </template>
                </van-popover>
            </div>


        </div>

        <div class="container_body">
            <van-swipe indicator-color="#66C6F2" @change="onChange">
                <van-swipe-item>
                    <transition name="fade-transform">
                        <div class="container_body_content" v-if="0 === current"
                            :style="manageType == '个人' && curSelectTime.text != '当月' ? 'grid-template-columns: repeat(1, 1fr);' : ''">

                            <template v-if="manageType == '个人' && curSelectTime.text != '当月'">
                                <div class="container_body_content_item">
                                    <myCharts class="myCharts" :options="getBarChartOption(true)" />
                                </div>

                            </template>
                            <template v-else>
                                <div class="container_body_content_item">
                                    <myCharts class="myCharts" :options="getPieChartOption('新装')" />
                                    <div class="container_body_content_bottom" @click="viewDetails('新装')">
                                        <div class="stats-item">
                                            <div class="stats-value">{{ highPlanLockData.newEquipment }}户</div>
                                            <div class="stats-label">新装高套</div>
                                        </div>
                                        <div class="stats-item">
                                            <div class="stats-value">{{ highPlanLockData.newEquipmentFold }}户</div>
                                            <div class="stats-label">新装高套<br>(折算)</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="container_body_content_item">
                                    <myCharts class="myCharts" :options="getPieChartOption('存量')" />
                                    <div class="container_body_content_bottom" @click="viewDetails('存量')">
                                        <div class="stats-item">
                                            <div class="stats-value">{{ highPlanLockData.stockEquipment }}户</div>
                                            <div class="stats-label">存量高套</div>
                                        </div>
                                        <div class="stats-item">
                                            <div class="stats-value">{{ highPlanLockData.stockEquipmentFold }}户</div>
                                            <div class="stats-label">存量高套<br>(折算)</div>
                                        </div>
                                    </div>
                                </div>
                            </template>

                        </div>
                    </transition>
                </van-swipe-item>
                <!-- 个人暂不支持跳转至详情页 -->
                <van-swipe-item v-if="manageType != '个人'">
                    <transition name="fade-transform">
                        <div class="container_body_content" v-if="1 === current"
                            style="grid-template-columns: repeat(1, 1fr);">
                            <div class="container_body_content_item">
                                <myCharts class="myCharts" :options="getBarChartOption()" />
                            </div>

                        </div>
                    </transition>
                </van-swipe-item>
            </van-swipe>
        </div>
    </div>
</template>

<script setup lang='ts'>
import { useRouter } from "vue-router";
import { selectTime } from "@/hooks_modules/selectTime";
import myCharts from "@/components/charts/myCharts.vue";
import { ref, reactive } from 'vue';
import { graphic } from 'echarts/core';
import { getHighValueAreaL2CountAPI, getHighValueStaffAPI, type HighValueStatsResponse } from "@/api/highPlanLock";
const router = useRouter();

const props = defineProps({
    manageType: {
        type: String,
        default: ''
    }
})

const { formline,
    defaultDate,
    maxDate,
    minDate,
    calendarIsShow,
    actionsList,
    curSelectTime,
    timeSection,
    changeSelete,
    onConfirm,
} = selectTime({
    source: 'highPlanLockArea',
    callback: () => {
        loadHighPlanLockData()
    }
})


const current = ref()
function onChange(index: any) {
    current.value = index;
}





const getPieChartOption = (type: any) => {
    const completedValue = type == '新装' ? Number(highPlanLockData.completionRate) : Number(highPlanLockData.completionRateStock)
    const uncompletedValue = 100 - completedValue


    let option = {
        // 环形图中间默认显示文字
        title: [
            {
                text: `${type}`,
                left: "center",
                top: "30%",
                textStyle: {
                    fontSize: 14,
                    fontWeight: "bold",
                    color: "#1A76FF",
                },
            },
            {
                // 成交金额文本
                text: `完成率:${completedValue}%`,
                left: "center",
                top: "42%",
                textStyle: {
                    fontSize: 9,
                    color: "#1A76FF",
                    lineHeight: 20,
                },
            },
        ],

        series: [
            {
                z: 2,
                name: "环形图",
                type: "pie",
                radius: ["55%", "90%"],
                center: ['center', "50%"],
                avoidLabelOverlap: false,
                labelLine: {
                    show: false,
                },
                label: {
                    show: false
                },
                color: ['#5C64FF', '#B2D5FD'],
                data: [
                    { value: completedValue, name: "存量" },
                    { value: uncompletedValue, name: "未完成" },
                ],
            }
        ],
    };
    return option
}


const getBarChartOption = (isSpecial = false) => {


    let colorList: string[] = []
    let isAllZero: boolean = false
    // 如果全为0，则使用最小值0.1来显示柱状图
    const minValue = 0.1
    let barData: string[] | number[] = []
    const { newEquipment, newEquipmentFold, stockEquipment, stockEquipmentFold, dismantleEquipment, removeEquipment, highEquipmentPriority } = highPlanLockData
    let XData: string[] = []
    if (isSpecial) {
        colorList = [
            '#FFA600',
            '#FEDB65',
            '#026DB2',
            '#12FEE0',
            '#6DD400',
            '#44D7B6',
            '#5C64FF',
            '#6988F8',
        ]
        isAllZero = newEquipment === 0 && newEquipmentFold === 0 && stockEquipment === 0 && stockEquipmentFold === 0
        barData = isAllZero
            ? [minValue, minValue, minValue, minValue]
            : [Math.abs(stockEquipmentFold), Math.abs(stockEquipment), Math.abs(newEquipmentFold), Math.abs(newEquipment),]
        XData = ['存量高套(折算)', '存量高套', '新装高套(折算)', '新装高套',]
    } else {
        colorList = [
            '#5C64FF',
            '#6988F8',
            '#0E5FFF',
            '#2DE1FD',
            '#8221F1',
            '#B26DF6',
        ]
        XData = ['拆机高套', '降档高套', '高套停机']
        isAllZero = dismantleEquipment === 0 && removeEquipment === 0 && highEquipmentPriority === 0
        barData = isAllZero
            ? [minValue, minValue, minValue]
            : [Math.abs(dismantleEquipment), Math.abs(removeEquipment), Math.abs(highEquipmentPriority)]

    }




    // 检查是否所有数据都为0


    let option = {
        grid: {
            top: "0%",
            bottom: "10%",
            left: "25%",
            right: "20%",
        },
        yAxis: [
            {
                data: XData,
                axisLabel: {
                    show: true,
                },
                splitLine: {
                    show: false,
                },
                axisTick: {
                    show: false,
                },
                axisLine: {
                    show: false,
                },
            },
        ],
        xAxis: {
            show: false,
        },
        series: [
            {
                z: 1,
                type: 'bar',
                data: barData,
                barWidth: 20,
                zlevel: 1,
                showBackground: false,
                itemStyle: {
                    barBorderRadius: [0, 20, 20, 0],
                    color: function (params: any) {
                        var index = params.dataIndex + params.dataIndex;
                        const colorStops = [{
                            offset: 0,
                            color: colorList[index]
                        }, {
                            offset: 1,
                            color: colorList[index + 1]
                        }];
                        return new graphic.LinearGradient(0, 0, 1, 0, colorStops)
                    },
                },
                label: {
                    color: '#333333',
                    show: true,
                    position: 'right',
                    distance: 4,
                    textStyle: {
                        fontSize: 12,
                    },
                    formatter: function (params: any) {
                        return (isAllZero ? 0 : params.value);
                    }
                },
            },
        ],
    };

    return option
}



// 使用reactive并通过Object.assign来更新数据。
const highPlanLockData = reactive({
    newEquipment: 0,
    newEquipmentFold: 0,
    completionRate: '0',
    stockEquipment: 0,
    stockEquipmentFold: 0,
    completionRateStock: '0',
    dismantleEquipment: 0,
    removeEquipment: 0,
    highEquipmentPriority: 0,
    day: ''
});

const loadHighPlanLockData = () => {
    const params = {
        type: curSelectTime.value,
    }
    const API = props.manageType === '分局' ? getHighValueAreaL2CountAPI : getHighValueStaffAPI

    API(params).then((res: any) => {
        if (res.code == 200) {
            const data = res.data || {};
            // 使用临时对象收集所有更新的值
            const updates = {
                newEquipment: data.xzgt ?? 0,
                newEquipmentFold: data.xzgtzk ?? 0,
                completionRate: ((data.xzgtRate ?? 0) * 100).toFixed(2),
                stockEquipment: data.clgt ?? 0,
                stockEquipmentFold: data.clgtzk ?? 0,
                completionRateStock: ((data.clgtRate ?? 0) * 100).toFixed(2),
                dismantleEquipment: data.gtcj ?? 0,
                removeEquipment: data.gtjd ?? 0,
                highEquipmentPriority: data.gttj ?? 0,
                day: data.day || data.monthEnd || ''
            };

            // 使用Object.assign一次性更新所有值
            Object.assign(highPlanLockData, updates);

        }

    })

}


const viewDetails = (type: string) => {
    if (props.manageType === '个人') return
    router.push({
        path: '/highPlanLockDetail',
        query: {
            type: type,
            manageType: props.manageType,
            time: curSelectTime.text,
            timeType: curSelectTime.value,
            timesSection: highPlanLockData.day.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3')
        }
    })
}


onMounted(() => {
    actionsList.value[0].text = '日报';
    curSelectTime.text = '日报'
    current.value = 0
    loadHighPlanLockData()
})
</script>

<style lang="scss" scoped>
.selectOption-item {
    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
    font-weight: 400;
    font-size: 14px;
    color: #1A76FF;
    padding: 8px;
    display: flex;
    align-items: center;

    .play {
        transform: rotate(90deg);
        margin-left: 3px;
    }
}

.selectOption-item:active {
    background: #d8dee6;
}

.container_body {
    padding: 10px 0px;
}

.more {
    font-size: 14px;
    color: #3D3D3D;
    font-weight: 500;
}

.container_body {
    .container_body_content {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;

        .container_body_content_item {
            // background: #fff;
            border-radius: 10px;
            padding: 5px;
            display: flex;
            flex-direction: column;

            .container_body_content_bottom {
                display: flex;

                .stats-item {
                    flex: 1;
                    text-align: center;

                    .stats-value {
                        font-size: 18px;
                        font-weight: 600;
                        color: #1A76FF;
                        margin-bottom: 4px;
                    }

                    .stats-label {
                        font-size: 12px;
                        color: #666666;
                    }
                }

            }
        }

    }

    .myCharts {
        width: 100%;
        height: 125px;
    }


}


.tips {
    position: absolute;
    left: 22px;
    bottom: -18px;
    font-size: 10px;
    color: #464646;
}

::v-deep(.van-swipe__indicator) {
    background-color: #a8a8a8;
}
</style>