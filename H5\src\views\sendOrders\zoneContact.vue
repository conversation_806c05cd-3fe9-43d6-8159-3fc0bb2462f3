<template>
    <div>
        <div class="card container_">
            <div class="container__header">
                <div style="display: flex; align-items: center;">
                    <van-icon name="arrow-left" size="4.5vw" class="mr-5" @click="router.go(-1)" />
                    <van-popover placement="bottom-start">
                        <div class="popoverBody">
                            <li>战区触点：是所有营业员（含店长）、装维和网格人员的执行情况汇总</li>
                        </div>
                        <template #reference>
                            <img src="@/assets/sendOrdersInfo.svg" alt="" srcset="">
                            <span class="ml-6" style="font-weight: 500;">派单情况</span>
                            <van-icon class="ml-3" name="question" color="#1A76FF" />
                        </template>
                    </van-popover>


                </div>

            </div>


            <div class="container_body mb-18">
                <div class="mb-20 mt-20 " style="display: flex;align-items: center;">
                    <span class="itemName">区域合计</span>
                </div>
                <div class="list">
                    <div class="optionList" style="padding: 0px;">
                        <div class="item" v-for="item in optionList" :key="item.text">
                            <div class="value" :style="{ color: item.color }">{{ countList[item.props as keyof
                                CountList] }}
                            </div>
                            <div class="text">{{ item.text }}</div>

                        </div>
                    </div>

                </div>
            </div>
            <div class="container_body ">
                <div class="list">
                    <div v-for="(staffItem, index) in roleOptionsList" :key="index">
                        <div class="mb-8 mt-5"
                            style="display: flex;align-items: center;justify-content: space-between;">
                            <span class="itemName">{{ staffItem.staffRoleName }}</span>
                            <div class="go-button" @click="GO(staffItem)">
                                <span>GO</span>
                                <van-icon name="arrow" color="#fff" />
                            </div>
                        </div>
                        <div class="optionList mb-20">

                            <div class="item" v-for="item in optionList" :key="item.text">
                                <div class="value" :style="{ color: item.color }">{{ staffItem[item.props] }}</div>
                                <div class="text">{{ item.text }}</div>

                            </div>
                        </div>

                    </div>

                </div>

            </div>

        </div>

        <div class="card mt-15 container_">
            <div class="container__header">
                <div style="display: flex; align-items: center;">
                    <van-icon name="arrow-left" size="4.5vw" class="mr-5" @click="router.go(-1)" />
                    <img src="@/assets/sendOrdersInfo.svg" alt="" srcset="">
                    <span class="ml-6" style="font-weight: 500;">派单承接量情况</span>
                </div>
            </div>

            <div class="container_body ">

                <myEcharts class="myEcharts" :options="getEchartsOption('dispatchNum')"></myEcharts>
            </div>

        </div>
        <div class="card mt-15 container_">
            <div class="container__header">
                <div style="display: flex; align-items: center;">
                    <van-icon name="arrow-left" size="4.5vw" class="mr-5" @click="router.go(-1)" />
                    <img src="@/assets/sendOrdersInfo.svg" alt="" srcset="">
                    <span class="ml-6" style="font-weight: 500;">派单外呼量情况</span>
                </div>
            </div>

            <div class="container_body ">
                <myEcharts class="myEcharts" :options="getEchartsOption('outCallNum')"></myEcharts>

            </div>

        </div>


    </div>
</template>

<script setup lang="ts">
import myEcharts from "@/components/charts/myCharts.vue";
import { useRouter, useRoute } from "vue-router";
import { getSendOrderZoneContactAPI } from "@/api/home";
const route = useRoute()
const router = useRouter()

interface Option {
    text: string;  // 选项文本
    color: string;  // 颜色的十六进制代码
    props: string,
}
// 定义每个选项的类型
interface OptionList {
    dispatchNum: Option;  // 派单总量
    implementedNum: Option;  // 已执行量
    successResultNum: Option
    pengdingNum: Option;  // 待处理量
    [key: string]: any
}
interface CountList {
    dispatchNum: number;
    implementedNum: number;
    successResultNum: number;
    pengdingNum: number;
}
// 定义列表
interface DispatchData {
    dispatchNum?: string; // 调度次数
    implementedNum?: string; // 已执行次数
    dispatchNumRatio?: string
    outCallNumRatio?: string
    outCallNum?: string; // 外呼次数
    pengdingNum?: string; // 待处理次数
    staffRoleName: string; // 员工角色名称
    successConvertionTotal?: string; // 成功转化总数

    [key: string]: any
}
interface RoleOptionsList {
    shop_leader: DispatchData
    fix: DispatchData
    grid: DispatchData
}


const roleOptionsList = reactive<RoleOptionsList>({
    shop_leader: {
        staffRoleName: '店长(营业员)',
        staffRole: 'shop_leader',
        dispatchNumRatio: '0',
        outCallNum: '0',
        outCallNumRatio: '0',
        dispatchNum: '0',
        implementedNum: '0',
        successResultNum: '0',
        pengdingNum: '0',

    },
    grid: {
        staffRoleName: '网格助理',
        staffRole: 'grid',
        dispatchNumRatio: '0',
        outCallNum: '0',
        outCallNumRatio: '0',
        dispatchNum: '0',
        implementedNum: '0',
        successResultNum: '0',
        pengdingNum: '0',

    },
    fix: {
        staffRoleName: '装维',
        staffRole: 'fix',
        dispatchNumRatio: '0',
        outCallNum: '0',
        outCallNumRatio: '0',
        dispatchNum: '0',
        implementedNum: '0',
        successResultNum: '0',
        pengdingNum: '0',

    },
})

const optionList = reactive<OptionList>({
    dispatchNum: {
        text: '派单总量',
        color: '#1A76FF',
        props: 'dispatchNum'
    },
    implementedNum: {
        text: '已执行量',
        color: '#6282FD',
        props: 'implementedNum'

    },
    successResultNum: {
        text: '成功数',
        color: '#00AFA3',
        props: 'successResultNum'
    },
    pengdingNum: {
        text: '待处理量',
        color: '#FFA34D',
        props: 'pengdingNum'

    },
})


function getEchartsOption(type: string) {


    let data = Object.values(roleOptionsList).map(item => {
        return {
            name: item.staffRoleName ?? '',
            value: Number(item[type]) ?? 0,
            rate: item[`${type}Ratio`] ?? 0
        }
    })
    console.log(data);

    const color = ['#1A76FF', '#00afa3', '#0083FD']
    const option = {


        color: color,
        tooltip: {
            trigger: "item",
            formatter: function (params: any) {
                return `<span style="color:${params.color}">${params.marker}${params.name}: ${params.value}</span>`;
            }
        },
        legend: {
            orient: 'vertical',
            itemWidth: 16,
            itemHeight: 16,
            icon: 'rect',
            left: '40%',
            bottom: 'center',
            data: data,
            textStyle: {
                fontSize: 14,
                padding: [10, 0, 10, 5],
                rich: {
                    color0: { color: color[0], fontSize: 14 }, // For first label
                    color1: { color: color[1], fontSize: 14 }, // For second label
                    color2: { color: color[2], fontSize: 14 }, // For third label
                },
            },
            formatter: function (params: any) {
                const index = data.findIndex(item => item.name === params)
                const rate = data[index] ? data[index].rate : 0
                return `{color${index}|${params}:${rate}%}`;
            },


        },
        series: [
            {
                center: ['20%', '50%'],
                radius: ['0%', '75%'],
                type: 'pie',
                data: data,
                label: {
                    show: false,
                },
            }
        ]
    };

    return option

}


// 获取列表数据 
function laodSendOrderZoneContact() {
    let params = {
        startTime: route.query.startTime ? `${route.query.startTime} 00:00:00` : '',
        endTime: route.query.endTime ? `${route.query.endTime} 23:59:59` : '',
        areaIdL2: route.query.areaL2Id
    }
    getSendOrderZoneContactAPI(params).then((res: any) => {
        for (const item of res.data) {
            if (item.staffRole in roleOptionsList) {
                roleOptionsList[item.staffRole as keyof RoleOptionsList] = item;
            }
        }


    })

}


const countList: ComputedRef<CountList> = computed(() => {
    return Object.values(roleOptionsList).reduce(
        (acc, current) => {
            acc.dispatchNum += Number(current.dispatchNum || 0);
            acc.implementedNum += Number(current.implementedNum || 0);
            acc.successResultNum += Number(current.successResultNum || 0);
            acc.pengdingNum += Number(current.pengdingNum || 0);
            return acc;
        },
        {
            dispatchNum: 0,
            implementedNum: 0,
            successResultNum: 0,
            pengdingNum: 0,
        })
})


function GO(item: DispatchData) {
    router.push({
        path: '/sendOrdersZoneContactDetail',
        query: {
            staffRole: item.staffRole,
            staffRoleName: item.staffRoleName,
            areaIdL2: route.query.areaL2Id,
            startTime: (route.query.startTime as string) || '',
            endTime: (route.query.endTime as string) || ''
        }
    })

}




onMounted(() => {
    laodSendOrderZoneContact()
})
</script>


<style lang="scss" scoped>
.container_ {
    font-family: 'Source Han Sans, Source Han Sans';

    overflow-y: scroll;
    overflow-x: hidden;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
}

.card {
    font-family: 'Source Han Sans, Source Han Sans';
    padding-bottom: 32px;
    // background: #fbfcff;

    border-radius: 10px;

    .container_body {
        padding-top: 0px;
        padding-bottom: 0px;

    }


    .itemName {
        font-weight: 500;
        font-size: 18px;
    }


}

.list {
    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
    padding: 3px 12px;
}

.optionList {


    display: grid;
    grid-template-columns: repeat(4, 1fr);


    .item {

        display: flex;
        height: 100%;
        padding: 15px 5px;
        flex-direction: column;
        place-content: center;
        text-align: center;

        .text {
            font-weight: 500;
            font-size: 12px;
            color: #3D3D3D;
        }

        .value {
            font-family: 'DIN, DIN';
            font-weight: 700;
            font-size: 20px;
            color: #6282FD;
            margin-bottom: 7px;
        }
    }
}

.myEcharts {

    width: 100%;
    height: 150px;

}
</style>