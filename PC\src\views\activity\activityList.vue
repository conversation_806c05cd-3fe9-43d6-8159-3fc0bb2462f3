<template>
    <div>
        <div class="card-header">
            <div class="card-label">活动列表</div>
            <el-form :inline="true" class="demo-form-inline ml-35" label-suffix=":" label-width="0"
                label-position="left">
                <el-form-item>
                    <el-input v-model="activeName" style="width: 200px" placeholder="搜索活动主题" clearable>
                        <template #prefix>
                            <el-icon class="el-input__icon">
                                <search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
                <el-form-item>
                    <el-date-picker v-model="createDate" style="width: 200px" type="year" placeholder="请选择活动时间" />
                </el-form-item>
                <el-form-item>
                    <div class="refreshButton" @click="refresh">
                        <el-icon :size="18" color="#8e9295">
                            <Refresh />
                        </el-icon>
                    </div>
                </el-form-item>

            </el-form>
        </div>
        <div class="hr"></div>
        <div class="activity-list">

            <div class="add-activity" @click="dialogTableVisible = true">
                <div class="add-box">
                    <el-icon size="20" color="#5790E1">
                        <Plus />
                    </el-icon>
                </div>

                <div class="add-activity-text">新增活动</div>
            </div>

            <div v-for="(item, idx) in activityList" :key="item.id" class="activity-card" @click="gotoDetail(item)">
                <div class="activity-title">{{ item.name }}</div>
                <!-- <div class="activity-date">{{ item.date }}</div> -->
                <!-- <div class="activity-tags">
                    <span v-for="(tag, tIdx) in item.tags" :key="tIdx" class="activity-tag">{{
                        tag.name }}</span>
                </div> -->
                <!-- <div class="activity-content">
                    <div class="content">
                        {{ item.content }}
                    </div>

                    <span class="activity-more-btn">
                        <el-icon size="16" color="#C0C4CC">
                            <ArrowRight />
                        </el-icon>
                    </span>
                </div>
                <div class="activity-footer">
                    共{{ item.topicCount }}条主题
                </div> -->
            </div>


        </div>


        <!-- <el-pagination class="customPagination" @size-change="handleSizeChange" prev-icon="DArrowLeft"
            next-icon="DArrowRight" @current-change="handleCurrentChange" :page-sizes="[10, 50, 100, 200]" background
            :page-size="pageSize" :current-page="pageNumber" :pager-count="5"
            layout="prev, pager, next,sizes,jumper,total" :total="total"
            style="margin: 40px 60px 20px 0px; justify-content: end">
        </el-pagination> -->


        <el-dialog v-model="dialogTableVisible" title="新增活动" width="600" class="curtomDialog">

            <el-form ref="ruleFormRef" :model="formLine" :rules="rules" :inline="true" class="demo-form-inline"
                label-suffix=":" label-width="auto" label-position="left">
                <el-form-item label="活动名称" prop="name">
                    <el-input v-model="formLine.name" style="width: 300px" clearable placeholder="请输入活动名称"></el-input>
                </el-form-item>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="resetForm(ruleFormRef)">取消</el-button>
                    <el-button type="primary" @click="submitForm(ruleFormRef)">
                        确定提交
                    </el-button>
                </div>
            </template>
        </el-dialog>

    </div>
</template>

<script setup lang="ts">
// import { debounce } from "lodash-es";
import { createActivityAPI, getActivityListAPI } from "@/api/activity";
const activeName = ref()
const createDate = ref()
const dialogTableVisible = ref(false)
import type { FormInstance, FormRules } from 'element-plus'

interface RuleForm {
    name: string

}
const router = useRouter()
const ruleFormRef = ref<FormInstance>()
const formLine = reactive<RuleForm>({
    name: '',
})

const rules = reactive<FormRules<RuleForm>>({
    name: [
        { required: true, message: '请输入活动主题', trigger: 'blur' },
        { min: 2, max: 50, message: '活动主题长度在2到50个字符之间', trigger: 'blur' }
    ],
})

const pageNumber = ref(1)
const pageSize = ref(10)
const total = ref(0)

const activityList = ref<{ id: string, name: string }[]>([])

const submitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {

            createActivityAPI({ name: formLine.name }).then((res: any) => {
                if (res.code == 200) {
                    ElMessage.success('新增活动成功')
                    loadActivityList()
                    dialogTableVisible.value = false
                }
            })

        } else {

        }
    })
}

const resetForm = (formEl: FormInstance | undefined) => {
    if (!formEl) return
    formEl.resetFields()
    dialogTableVisible.value = false
}

const refresh = debounce(() => {
    loadActivityList()
}, 300, { leading: true, trailing: false, })
function handleSizeChange(val: number) {
    pageSize.value = val;
    loadActivityList()
}
function handleCurrentChange(val: number) {
    pageNumber.value = val;
    loadActivityList()

}


function loadActivityList() {
    getActivityListAPI({ name: activeName.value, year: createDate.value }).then((res: any) => {
        if (res.code == 200) {
            activityList.value = res.data
        }

    })
}
const gotoDetail = (item: any) => {
    router.push({
        path: '/seedOrderList',
        query: {
            id: item.id,
            activityNmae: item.name,
        }
    })
}
onMounted(() => [
    loadActivityList()
])
</script>

<style lang="scss" scoped>
.card-header {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-bottom: 10px;
}



.demo-form-inline {
    :deep(.el-form-item) {
        margin-bottom: 0px;
    }
}

.add-activity {
    box-sizing: border-box;
    width: 270px;
    height: 178px;

    border-radius: 6px 6px 6px 6px;
    border: 2px dashed #DCDCDC;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    cursor: pointer;
    user-select: none;

    .add-box {
        width: 56px;
        height: 56px;
        background: #D9E1FF;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .add-activity-text {
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.6);
        line-height: 22px;
        margin-top: 12px;
    }
}

.activity-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, 270px);
    gap: 20px;
}

.activity-card {
    border: 1px solid #DCDCDC;
    border-radius: 6px;
    box-sizing: border-box;
    height: 178px;
    padding: 15px 15px 10px 15px;
    margin-bottom: 16px;
    position: relative;
    border-bottom: 6px solid #458AFB;


    .activity-title {
        font-weight: 700;
        font-size: 20px;
    }

    .activity-date {
        margin-top: 5px;
        font-weight: 500;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.4);
    }

    .activity-tags {
        margin-bottom: 16px;
        margin-top: 10px;

        .activity-tag {
            padding: 4px 8px;

            background-color: #E7E7E7;
            border-radius: 4px;
            margin-right: 8px;
            font-weight: 500;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.9);
        }

        .activity-tag:first-child {
            background-color: #2BA471;
            color: #FFF;
        }
    }

    .activity-content {
        display: grid;
        grid-template-columns: auto 16px;
        align-items: center;
        margin-bottom: 16px;

        .content {
            font-weight: 500;
            font-size: 14px;
            color: #00000066;
            @include multiline-ellipsis(2);
        }

        .activity-more-btn {
            cursor: pointer;
            color: #5790E1;
        }
    }

    .activity-footer {
        font-weight: 500;
        font-size: 12px;
        color: #00000066;
        text-align: right;
    }
}
</style>