import { ElMessage } from 'element-plus'
const directives = {
  install: ((app:any) => {
    app.directive('Copy', (el:any, binding:any) => {
      el.onclick = () => {
        if (!binding.value || binding.value == '') {
          return false;
        }
        if (!navigator.clipboard) {
          fallbackCopyTextToClipboard(binding.value);
          return;
        }
        navigator.clipboard.writeText(binding.value).then(
          function () {
            ElMessage({
              message: " Copying  successful!",
              type: "success",
            });
          },
          function (err) {
            ElMessage({
              message: "Copying  unsuccessful",
              type: "error",
            });
          }
        )
      }
    });
  })
}

function fallbackCopyTextToClipboard(text:string) {
  // 1.Create a selectable element
  let textArea = document.createElement("textarea");
  textArea.value = text;

  // 2.Use positioning to prevent page scrolling
  textArea.style.top = "0";
  textArea.style.left = "0";
  textArea.style.position = "fixed";
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  try {
    let successful = document.execCommand("copy");
    let msg = successful ? "successful" : "unsuccessful";
    ElMessage({
      message: " Copying  successful!",
      type: "success",
    });
  } catch (err) {
    ElMessage({
      message: " Copying  unsuccessful!",
      type: "error",
    });
  }
  // 3.Remove element
  document.body.removeChild(textArea);
}
export default directives;
