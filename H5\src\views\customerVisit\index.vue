<template>
    <div class="container_ ">
        <div class="card ">

            <div class="container_body">
                <div class="container_body_header">
                    <img src="@/assets/defaultAvatar.png" alt="" srcset="" style="width: 100%;">
                    <div class="ml-15">
                        <div class="userName">{{ userInfoStore.userInfo.staffName }}</div>
                        <div class="primaryRoleName">{{ userInfoStore.userInfo.primaryRoleName }}</div>
                    </div>
                    <router-link :to="{
                        path: '/workData', query: {
                            isLeader: componentType == 'customerVisit' ? false : true
                        }
                    }">
                        <div class="fs12"
                            style="display: flex;align-items: center;color: #327FF0;justify-content: flex-end;">
                            <img src="./assets/workCollection.png" alt="" style="width: 17px;">
                            <span class="ml-3" style="font-weight: 500;">工作收集</span>
                        </div>
                    </router-link>


                </div>
                <div class="container_body_box">
                    <div>
                        <div class="first">本月拜访</div>
                        <div style="color: #3D3D3D;position: relative; z-index: 1;" class="mt-12">
                            <span class="number">{{ visitCount.month }}</span>
                            <span class="fs14">次</span>
                        </div>
                        <img class="a_img" src="./assets/month.png" alt="" srcset="">
                    </div>
                    <div>
                        <div class="first">累计拜访</div>
                        <div style="color: #3D3D3D;position: relative; z-index: 1;" class="mt-12">
                            <span class="number">{{ visitCount.total }}</span>
                            <span class="fs14">次</span>
                        </div>
                        <img class="a_img" src="./assets/Total.png" style="right: 3px;" alt="" srcset="">
                    </div>
                </div>
                <div class="history_list">
                    <div class="history_list_header">
                        <div class="first">最近拜访照片</div>
                        <router-link to="/visitHistoryList">
                            <div class="fs12"
                                style="display: flex;align-items: center;color: #327FF0;justify-content: flex-end;">
                                <img src="./assets/history.png" alt="" style="width: 17px;">
                                <span class="ml-3" style="font-weight: 500;">历史记录</span>
                            </div>
                        </router-link>

                    </div>
                    <div class="img_list">
                        <img v-imgPreview="[visitImageList.slice(0, 3), index]"
                            v-for="(item, index) in visitImageList.slice(0, 3)" :src="item" alt="" srcset=""
                            :key="index">
                    </div>
                </div>
            </div>

        </div>

        <div class="card mt-14">
            <div class="container__header">
                <div>
                    <img src="./assets/customerVisit.svg" alt="" srcset="" />
                    <span class="ml-6">客户拜访</span>
                </div>
            </div>
            <div class="container_body dictList">
                <div class="dictItem" :class="curSelectType.value == item.value ? 'activeItem' : ''"
                    v-for="item in tabList" :key="item.value" @click="changeTab(item)">{{ item.text }}</div>

            </div>
            <div class="container_body" style="padding: 0px 0px;">

                <van-form required="auto" ref="formField">
                    <van-field v-model="formLine_.customerName" label="客户名称" placeholder="请输入客户名称" label-align="left"
                        input-align="right" :rules="[{ required: true, message: '请填写客户名称' }]">
                    </van-field>
                    <van-field v-model="formLine_.workAddress" label="工作地址" placeholder="请输入工作地址" label-align="left"
                        input-align="right" :rules="[{ required: true, message: '请填写工作地址' }]">
                    </van-field>
                    <van-field name="fileList" label="上传照片" label-align="left" input-align="right"
                        :rules="[{ required: formData.description?.trim() ? false : true, message: '请拍摄照片' }]">
                        <template #input>
                            <div class="right_align">
                                <van-uploader :after-read="handleAfterRead" :before-read="handleBeforeRead"
                                    v-model="formData.fileList" preview-size="89" :before-delete="deleteFile">
                                    <template #default>
                                        <div class="form_card_camera">
                                            <van-image width="28px" height="23px" :src="camera" />
                                        </div>
                                    </template>
                                </van-uploader>
                            </div>
                        </template>
                    </van-field>
                    <van-field v-model.trim="formData.description"
                        :rules="[{ required: formLine.images.length > 0 ? false : true, message: '请输入工作备注' }]"
                        label-align="left" input-align="right" autosize label="工作备注" type="textarea" rows="2"
                        maxlength="1000" show-word-limit placeholder="请输入工作备注" />


                </van-form>
            </div>

        </div>

        <div class="sure_btn">
            <van-button type="primary" color=" linear-gradient( 90deg, #73ACFF 0%, #237CFF 100%)" block
                @click="submit">提交方案</van-button>
        </div>
    </div>



</template>

<script setup lang="ts">

import { getDictInfoAPI } from "@/api/common";
import { visitSaveAPI, getVisitListAPI, getVisitCountAPI } from "@/api/customerVisit";
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showToast, showSuccessToast, showFailToast } from "vant";
import camera from "@/views/template/MyDay/assets/images/camera.png";
import { uploadFileHooks } from "@/hooks_modules/upload_hooks";
import { debounce } from "@/utils/debounce";
import { useUserInfo } from "@/stores/userInfo";
const userInfoStore = useUserInfo()

const router = useRouter();
let formField = ref();



const props = defineProps({
    componentType: {
        type: String,
        default: ''
    },
})

const { componentType } = toRefs(props)

const {
    handleAfterRead,
    handleBeforeRead,
    deleteFile,
    formLine,
    formData,
} = uploadFileHooks()

const formLine_ = reactive({
    workAddress: '',
    customerName: '',
})


const submit = debounce(() => {
    formField.value
        .validate()
        .then(() => {
            let params = {
                images: formLine.images.join(','),
                workNote: formData.description,
                workAddress: formLine_.workAddress,
                customerName: formLine_.customerName,
                workNoteTypeCode: curSelectType.value,
                workNoteType: curSelectType.text
            }
            visitSaveAPI(params).then((res: any) => {
                if (res.code == 200) {
                    showSuccessToast("提交成功");
                    setTimeout(() => {
                        router.push({
                            path: '/visitHistoryList',
                        });
                    }, 1500);
                } else {
                    showFailToast(res.msg)
                }
            })







        })
        .catch((err: any) => {
            showFailToast("请完善信息");
        });
}, 300, true)





const tabList = ref<{ text: string, value: string }[]>([])
const curSelectType = reactive<{ text: string, value: string }>({
    text: '',
    value: ''
})

function changeTab(item: any) {
    curSelectType.value = item.value
    curSelectType.text = item.text
}

const visitImageList = ref([])
const visitCount = reactive({
    month: 0,
    total: 0
})
function loadList() {
    let params = {
        pageNum: 1,
        pageSize: 3,
    }
    getVisitListAPI(params).then((res: any) => {
        if (res.code == 200) {
            visitImageList.value = res.data.records.reduce((prev: any, curr: any) => {
                prev.push(...curr.images.split(','))
                return prev
            }, [])

        }
    })

    getVisitCountAPI().then((res: any) => {
        if (res.code == 200) {
            visitCount.month = res.data.monthCount
            visitCount.total = res.data.totalCount

        }
    })

}

async function initDictInfo() {
    await getDictInfoAPI('customerVisit').then((res: any) => {
        if (res.code == 200) {
            tabList.value = res.data.map((item: any) => ({ text: item.dictName, value: item.dictCode }))
            curSelectType.value = tabList.value[0].value ?? ''
            curSelectType.text = tabList.value[0].text ?? ''
        }

    })
}


onMounted(async () => {
    await initDictInfo()
    loadList()
})

</script>


<style lang="scss" scoped>
.container_ {
    background-color: transparent;
    margin-bottom: 80px;
}

.card {
    background: #fff;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px;
    padding-bottom: 20px;
}



.form_card_camera {
    width: 89px;
    height: 89px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: rgba(103, 159, 255, 0.15);
}

.sure_btn {
    width: calc(100% - 18px - 18px);

    position: absolute;
    left: 50%;
    bottom: 100px;
    transform: translateX(-50%);
}


.container_body_header {
    display: grid;
    grid-template-columns: 60px auto 75px;
    align-items: center;

    .userName {
        font-weight: 700;
        font-size: 18px;
        color: #3D3D3D;
        line-height: 20px;
    }

    .primaryRoleName {
        font-weight: 400;
        font-size: 14px;
        color: #3D3D3D;
        margin-top: 8px;
    }
}

.container_body_box {
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 12px;

    &>div {

        width: 50%;
        max-width: 50%;
        background: linear-gradient(180deg, #E4F0FF 0%, #FFFFFF 100%);
        box-shadow: 0px 4px 5px 0px rgba(96, 173, 255, 0.15);
        border-radius: 10px;
        padding: 10px 10px 15px 12px;
        position: relative;

        .first {
            font-weight: 700;
            font-size: 14px;
            color: #227BFF;
            line-height: 16px;
        }

        .number {
            font-weight: 700;
            font-size: 24px;
            color: #217BFF;

        }

        .a_img {
            position: absolute;
            width: 80px;
            height: 82px;
            top: -16px;
            right: 0px;
            z-index: 0;
        }
    }
}

.history_list {
    margin-top: 24px;

    .history_list_header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 18px;

        .first {
            font-weight: 700;
            font-size: 14px;
            color: #3D3D3D;
        }
    }

    .img_list {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 5px;

        &>img {
            object-fit: cover;
            width: 100%;
            height: 100px;
            border-radius: 5px;
        }
    }
}
</style>
