<template>
    <div>
        <div class="card-header">
            <div class="selectOption-item" @click="calendarIsShow = true">
                <span>{{ time ? time : '请选择时间' }}</span>
                <van-icon name="play" class="play" color="#1A76FF" />
            </div>

        </div>
        <div class=' container_ mt-6'>
            <div class="container__header">
                <div>
                    <img src="@/assets/computed.png" alt="" srcset="">
                    <span class="ml-6">{{ route.query.gridName }}网格宣传检查</span>
                </div>
            </div>

            <div class="list-container">

                <div class="solution-card" v-for="(item, index) in tableData" :key="item.id">
                    <div class="company-name">
                        <span class="name van-multi-ellipsis--l2" v-getName="{ item: item }"> {{ item.staffName }}</span>
                        <div style="text-align: center;flex-shrink: 0;">
                            <div class="fs12 mb-3">{{ item.staffCode }}</div>
                            <span class="date">{{ item.createTime }}</span>
                        </div>

                    </div>
                    <div class="item-type">{{ item.gridProperty }}</div>
                    <div class="images">
                        <van-image fit="cover" v-for="el, i in item.images" lazy-load :src="el"
                            v-imgPreview="[item.images, i]" />
                    </div>

                    <div class="description">
                        <van-text-ellipsis rows="2" :content="item.description" expand-text="展开" collapse-text="收起"
                            position="middle" />
                    </div>
                </div>

                <van-empty description="暂无数据" class="mt-8" style="background-color: #fff;border-radius: 15px;"
                    v-if="tableData.length == 0" />
            </div>



        </div>

        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
            switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
            @confirm="onConfirm" />
    </div>
</template>

<script setup lang="ts">
import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { getPromoShotsListAPI, getManagementGridDetailAPI } from "@/api/promoShots";
import { useRoute, useRouter } from "vue-router";
import { formatDate } from "@/utils/loadTime";
const route = useRoute()
const router = useRouter()
interface List {
    id: string; //主键ID
    areaIdL2: string; // 二级区域 ID
    areaNameL2: string; // 二级区域名称
    createTime: string; // 创建时间，格式为 "YYYY-MM-DD HH:mm:ss.S"
    description: string; // 详细描述，如具体地址或位置
    gridId: string; // 网格 ID
    gridName: string; // 网格名称
    gridTypeName: string | null; // 网格类型名称，可能为 null
    images: string; // 相关图片的 URL
    status: number; //点检状态
    gridProperty: string;
    staffName: string;
    staffCode: string;
}

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    onConfirm,
} = searchHooks({
    source: 'inspectionLogMangementDetail',
    callback: loadStorePackageList
})

const areaIdL2 = computed(() => {
    return route.query.areaIdL2
})


const tableData = ref<List[]>([]);




const time = computed(() => {
    if (formline.startTime && formline.endTime) return `${formline.startTime} - ${formline.endTime}`
    else return ``
})

function loadStorePackageList() {
    let params = {
        startTime: formline.startTime ? `${formline.startTime} 00:00:00` : '',
        endTime: formline.endTime ? `${formline.endTime} 23:59:59` : '',
        areaIdL2: areaIdL2.value
    }
    getManagementGridDetailAPI(params).then((res: any) => {
        if (res.code == 200) {

            for (const item of res.data || []) {
                item.images = (item.images ? item.images.split(',') : []).map((el: string) => el)
                item.createTime = item.createTime ? formatDate(item.createTime, 'yyyy-MM-dd hh:mm') : ''
            }
            // 将合并后的数据转换回数组
            tableData.value = res.data.filter((item: any) => item.gridId == route.query.gridId)

        }
    })
}




onMounted(() => {
    defaultDate.value = [
        route.query.startTime ? new Date(String(route.query.startTime)) : new Date(),
        route.query.endTime ? new Date(String(route.query.endTime)) : new Date()
    ]
    formline.startTime = String(route.query.startTime ?? '')
    formline.endTime = String(route.query.endTime ?? '')

    loadStorePackageList()
})
</script>

<style lang="scss" scoped>
.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

    .selectOption-item {
        flex: 1 0 auto;
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;
        // justify-content: space-between;
        position: relative;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
            position: absolute;
            right: 10px;
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }
}








.solution-card {
    border-radius: 10px;
    background: #FFFFFF;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    padding: 11px 15px 15px;
    margin-bottom: 10px;

}

.company-name {
    font-weight: 700;
    font-size: 14px;
    color: #3D3D3D;
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.date {
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    flex: 0 0 auto;
}



.images {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 5px;
    margin-top: 14px;
    margin-bottom: 11px;

    &>.van-image {
        width: 100%;
        height: 100px;
        border-radius: 5px;
        overflow: hidden;
    }
}

.description {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 13px;
}

.item-type {
    display: inline-block;
    font-weight: 400;
    font-size: 12px;
    line-height: 13px;
    color: #217BFF;
    background: #EDF5FF;
    border-radius: 2px;
    padding: 3px;
    margin-top: 9px;
}













</style>