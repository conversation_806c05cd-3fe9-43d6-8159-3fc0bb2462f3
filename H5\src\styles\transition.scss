// .fade-enter-active,
// .fade-leave-active {
//   transition: opacity 0.28s;
// }

// .fade-enter,
// .fade-leave-active {
//   opacity: 0;
// }

/* fade-transform */



.fade-transform-leave-active {
  transition: all 0.3s ease;
}

.fade-transform-enter-active {
  transition: all 0.15s ease;
  transition-delay: 0.1s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-transform-enter-to {
  opacity: 1;
  transform: translateY(0px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

/* breadcrumb transition */
.breadcrumb-enter-active,
.breadcrumb-leave-active {
  transition: all 0.5s;
}

.breadcrumb-enter,
.breadcrumb-leave-active {
  opacity: 0;
  transform: translateX(20px);
}

.breadcrumb-move {
  transition: all 0.5s;
}

.breadcrumb-leave-active {
  position: absolute;
}


/* zoom-out */
.zoom-out-enter-active,
.zoom-out-leave-active {
  transition:
    opacity 0.1 ease-in-out,
    transform 0.15s ease-out;
}

.zoom-out-enter-from,
.zoom-out-leave-to {
  opacity: 0;
  transform: scale(0);
}

// zoom-fade
.zoom-fade-enter-active,
.zoom-fade-leave-active {
  transition:
    transform 0.2s,
    opacity 0.3s ease-out;
}

.zoom-fade-enter-from {
  opacity: 0;
  transform: scale(0.92);
}

.zoom-fade-leave-to {
  opacity: 0;
  transform: scale(1.06);
}


@keyframes identifier_ {

  0%,
  100% {
    transform: translateX(0px) rotate(-90deg);
    opacity: 1;
  }

  50% {
    transform: translateX(10px) rotate(-90deg);
    opacity: 0.1;
  }

}