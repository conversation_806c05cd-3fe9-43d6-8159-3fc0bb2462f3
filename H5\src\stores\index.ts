// https://pinia.vuejs.org/
import { createPinia } from "pinia";
// import { createPersistedState } from 'pinia-plugin-persistedstate';
// // 创建
// const pinia = createPinia().use(createPersistedState({
//     storage: localStorage
// }));

import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

// 导出
export default pinia;
