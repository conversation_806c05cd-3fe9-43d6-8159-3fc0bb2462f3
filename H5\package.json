{"name": "net-portal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"serve": "vite", "serve:local": "vite --mode localhost", "dev": "vite", "start": "vite --mode production", "build": "vite build  --mode production && node ./obfuscator.js", "build:test": "vite build --mode test && node ./obfuscator.js", "preview": "vite preview"}, "dependencies": {"@vitejs/plugin-basic-ssl": "1.1.0", "@vueuse/core": "10.7.2", "autoprefixer": "10.4.14", "axios": "1.6.2", "browser-image-compression": "^2.0.2", "cnjm-postcss-px-to-viewport": "1.0.0", "crypto-js": "4.2.0", "echarts": "5.4.3", "file-saver": "^2.0.5", "fs": "0.0.1-security", "javascript-obfuscator": "4.1.1", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "json-stable-stringify": "1.1.1", "lodash-es": "4.17.21", "modern-screenshot": "^4.5.4", "normalize.css": "8.0.1", "nprogress": "0.2.0", "pinia": "3.0.3", "pinia-plugin-persistedstate": "4.4.1", "process": "0.11.10", "terser": "^5.42.0", "vant": "4.9.4", "vconsole": "^3.15.1", "vite-plugin-commonjs": "0.10.1", "vite-plugin-compression": "0.5.1", "vite-plugin-require-transform": "1.0.21", "vue": "3.5.17", "vue-router": "4.5.1", "xlsx": "^0.18.5"}, "devDependencies": {"@types/crypto-js": "4.2.2", "@types/file-saver": "^2.0.7", "@types/js-cookie": "3.0.6", "@types/json-stable-stringify": "1.0.36", "@types/lodash-es": "^4.17.12", "@types/node": "20.10.4", "@types/nprogress": "0.2.3", "@vant/auto-import-resolver": "1.2.1", "@vitejs/plugin-vue": "4.5.0", "@vue/runtime-core": "3.5.17", "postcss-px-to-viewport": "1.1.1", "sass": "1.69.5", "typescript": "5.2.2", "unplugin-auto-import": "0.17.8", "unplugin-vue-components": "0.26.0", "vite": "5.0.0", "vite-plugin-style-import": "1.4.1", "vue-tsc": "1.8.22"}}