/* popper box */
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
  color: #303133;
  color: #fff;
}

.el-sub-menu {
  .el-menu--inline {
    background-color: #6f6edc !important;
  }
}

.el-menu--vertical .el-menu-item:hover {
  // background-color: #001528 !important;
  background-color: rgba(233, 244, 255, 0.3490196078) !important;
}

/* el-table */
.customTable.el-table {
  .el-table__header {
    thead tr th.el-table__cell {

      .cell,
      .cell>* {
        // text-transform: uppercase;
        vertical-align: middle;
        color: #2e7060;
        font-size: 14px;
        font-weight: 600;
        letter-spacing: 1.5px;
        word-wrap: break-word !important;
        word-break: normal;
      }

    }
  }

  .el-table__body-wrapper {
    .el-table__body {
      .el-table__row .cell {
        transition: background 0.3s ease;
        vertical-align: middle;
        padding: 4px 8px;
        font-size: 13px;
        color: #3f4254;
        font-family: proud, Helvetica, "sans-serif" !important;
      }
    }
  }

  .el-checkbox {
    .el-checkbox__inner {
      background-color: #ebedf3;
      border: 1px solid transparent;
      width: 16px;
      height: 16px;
      border-radius: 5px;
    }

    .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
      height: 4px;
    }
  }

  .el-checkbox.is-checked,
  .el-checkbox__input.is-indeterminate {
    .el-checkbox__inner {
      background-color: #0bb783;
    }

    .el-checkbox__inner::after {
      height: 9px;
      left: 4px;
      border-width: 2px;
      width: 4px;
    }
  }

  .el-checkbox.is-disabled {
    .el-checkbox__input.is-disabled {
      .el-checkbox__inner {
        background-color: rgba(255, 255, 255, 0.5);
      }
    }
  }

  .options {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    align-content: center;
    justify-content: center;

    i {
      display: grid;
      justify-items: center;
      align-items: center;
      width: 100%;
      height: 100%;
    }
  }

  .options i:hover {
    color: #0bb783;
    background-color: #c8d1da;
    border-color: transparent;
  }
}

.customPagination.el-pagination.is-background .el-pager li:not(.disabled).is-active {
  color: #ffffff !important;
  background-color: #0bb783 !important;
  border-color: #0bb783 !important;
  font-weight: 600;
}

.customPagination.el-pagination.is-background .el-pager li {
  color: #7e8299;
  background-color: #e4e6ef;
  border: 1px solid #d9d9d9;
  border-color: #e4e6ef;
  border-radius: 4px 4px 4px 4px;

  font-size: 14px;
  font-family: HelveticaNeue-, HelveticaNeue;
  font-weight: normal;
  color: rgba(0, 0, 0, 0.65);
}

.customPagination.el-pagination {

  .el-input__wrapper,
  .el-select__wrapper {
    background-color: #f3f6f9;
    border-color: #f3f6f9;
    color: #3f4254;
    border-radius: 7px;
  }

}

.el-table__empty-text {
  font-size: 18px;
  color: var(--el-border-color-lighter);
}

.customPagination.el-pagination.is-background .btn-prev:disabled,
.customPagination.el-pagination.is-background .btn-next:disabled {
  background-color: rgb(255 255 255 / 30%);
}

.customPagination.el-pagination__sizes .el-input__wrapper,
.customPagination.el-pagination__jump .el-input__wrapper {
  background-color: #e4e6ef;
}

/* dialog */


.el-overlay-message-box {
  width: 100%;
  -webkit-backdrop-filter: blur(20px) saturate(100%);
  backdrop-filter: blur(20px) saturate(100%);

  .el-message-box {
    border: none;
    width: 750px;
    border-radius: 28px;
    padding: 20px;
    background: #cbcbd51a;
    box-shadow: inset 0 0 0 0.5px hsl(0deg 0% 100% / 30%);
    -webkit-backdrop-filter: blur(40px);
    backdrop-filter: blur(40px);
    color: #fff;
    box-shadow: rgba(0, 0, 0, 0.25) 0px 54px 55px,
      rgba(0, 0, 0, 0.12) 0px -12px 30px, rgba(0, 0, 0, 0.12) 0px 4px 6px,
      rgba(0, 0, 0, 0.17) 0px 12px 13px, rgba(0, 0, 0, 0.09) 0px -3px 5px;
  }

  .el-message-box__content,
  .el-message-box__title {
    color: #fff;
    word-wrap: break-word;
    white-space: pre-line;
  }

  .el-message-box__btns {
    width: 100%;

    .el-button {
      background: transparent;
      color: #fff;
      border-radius: 20px;
      border: 1px solid #ccc;
    }

    .el-button--primary {
      background: #737ebc;
      border: 1px solid #737ebc;
    }
  }

  .el-message-box__close {
    font-size: 18px;
    color: #fff;
  }

  .el-message-box__headerbtn:focus .el-message-box__close,
  .el-message-box__headerbtn:hover .el-message-box__close {
    font-size: 18px;
    color: #fff;
    font-weight: bolder;
  }
}

.stepsDialog.el-dialog {
  z-index: 99999;
  padding: 32px;
  border-radius: 30px;

  box-shadow: rgb(0 0 0 / 10%) 0px 0px 0px 0.5px inset;
  backdrop-filter: blur(40px);
  background: rgba(255, 255, 255, 0.6);
}

.stepsDialog .dialog-footer {
  display: inline-flex;
}

.el-overlay {
  background-color: rgba(0, 0, 0, 0.4);
}

.stepsDialog {
  .my-header {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 60%;
    min-width: 720px;
    margin: 0px auto;

    &>div {
      padding: 10px 0px;
      flex: 1 0 30.33333%;
      display: flex;
      align-items: center;

      .wizard-icon {
        transition: color 0.15s ease, background-color 0.15s ease,
          border-color 0.15s ease, box-shadow 0.15s ease,
          -webkit-box-shadow 0.15s ease;
        background-color: rgb(243, 246, 249);
        display: grid;
        justify-items: center;
        align-items: center;
        min-width: 46px;
        height: 46px;
        border-radius: 12px;
        color: rgb(63, 66, 84);
        font-size: 20px;
        font-weight: 600;
        flex: 0 0;
      }

      .wizard-icon.active {
        color: #1bc5bd;
        background-color: #c9f7f5;
      }

      .wizard-check {
        height: 46px;
        flex: 1 0 auto;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: flex-start;
        padding: 0px 15px;

        .wizard-title {
          color: #181c32;
          font-weight: 600;
          font-size: 18px;
        }

        .wizard-desc {
          color: #727278;
          font-size: 15px;
        }
      }
    }

    &>div:nth-of-type(1) {
      flex: 1 0 37.3333%;
    }
  }

  .AddressDetails {
    display: flex;
    flex-direction: column;

    svg:hover {
      color: rgb(29, 0, 249);
      cursor: pointer;
    }

    img {
      margin: 25px auto;
    }
  }
}

.curtomDialog.el-dialog {
  padding: 0px;
  border-radius: 16px;
  overflow: hidden;
  .el-dialog__header {
    background-color: #4D8AC9;

    .el-dialog__title,
    .el-icon.el-dialog__close {
      color: #fff;
    }

    .el-icon.el-dialog__close {
      font-size: 20px;
    }
  }

  .el-dialog__header.show-close {
    padding: var(--el-dialog-padding-primary);
  }

  .el-dialog__body,
  .el-dialog__footer {
    padding: var(--el-dialog-padding-primary);
  }
}

.demo-form-inline {

  .el-input__wrapper,
  .el-textarea__inner,.el-select__wrapper {
    background-color: #f3f6f9;
    border-color: #f3f6f9;
    color: #3f4254;
    border-radius: 7px;
  }


  .el-progress-circle__track {
    stroke: #d1f8f6 !important;
  }

  .el-form--inline .el-form-item {
    align-items: center;
  }
}