<template>
    <transition name="el-collapse-transition" mode="out-in" v-on="on">
        <slot />
    </transition>
</template>
<script setup>
const on = {
    beforeEnter(el) {
        if (!el.dataset) el.dataset = {}
        el.dataset.oldPaddingTop = el.style.paddingTop
        el.dataset.oldPaddingBottom = el.style.paddingBottom

        el.style.maxHeight = 0
        el.style.paddingTop = 0
        el.style.paddingBottom = 0
    },

    enter(el) {
        el.dataset.oldOverflow = el.style.overflow
        el.style.overflow = 'hidden'

        el.style.maxHeight = `${el.scrollHeight}px`
        el.style.paddingTop = el.dataset.oldPaddingTop
        el.style.paddingBottom = el.dataset.oldPaddingBottom
    },

    afterEnter(el) {
        el.style.maxHeight = '' // 需要还原。
        el.style.overflow = el.dataset.oldOverflow
    },

    beforeLeave(el) {
        if (!el.dataset) el.dataset = {}
        el.dataset.oldPaddingTop = el.style.paddingTop
        el.dataset.oldPaddingBottom = el.style.paddingBottom
        el.dataset.oldOverflow = el.style.overflow

        el.style.overflow = 'hidden'
        el.style.maxHeight = `${el.scrollHeight}px`
    },

    leave(el) {
        if (el.scrollHeight !== 0) {
            el.style.maxHeight = 0
            el.style.paddingTop = 0
            el.style.paddingBottom = 0
        }
    },

    afterLeave(el) {
        el.style.maxHeight = ''
        el.style.overflow = el.dataset.oldOverflow
        el.style.paddingTop = el.dataset.oldPaddingTop
        el.style.paddingBottom = el.dataset.oldPaddingBottom
    },
}

</script>

<style lang="scss" scoped>
.el-collapse-transition-leave-active,
.el-collapse-transition-enter-active {
    transition: 0.3s max-height ease-in-out,
        0.3s padding-top ease-in-out,
        0.3s padding-bottom ease-in-out;
}
</style>