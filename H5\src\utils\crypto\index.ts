import CryptoJS from 'crypto-js'

import { randomString } from "./foundation";
import jsonStableStringify from 'json-stable-stringify'

// 加密密钥和应用程序ID
const ckey = 'okou74hs53z69hj7jy7wim5gqinzqx7i';//密钥
const appId = 'njdx_task_hub_2024'

/**
 * 参数加密方法 - 用于API请求签名
 * 将请求参数进行加密处理，生成时间戳、随机数和签名
 * @param params 需要加密的参数对象
 * @returns 包含时间戳、随机数、应用ID和签名的对象
 */
export const CryptoParams = (params: Object): Object => {
  // 生成6位随机字符串作为nonce
  let nonce = randomString(6);
  // 获取当前时间戳
  let timeStamp = Date.parse(String(new Date())).toString()
  // 构建基础对象
  let Obj = { timeStamp, nonce, appId }
  // 将基础对象合并到参数中
  Object.assign(params, Obj)
  // 对参数进行稳定排序并转换为JSON字符串
  let queryObj = jsonStableStringify(queryNodes(params))
  // 使用HMAC-SHA256算法生成签名
  const Hmac = CryptoJS.HmacSHA256(queryObj.toString(), ckey)
  // 将签名转换为Base64格式
  const sign = CryptoJS.enc.Base64.stringify(Hmac)
  // 返回包含所有信息的对象
  let AES = {
    ...Obj,
    sign
  }
  // console.log(AES, 'aes==============')
  return AES
}

/**
 * 清理参数对象中的空值
 * 递归遍历对象，删除null、undefined和'null'字符串
 * @param obj 需要清理的对象
 * @returns 清理后的对象
 */
const queryNodes = (obj: any) => {
  Object.keys(obj).forEach((key) => {
    let value = obj[key];
    // 如果值是对象，递归处理
    value && typeof value === 'object' && queryNodes(value);
    // 删除空值
    (value === 'null' || value === null || value === undefined) && delete obj[key];
  });
  return obj;
};

// /**
//  * AES数据加密 ：字符串 key iv  返回base64
//  */
// export function Encrypt(params, AES_KEY) {
//   const key = CryptoJS.enc.Utf8.parse(AES_KEY)
//   const srcs = CryptoJS.enc.Utf8.parse(params)
//   const encrypted = CryptoJS.AES.encrypt(srcs, key, {
//     mode: CryptoJS.mode.ECB,
//     padding: CryptoJS.pad.Pkcs7
//   })
//   return encrypted.toString()
// }

/**
 * AES加密方法
 * 使用ECB模式和PKCS7填充对数据进行AES加密
 * @param data 需要加密的字符串
 * @param aes_key AES密钥，默认为'RHjKzzL7n64eVR9V'
 * @returns Base64编码的加密结果
 */
export function Encrypt(data: string, aes_key = 'RHjKzzL7n64eVR9V') {
  // 将密钥转换为WordArray格式
  let key = CryptoJS.enc.Utf8.parse(aes_key);
  // 使用AES加密，ECB模式，PKCS7填充
  let encrypted = CryptoJS.AES.encrypt(data, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  });
  // 将加密结果转换为Base64格式
  return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Hex.parse(encrypted.ciphertext.toString()));
}

// /**
//  1. AES数据解密 ：字符串 key iv  返回base64
//  2.  3. @return {string}
//  */
// export function Decrypt(params, AES_KEY) {
//   var key = CryptoJS.enc.Utf8.parse(AES_KEY)
//   var decrypt = CryptoJS.AES.decrypt(params, key, {
//     mode: CryptoJS.mode.ECB,
//     padding: CryptoJS.pad.Pkcs7
//   })
//   return CryptoJS.enc.Utf8.stringify(decrypt).toString()
// }

/**
 * AES解密方法
 * 使用ECB模式和PKCS7填充对数据进行AES解密
 * @param word 需要解密的Base64字符串
 * @param aes_key AES密钥，默认为'RHjKzzL7n64eVR9V'
 * @returns 解密后的原始字符串
 */
export function Decrypt(word: string, aes_key = 'RHjKzzL7n64eVR9V') {
  // 将密钥转换为WordArray格式
  let key = CryptoJS.enc.Utf8.parse(aes_key);
  // 使用AES解密，ECB模式，PKCS7填充
  let decrypt = CryptoJS.AES.decrypt(word, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });
  // 将解密结果转换为UTF8字符串
  let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
  return decryptedStr;
}

/**
 * HKDF密钥派生函数实现 (基于RFC 5869标准)
 * HKDF是一种密钥派生函数，用于从主密钥生成多个子密钥
 * @param ikmBase64 Base64编码的输入密钥材料
 * @param saltBase64 Base64编码的盐值
 * @param info 信息字符串，用于区分不同的派生密钥
 * @param length 输出密钥长度（字节）
 * @returns Base64编码的派生密钥
 */
export function hkdfDerive(ikmBase64: string, saltBase64: string, info: string, length: number): string {
  // 解码Base64输入
  const ikmWordArray = base64ToWordArray(ikmBase64);
  const saltWordArray = base64ToWordArray(saltBase64);
  const infoWordArray = CryptoJS.enc.Utf8.parse(info);

  // 步骤1: 提取阶段 - 使用HMAC-SHA256从输入密钥材料中提取伪随机密钥
  const prk = hmacSha256(saltWordArray, ikmWordArray);

  // 步骤2: 扩展阶段 - 从伪随机密钥扩展出指定长度的输出密钥
  const okm = expand(prk, infoWordArray, length);

  // 转换为Base64格式返回
  return okm.toString(CryptoJS.enc.Base64);
}

/**
 * HMAC-SHA256哈希函数
 * 使用指定的密钥对数据进行HMAC-SHA256哈希计算
 * @param key 密钥
 * @param data 需要哈希的数据
 * @returns HMAC-SHA256哈希结果
 */
function hmacSha256(key: CryptoJS.lib.WordArray, data: CryptoJS.lib.WordArray): CryptoJS.lib.WordArray {
  return CryptoJS.HmacSHA256(data, key);
}

/**
 * HKDF扩展函数
 * 从伪随机密钥(PRK)扩展出指定长度的输出密钥材料(OKM)
 * @param prk 伪随机密钥
 * @param info 信息字符串
 * @param length 需要的输出长度（字节）
 * @returns 扩展后的密钥材料
 */
function expand(
  prk: CryptoJS.lib.WordArray, 
  info: CryptoJS.lib.WordArray, 
  length: number
): CryptoJS.lib.WordArray {
  // 计算需要的块数（SHA-256输出长度为32字节）
  const blocksNeeded = Math.ceil(length / 32);
  let t = CryptoJS.lib.WordArray.create();
  let okm = CryptoJS.lib.WordArray.create();

  // 循环生成每个块
  for (let i = 1; i <= blocksNeeded; i++) {
    // 第一个块使用空字符串，后续块使用前一个块的结果
    let prev = (i === 1) ? CryptoJS.lib.WordArray.create() : t;
    let input = CryptoJS.lib.WordArray.create();
    // 拼接：前一个块 + 信息 + 计数器
    input.concat(prev);
    input.concat(info);
    input.concat(CryptoJS.enc.Utf8.parse(String.fromCharCode(i)));

    // 使用HMAC-SHA256计算当前块
    t = hmacSha256(prk, input);
    okm.concat(t);
  }

  // 手动截断到指定的字节长度
  return truncateWordArray(okm, length);
}

/**
 * Base64字符串转换为WordArray
 * @param base64 Base64编码的字符串
 * @returns WordArray对象
 */
function base64ToWordArray(base64: string): CryptoJS.lib.WordArray {
  return CryptoJS.enc.Base64.parse(base64);
}

/**
 * 手动截断WordArray到指定的字节长度
 * @param wordArray 需要截断的WordArray
 * @param byteLength 目标字节长度
 * @returns 截断后的WordArray
 */
function truncateWordArray(wordArray: CryptoJS.lib.WordArray, byteLength: number): CryptoJS.lib.WordArray {
  // 计算需要的字数（每个字4字节）
  const wordCount = Math.ceil(byteLength / 4);
  const words = wordArray.words.slice(0, wordCount);
  // 计算部分字节数
  const partialBytes = byteLength % 4;
  // 如果有部分字节，需要掩码处理最后一个字
  if (partialBytes > 0 && words.length > 0) {
    const mask = 0xFFFFFFFF >>> (32 - partialBytes * 8);
    words[words.length - 1] &= mask;
  }

  return CryptoJS.lib.WordArray.create(words, byteLength);
}

/**
 * 使用HKDF派生密钥进行解密
 * 结合HKDF密钥派生和AES解密，提供更安全的解密方式
 * @param word 需要解密的字符串
 * @param saltBase64 Base64编码的盐值
 * @returns Promise<string> 解密后的字符串
 */
export async function DecryptWithHKDF(word: string,saltBase64:string): Promise<string> {
  return new Promise<string>(async (resolve, reject) => {
    try {
      const info = "hkdf"; // 信息字符串，用于密钥派生
      const length = 16; // 16字节 = 128位密钥长度
      // 使用固定的主密钥和提供的盐值派生AES密钥
      const aes_key = hkdfDerive('lxlnqe2spl6zelhjdzjv85f5ml18uf8xklk6', saltBase64, info, length);
      // 将派生密钥转换为WordArray格式
      const key = CryptoJS.enc.Utf8.parse(aes_key);

      // 使用派生的密钥进行AES解密
      const decrypt = CryptoJS.AES.decrypt(word, key, { 
        mode: CryptoJS.mode.ECB, 
        padding: CryptoJS.pad.Pkcs7 
      });

      // 将解密结果转换为UTF8字符串
      const decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
      resolve(decryptedStr);
    } catch (error) {
      reject(error);
    }
  });
}





