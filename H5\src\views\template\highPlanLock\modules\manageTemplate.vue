<template>
    <div class='highPlanLock container_ mt-16'>

        <div class="container__header">
            <div style="align-items: flex-start;">
                <img src="@/views/template/processContro/assets/processContro.svg" alt="" srcset="">
                <span class="ml-6">
                    <div>高套封闭</div>

                </span>

            </div>
            <div style="display: flex;align-items: center;justify-content: space-between;">
                <van-popover :actions="actionsList" @select="changeSelete" placement="bottom-end">
                    <template #reference>
                        <div class="selectOption-item">
                            <span>{{ curSelectTime.text }}</span>
                            <van-icon name="play" class="play" color="#1A76FF" />
                        </div>
                    </template>
                </van-popover>
            </div>
        </div>

        <div class="container_body">
            <div class="stats-grid">
                <div class="stats-row" @click="viewDetails('新装')">
                    <div class="stats-item">
                        <div class="stats-value">{{ statsData.newEquipment }}户</div>
                        <div class="stats-label">新装高套</div>
                    </div>
                    <div class="stats-item">
                        <div class="stats-value">{{ statsData.newEquipmentFold }}户</div>
                        <div class="stats-label">新装高套（折算）</div>
                    </div>
                    <div class="stats-item">
                        <div class="stats-value">{{ statsData.completionRate }}%</div>
                        <div class="stats-label">完成率</div>
                    </div>
                </div>
                <div class="stats-row" @click="viewDetails('存量')">
                    <div class="stats-item">
                        <div class="stats-value">{{ statsData.stockEquipment }}户</div>
                        <div class="stats-label">存量高套</div>
                    </div>
                    <div class="stats-item">
                        <div class="stats-value">{{ statsData.stockEquipmentFold }}户</div>
                        <div class="stats-label">存量高套（折算）</div>
                    </div>
                    <div class="stats-item">
                        <div class="stats-value">{{ statsData.completionRateStock }}%</div>
                        <div class="stats-label">完成率</div>
                    </div>
                </div>
                <div class="stats-row" @click="viewDetails('拆降')">
                    <div class="stats-item">
                        <div class="stats-value">{{ statsData.dismantleEquipment }}户</div>
                        <div class="stats-label">拆机高套</div>
                    </div>
                    <div class="stats-item">
                        <div class="stats-value">{{ statsData.removeEquipment }}户</div>
                        <div class="stats-label">降档高套</div>
                    </div>
                    <div class="stats-item">
                        <div class="stats-value">{{ statsData.highEquipmentPriority }}户</div>
                        <div class="stats-label">高套停机</div>
                    </div>
                </div>

                <div class=" tips">
                    <van-icon name="info-o" color="#6A98FB" />
                    数据统计日期为：{{ (statsData.day || '').replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3') }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang='ts'>
import { useRouter } from "vue-router";
import { selectTime } from "@/hooks_modules/selectTime";
import { getHighValueAllCountAPI, type HighValueStatsResponse } from "@/api/highPlanLock";
import { formatDate } from "@/utils/loadTime";
const router = useRouter();

const props = defineProps({
    manageType: {
        type: String,
        default: ''
    }
})
const { manageType } = toRefs(props)

const { formline,
    defaultDate,
    maxDate,
    minDate,
    calendarIsShow,
    actionsList,
    curSelectTime,
    timeSection,
    changeSelete,
    onConfirm,
} = selectTime({
    source: 'highPlanLockArea',
    callback: () => {
        loadHighValueCount()
    }
})



// 统计数据
const statsData = reactive({
    newEquipment: 0, // 新装高套
    newEquipmentFold: 0, // 新装高套（折算）
    completionRate: '0', // 新装高套完成率
    stockEquipment: 0, // 存量高套
    stockEquipmentFold: 0, // 存量高套（折算）
    completionRateStock: '0', // 存量高套完成率
    dismantleEquipment: 0, // 高套拆机
    removeEquipment: 0, // 高套降档
    highEquipmentPriority: 0, // 高套停机
    day: ''
});

function loadHighValueCount() {
    getHighValueAllCountAPI({ type: curSelectTime.value }).then((res: any) => {
        if (res.code == 200) {
            console.log(res, 'res');

            const data: HighValueStatsResponse = res.data ?? {};
            statsData.newEquipment = data.xzgt ?? 0;
            statsData.newEquipmentFold = data.xzgtzk ?? 0;
            statsData.completionRate = ((data.xzgtRate ?? 0) * 100).toFixed(2);
            statsData.stockEquipment = data.clgt ?? 0;
            statsData.stockEquipmentFold = data.clgtzk ?? 0;
            statsData.completionRateStock = ((data.clgtRate ?? 0) * 100).toFixed(2);
            statsData.dismantleEquipment = data.gtcj ?? 0;
            statsData.removeEquipment = data.gtjd ?? 0;
            statsData.highEquipmentPriority = data.gttj ?? 0;
            statsData.day = data.day || data.monthEnd;
        }

    });
}


const viewDetails = (type: string) => {
    router.push({
        path: '/highPlanLockDetail',
        query: {
            type: type,
            time: curSelectTime.text,
            timeType: curSelectTime.value,
            manageType: props.manageType,
            timesSection: statsData.day.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3')
        }
    })
}


onMounted(() => {
    actionsList.value[0].text = '日报'
    curSelectTime.text = '日报'
    loadHighValueCount()
})
</script>

<style lang="scss" scoped>
.selectOption-item {
    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
    font-weight: 400;
    font-size: 14px;
    color: #1A76FF;
    padding: 8px;
    display: flex;
    align-items: center;

    .play {
        transform: rotate(90deg);
        margin-left: 3px;
    }
}

.selectOption-item:active {
    background: #d8dee6;
}

.more {
    font-size: 14px;
    color: #3D3D3D;
    font-weight: 500;
}

.container_body {
    .stats-grid {
        background-color: #EDF5FF;
        padding: 20px 0px;
        border-radius: 15px;
        position: relative;
        // border: 2px solid #628FF8;

        .stats-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;

            &:last-child {
                margin-bottom: 0;
            }

            .stats-item {
                flex: 1 0 33.33333%;
                max-width: 33.33333%;
                text-align: center;

                .stats-value {
                    font-size: 18px;
                    font-weight: 600;
                    color: #1A76FF;
                    margin-bottom: 4px;
                }

                .stats-label {
                    font-size: 12px;
                    color: #666666;
                }
            }
        }

        .tips {
            position: absolute;
            left: 20px;
            bottom: 15px;
            font-size: 10px;
            color: #1A76FF;
        }
    }
}
</style>