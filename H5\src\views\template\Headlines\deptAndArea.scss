.myEcharts {
    margin-top: 10px;
    width: 100%;
    height: 150px;
}

.container_body {
    position: relative;

    .tabs {
        width: 40%;

        position: absolute;
        top: 48%;
        left: 50%;
        transform: translateY(-50%);

        .tab-item {
            margin-top: 10px;
            display: grid;
            grid-template-columns: 10px 1fr auto;
            align-items: center;

            .circle {
                width: 7px;
                height: 7px;
                border-radius: 50%;
            }

            .name {
                font-family: Source <PERSON>, Source <PERSON>;
                font-weight: 400;
                font-size: 14px;
                color: #3D3D3D;
            }

            .value {
                font-family: Source <PERSON>, Source <PERSON>;
                font-weight: 500;
                font-size: 16px;
                color: #3D3D3D;
            }
        }
    }
}

.popover {
    position: absolute;
    right: 20px;
    top: 10px
}

.selectOption-item {
    background: #EDF5FF;
    border-radius: 5px 5px 5px 5px;
    font-weight: 400;
    font-size: 14px;
    color: #1A76FF;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;


}

.play {
    transform: rotate(90deg);
    margin-left: 3px;
}

.selectOption-item:active {
    background: #d8dee6;
}

.timeSection {
    font-size: 13px;
    border-radius: 5px;
    padding: 6px;
    color: #1A76FF;
    border: 1PX solid #1A76FF;
}