import { ref, reactive, onMounted, onUnmounted, computed, toRefs } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { getHeadlinesListAPI, getHeadlinesListGovAPI } from "@/api/home";


interface GoalCompletion {
    month: number; // 月份 (格式: YYYYMM)，默认值: 202409
    id: number; // 唯一标识符，可选
    points: number; // 积分目标，默认值: 0
    rongHe: number; // 融合目标，默认值: 0
    xinliangZgz: number; // 新装高套目标，默认值: 0
    completeDayPoints: number; // 当日完成积分，默认值: 0
    completeDayRongHe: number; // 当日完成融合，默认值: 0
    completeDayXinliangZgz: number; // 当日完成新装高套，默认值: 0
    completeMonthPoints: number; // 当月完成积分，默认值: 0
    completeMonthRongHe: number; // 当月完成融合，默认值: 0
    completeMonthXinliangZgz: number; // 当月完成新装高套，默认值: 0
    dailyPointsDenominator: number;  // 每日积分目标分母
    dailyRongHeDenominator: number;  // 每日融合目标分母
    dailyXinliangZgzDenominator: number;  //  每日新装高套目标分母


    // 政企相关目标和完成情况
    gov5g: number; // 政企5G业务完成量
    gov5gGoal: number; // 政企5G业务目标量
    // govCloudComputer: number; // 政企云电脑业务完成量
    // govCloudComputerGoal: number; // 政企云电脑业务目标量
    govFttrb: number; // 政企FTTR-B业务完成量
    govFttrbGoal: number; // 政企FTTR-B业务目标量
    govHuZhuan: number; // 政企互转业务完成量
    govHuZhuanGoal: number; // 政企互转业务目标量
    govShangZhuan: number; // 政企上转业务完成量
    govShangZhuanGoal: number; // 政企上转业务目标量
    govTianyi: number; // 政企天翼业务完成量
    govTianyiGoal: number; // 政企天翼业务目标量
    govTianyiShilian: number; // 政企天翼试炼业务完成量
    govTianyiShilianGoal: number; // 政企天翼试炼业务目标量
    govXiaowei: number; // 政企小微业务完成量
    govXiaoweiGoal: number; // 政企小微业务目标量

    [key: string]: any; // 动态键值，允许扩展其他属性
}
type ParamsData = {
    staffType: string,
    isGov: boolean
}


export const HeadlinesHooks = function (params: Partial<ParamsData> = {}) {
    const { staffType, isGov } = params


    const dataList = reactive<GoalCompletion>({
        month: 202409, // 月份
        id: 0, // 唯一标识符
        points: 0, // 积分目标
        rongHe: 0, // 融合目标
        xinliangZgz: 0, // 新装高套目标
        completeDayPoints: 0, // 当日完成积分
        completeDayRongHe: 0, // 当日完成融合
        completeDayXinliangZgz: 0, // 当日完成新装高套
        completeMonthPoints: 0, // 当月完成积分
        completeMonthRongHe: 0, // 当月完成融合
        completeMonthXinliangZgz: 0, // 当月完成新装高套
        dailyPointsDenominator: 0,  // 每日积分目标分母
        dailyRongHeDenominator: 0,  // 每日融合目标分母
        dailyXinliangZgzDenominator: 0,//  每日新装高套目标分母


        gov5g: 0,
        gov5gGoal: 0,
        // govCloudComputer: 0,
        // govCloudComputerGoal: 0,
        govFttrb: 0,
        govFttrbGoal: 0,
        govHuZhuan: 0,
        govHuZhuanGoal: 0,
        govShangZhuan: 0,
        govShangZhuanGoal: 0,
        govTianyi: 0,
        govTianyiGoal: 0,
        govTianyiShilian: 0,
        govTianyiShilianGoal: 0,
        govXiaowei: 0,
        govXiaoweiGoal: 0,
        govQudaoJifen: 0,
        govQudaoJifenGoal: 0,

    })

    type ResponseItem = {
        status: string;
        value: {
            code: string | number;
            data: Record<string, any>;
        };
    };
    type MergedData = Record<string, any>;


    // 当前月的天数
    const CurdaysInMonth = ref(0)

    // 加载头条列表数据
    function loadHeadlinesList() {
        Promise.allSettled(isGov ? [getHeadlinesListAPI(), getHeadlinesListGovAPI()] : [getHeadlinesListAPI()]).then((res: any) => {
            
            let list = mergeFulfilledData(res)
            // 将 API 返回的数据更新到 dataList 中
            for (const key in dataList) {
                if (Object.prototype.hasOwnProperty.call(dataList, key)) {
                    dataList[key] = list[key] ?? 0;
                }
            }
            // 获取当前月的天数
            const CurdaysInMonth = getCurdaysInMonth()
            // 计算每日积分、融合、新装高套的目标分母
            dataList.dailyPointsDenominator = Math.ceil((dataList.points / CurdaysInMonth))
            dataList.dailyRongHeDenominator = Math.ceil((dataList.rongHe / CurdaysInMonth))
            dataList.dailyXinliangZgzDenominator = Math.ceil((dataList.xinliangZgz / CurdaysInMonth))

        })


    }

    function mergeFulfilledData(responses: ResponseItem[]): MergedData {
        return responses
            .filter(item => item.status === "fulfilled" && item.value.code === "200")
            .reduce((acc, item) => {
                return { ...acc, ...item.value.data };
            }, {});
    }

    // 获取当前月的天数
    function getCurdaysInMonth(): number {
        const currentDate = new Date();
        // 获取当前年份和月份
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth() + 1; // getMonth() 返回的月份是从 0 开始的，因此要加 1
        // 创建一个当前月的下个月的日期对象，并将日期设置为 0，这样就可以获取到当前月的最后一天
        const daysInMonth = new Date(year, month, 0).getDate();
        return daysInMonth
    }

    // 生命周期钩子：组件挂载时执行
    onMounted(() => {
        loadHeadlinesList()
        CurdaysInMonth.value = getCurdaysInMonth()
    });

    // 生命周期钩子：组件卸载时执行
    onUnmounted(() => {
        // console.log('Component unmounted');
    });

    // 返回钩子函数提供的方法和数据
    return {
        getCurdaysInMonth,
        loadHeadlinesList,
        CurdaysInMonth,
        dataList
    }

}


