<template>
    <div>
        <div class="card-header">

            <div class="selectOption">
                <van-popover :actions="actions" @select="(params: any) => params.handle()" placement="bottom">
                    <template #reference>
                        <div class="selectOption-item">
                            <span>{{ curTimeType == '当月' ? `${startTime}~ ${endTime}` : endTime }}</span>
                            <van-icon name="play" class="play" color="#1A76FF" />
                        </div>
                    </template>
                </van-popover>
            </div>


            <cusTabs :tabList="tabList" class="ml-10" v-model:curSelectTab="tabValue"
                @changeSelectTab="changeSelectTab">
            </cusTabs>
        </div>
        <div class=' container_ mt-6'>
            <div class="container__header">
                <div>
                    <img src="@/assets/computed.png" alt="" srcset="">
                    <span class="ml-6">


                        <van-popover placement="bottom">
                            <div class="popoverBody">
                                <li>展示T-2数据</li>
                            </div>
                            <template #reference>
                                <span>政企存量八件事关键动作完成情况通报</span>
                                <van-icon class="ml-3" name="question" color="#1A76FF" />
                            </template>
                        </van-popover>
                    </span>
                </div>

            </div>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th rowspan="2" class="sticky">分局名称</th>
                            <template v-if="tabValue == 3">
                                <th colspan="1">离网积分</th>
                                <th colspan="1">降档积分</th>
                                <th colspan="1">提值积分</th>
                            </template>
                            <template v-else-if="tabValue == 1">
                                <th>天翼拆机压降</th>
                                <th>V网价值评估</th>
                                <th>天翼到期续约</th>
                                <th>互专拆机压降</th>
                                <th>互专降档压降</th>
                            </template>
                            <template v-else>
                                <th>加装高端组网</th>
                                <th>加装权益</th>
                                <th>单天翼进融</th>
                            </template>
                        </tr>
                        <tr>
                            <template v-if="tabValue == 3">
                                <th>使用率</th>
                                <th>使用率</th>
                                <th>完成率</th>
                            </template>
                            <template v-else-if="tabValue == 1">
                                <th>使用率</th>
                                <th>完成率</th>
                                <th>完成率</th>
                                <th>使用率</th>
                                <th>使用率</th>
                            </template>
                            <template v-else>
                                <th>完成率</th>
                                <th>完成率</th>
                                <th>完成率</th>
                            </template>
                        </tr>

                    </thead>
                    <tbody>

                        <tr v-for="(row, index) in tableData" :key="index">
                            <td class="sticky">{{ row.areaNameL2 }}</td>
                            <template v-if="tabValue == 3">
                                <td>{{ formatterLsjz(row, 'Lsjz', 10000) }}%</td>
                                <td>{{ formatterLsjz(row, 'Jdjz', 10000) }}%</td>
                                <td>{{ formatterLsjz(row, 'Tsjz', 10000) }}%</td>
                            </template>
                            <template v-else-if="tabValue == 1">
                                <td>{{ formatterLsjz(row, 'Tycj') }}%</td>
                                <td>{{ formatterLsjz(row, 'VJzpg') }}%</td>
                                <td> {{ formatterLsjz(row, 'Tyxy') }}%</td>
                                <td>{{ formatterLsjz(row, 'Sxcj') }}%</td>
                                <td>{{ formatterLsjz(row, 'Sxjd') }}%</td>
                            </template>
                            <template v-else>
                                <td>{{ formatterLsjz(row, 'Gdzw') }}%</td>
                                <td>{{ formatterLsjz(row, 'Qy') }}%</td>
                                <td>{{ formatterLsjz(row, 'D2c') }}%</td>
                            </template>
                        </tr>

                    </tbody>
                </table>
            </div>
            <van-empty description="暂无数据" class="mt-8" style="background-color: #fff;border-radius: 15px;"
                v-if="tableData.length == 0" />
        </div>


        <van-popup v-model:show="monthPopup" round position="bottom">
            <van-date-picker v-model="currentDate" title="选择年月" :min-date="monthMinDate" :max-date="monthMaxDate"
                :formatter="formatter" :columns-type="columnsType" @confirm="confirmMonth" />

        </van-popup>
    </div>
</template>
<script setup lang="ts">
import { DatePickerColumnType } from "vant";
import cusTabs from "@/components/cusTabs/index.vue";
import { searchHooks } from "@/hooks_modules/changeTimeHooks";
import { getClyyReportL2ListAPI } from "@/api/home";


interface List {
    areaNameL2?: string; // 区域
    aimLsjz: number;    // 离网积分目标使用
    aimJdjz: number;    // 降档积分目标使用
    cntTsjz: number;    // 提值积分完成率
    aimTycj: number;    // 天翼拆机压降
    cntVJzpg: number;   // V网价值评估
    cntTyxy: number;    // 天翼到期续约
    cntSxcj: number;    // 互传拆机压降
    cntSxjd: number;   // V网价值评估
    cntGdzw: number;    // 加装高端组网
    cntQy: number;      // 加装权益
    cntD2c: number;     // 单天翼进融
    [key: string]: any;
}


const tabList = [
    { text: '经营结果', value: 3 },
    { text: '控流失', value: 1 },
    { text: '提价值', value: 2 },

]
const tabValue = ref(3)
const slectTypeText = ref('经营结果')


const tableData = ref<List[]>([]);


const curTimeType = ref('当月') //默认当月
const monthPopup = ref(false) // 月弹窗
const currentDate = ref([String(new Date().getFullYear()), String(new Date().getMonth() + 1)]);
const columnsType: DatePickerColumnType[] = ['year', 'month'];
const monthMinDate = new Date(2024, 9)
const monthMaxDate = new Date(new Date().getFullYear(), new Date().getMonth() - 1)
const endTime = ref('')
const startTime = ref('')

const actions = [
    {
        text: '当月',
        value: '0',
        handle: () => {

            initCurrentDate()
            curTimeType.value = '当月'
            loadList()
        }
    },
    {
        text: '每月',
        value: '1',
        handle: () => {

            monthPopup.value = true
        }
    },

]




function confirmMonth({ selectedValues }: any) {

    const year = parseInt(selectedValues[0], 10);
    const month = parseInt(selectedValues[1], 10);
    const lastDay = new Date(year, month, 0).getDate();
    endTime.value = `${selectedValues[0]}-${selectedValues[1]}-${lastDay}`;
    monthPopup.value = false
    curTimeType.value = '每月'
    loadList()
}









function changeSelectTab(item: any) {
    slectTypeText.value = item.text
}





function loadList() {
    let params = {
        day: endTime.value.replace(/-/g, '')
    }


    getClyyReportL2ListAPI(params).then((res: any) => {
        if (res.code == 200) {


            tableData.value = res.data
        }
    })
}


const formatterLsjz = (value: List, type: string, aim: number = 1): string => {
    if (type == 'VJzpg') {
        return ((((value[`cnt${type}`] ?? 0) / value['cntVAll'] / (value[`aim${type}`] ?? 1)) / aim) * 100).toFixed(2)
    } else if (type == 'Tyxy') {
        return ((((value[`cnt${type}`] ?? 0) / value['cntTyxyAll'] / (value[`aim${type}`] ?? 1)) / aim) * 100).toFixed(2)
    } else {
        return ((((value[`cnt${type}`] ?? 0) / (value[`aim${type}`] ?? 1)) / aim) * 100).toFixed(2)
    }





}



const initCurrentDate = () => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    let day = String(today.getDate()).padStart(2, '0');

    if (day === '01' || day === '02') {
        startTime.value = `${year}-${month}-01`;
        endTime.value = `${year}-${month}-${day}`;
    } else {
        today.setDate(today.getDate() - 2); // 当前时间T-2
        day = String(today.getDate()).padStart(2, '0');
        startTime.value = `${year}-${month}-01`;
        endTime.value = `${year}-${month}-${day}`;
    }

}

const formatter = (type: string, option: any) => {
    if (type === 'year') {
        option.text += '年';
    }
    if (type === 'month') {
        option.text += '月';
    }
    return option;
};

onMounted(() => {



    initCurrentDate()

    loadList()
})
</script>

<style lang="scss" scoped>
.card-header {
    // height: 46px;
    padding: 5px 8px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    // display: flex;
    // flex-direction: column;
    // align-items: center;
    // justify-content: space-between;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

    .selectOption {
        display: flex;
        align-items: center;
        flex: 1 0 auto;

        .selectOption-item {
            flex: 1 0 auto;
            background: #EDF5FF;
            border-radius: 5px 5px 5px 5px;
            font-weight: 400;
            font-size: 14px;
            color: #1A76FF;
            padding: 8px;
            display: flex;
            align-items: center;
            // justify-content: space-between;
            position: relative;
            justify-content: center;

            .play {
                transform: rotate(90deg);
                margin-left: 3px;
                position: absolute;
                right: 10px;
            }
        }

        .selectOption-item:active {
            background: #d8dee6;
        }
    }
}


.table-container {
    width: 100%;
    overflow-x: auto;
    margin-top: 10px;
    padding-bottom: 10px;
    position: relative;
    /* 添加相对定位 */

    table {
        width: 100%;
        border-collapse: collapse;
        text-align: center;
        font-size: 13px;
        position: relative;
        /* 添加相对定位 */

        th,
        td {
            padding: 8px;
            border: 1px solid #ddd;
            white-space: nowrap; // 禁止换行
        }

        .areaNameL2 {
            width: 100px;
            white-space: normal;
        }

        .sticky {
            /* 固定表格内容的门店列 */
            position: sticky;
            left: 0;
            background-color: #fff;
            /* 设置背景颜色以覆盖其他列 */
            z-index: 2;
            /* 提高z-index确保在最上层 */
            /* 确保门店列在最上层 */
            border-right: 2px solid #ddd !important;
            /* 添加右边框并确保显示 */
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
            /* 添加阴影效果增强视觉分离 */
        }

        .green {
            background-color: #92d050;
        }

        .yellow {
            background-color: #ffc000;
        }



        .main-title {
            font-size: 13px;
            font-weight: bold;
            padding: 12px;
            background-color: #2e70ba;
            color: #fff;
            text-align: center;
        }

        thead tr:nth-child(1) th {
            background-color: #2e70ba;
            color: #fff;
            font-weight: bold;
            padding: 10px;
            text-align: center;

            &.sticky {
                border-right: 2px solid #fff !important;
                /* 表头固定列的右边框 */
                box-shadow: 2px 0 5px rgba(0, 0, 0, 0.2);
                /* 表头阴影 */
            }
        }

        thead tr:nth-child(2) th {
            background-color: #2e70ba;
            color: #fff;
            font-weight: bold;
            padding: 8px;

            &.sticky {
                border-right: 2px solid #fff !important;
                /* 表头固定列的右边框 */
                box-shadow: 2px 0 5px rgba(0, 0, 0, 0.2);
                /* 表头阴影 */
            }
        }

        tbody tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        tbody tr:hover {
            background-color: #e6f7ff;
        }

        tbody td.highlight {
            background-color: #ffe58f;
            font-weight: bold;
        }
    }


}

:deep(.van-popover__wrapper) {
    flex: 1 0 auto;
}
</style>