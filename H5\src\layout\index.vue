<template>
    <div style="background-color: rgba(239, 241, 246,0.1)">
        <div class="main-page" ref="mainPage" :class="{
            userData: route.path == '/userData',

            tabbarVisible: !tabbarVisible,
        }">
            <RouterView v-slot="{ Component, route }">

                <transition name="zoom-out" mode="out-in">
                    <component :is="Component" :key="route.name" v-if="!route.meta.keepAlive" />
                </transition>
                <transition name="zoom-out" mode="out-in">
                    <keep-alive>
                        <component :is="Component" :key="route.name" v-if="route.meta.keepAlive" />
                    </keep-alive>
                </transition>

            </RouterView>

            <van-back-top target=".main-page" :bottom="height + 20" />
            <!-- <van-floating-bubble  style="height: 40px;width: 40px;--van-floating-bubble-icon-size:20px"
                axis="xy" v-model:offset="offset" magnetic="x" icon="revoke" :gap="15" @click="router.go(-1)" /> -->
            <van-floating-bubble v-if="canGoBack" style="height: 40px;width: 40px;--van-floating-bubble-icon-size:20px"
                axis="xy" v-model:offset="offset" magnetic="x" icon="revoke" :gap="30" @click="goToBack" />

        </div>


        <!-- v-if="route.meta.isTabbarView" -->
        <van-tabbar route class="tabbar" ref="tabbar" v-if="tabbarVisible">
            <van-tabbar-item v-for="menu in Menus" :key="menu.name" replace :name="menu.name" :to="menu.path">
                <template #icon>
                    <img :src="route.name == menu.name ? menu.meta.activeImg : menu.meta.inactiveImg" />
                </template>
                {{ menu.meta?.title }}
            </van-tabbar-item>
        </van-tabbar>


        <winnersList v-model:show="userInfo.isShowLh" v-if="userInfo.isShowLh"></winnersList>

    </div>
</template>

<script setup lang="ts">
import winnersList from "@/components/winnersList/index.vue";
import { useElementSize } from '@vueuse/core'
import { useRouter, useRoute, RouteLocationNormalized, RouteRecordNormalized } from 'vue-router';
import { ref, watch, ComputedRef, provide } from "vue";
import { useUserInfo } from '@/stores/userInfo';
const tabbar = ref(null)
const mainPage = ref(null)
const { width, height } = useElementSize(tabbar)
const { height: PageHeight } = useElementSize(mainPage)





function laodFloatingBubbleY() {
    offset.value.x = 15
    offset.value.y = PageHeight.value - 45
}
const offset = ref({ x: 0, y: 0 });


const route = useRoute()
const router = useRouter()
const userInfo = useUserInfo()



// 菜单
const Menus = computed(() => {
    return userInfo.getMenuList.map((item: MenuItem, index: number) => {
        let title = item.menuName
        if (item.menuCode == 'home' && item.children?.some((el: MenuItem) => el.menuCode == 'processContro')) {
            title = '运营'
        }
        return {
            name: item.menuCode,
            path: item.menuCode,
            id: item.id,
            meta: {
                title: title,
                inactiveImg: new URL(`../assets/tabbar/${item.menuCode}.png`, import.meta.url).href,
                activeImg: new URL(`../assets/tabbar/${item.menuCode}_active.png`, import.meta.url).href,
            },
        }
    })

})



// 创建一个响应式的路由栈
const routeStack = ref<RouteLocationNormalized[]>([]);

// 在全局前置守卫中添加路由到栈
router.beforeEach((to, from) => {
    // 直接使用 to.fullPath，而不是尝试在 matchedRoute 中查找
    if (routeStack.value.length === 0 || routeStack.value[routeStack.value.length - 1].fullPath !== to.fullPath) {
        if (routeStack.value.length === 0) {
            // if (to.path.includes('/stock')) {
            //     routeStack.value.push(to);

            // } else {
            routeStack.value.push(from, to);
            // }

        } else {
            if (to.path.includes('/resultOptimizationdDetail') && routeStack.value.some(item => item.path.includes('/resultOptimizationdDetail'))) {
                // todo 解决返回时，路由栈中存在多个相同页面
            } else {
                routeStack.value.push(to);
            }



        }
    }
});




// 判断是否可以返回
const canGoBack = computed(() => {
    return routeStack.value.length > 1 || route.query.isHasGoBack; // 确保有可返回的历史记录
});

// 处理返回逻辑          
function goToBack() {
    if (routeStack.value.length > 1) {
        routeStack.value.pop(); // 移除当前路由
        // const previousRoute = routeStack.value.pop(); // 获取上一个路由
        const previousRoute = routeStack.value[routeStack.value.length - 1]
        if (previousRoute) {
            router.push({ path: previousRoute.fullPath, query: previousRoute.query }); // 导航到上一个路由
        }
    } else {
        router.back(); // 默认行为
    }
}
provide('routeStack', routeStack)
provide('setRouteStack', setRouteStack)

function setRouteStack(params: RouteLocationNormalized[]) {
    routeStack.value = params
}

watch(() => PageHeight.value, () => {
    laodFloatingBubbleY()

})
onMounted(() => {
    // 由于统一工号登录，code是从url中带过来
    userInfo.codeInit()
    laodFloatingBubbleY()
})

const tabbarVisible = computed(() => {


    //这个角色不需要展示tabar
    if (userInfo.getMenuList.length <= 1) {
        return false
    } else {
        return true
    }



})




</script>

<style lang="scss" scoped>
$tabbarHeight: 65px;

.main-page {
    box-sizing: border-box;
    overflow-y: scroll;
    overflow-x: hidden;
    // background: linear-gradient(180deg, #A3D0FF 12%, #F5F5F5 100%);
    height: calc(100vh - #{$tabbarHeight});
    background: url('@/assets/home/<USER>') no-repeat;
    background-size: 100%;
    padding: 10px 12px;
}

.userData {
    background: url('@/assets/home/<USER>') no-repeat !important;
    background-size: 100% !important;

}

.tabbarVisible {
    height: calc(100vh);
}

.tabbar {
    bottom: 0;
    width: 100%;
    height: #{$tabbarHeight};
    position: fixed;
}

.van-floating-bubble__icon {
    font-size: 20px !important;
}
</style>