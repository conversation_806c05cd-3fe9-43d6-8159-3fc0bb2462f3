
import request from "../utils/request";
import configAxios from './config'

// 客户拜访
export function visitSaveAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/visit/save`,
        method: "post",
        data,
    });
}

//客户拜访查询
export function getVisitListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/visit/page`,
        method: "POST",
        data,
    });
}

//客户拜访总览
export function getVisitCountAPI() {
    return request({
        url: `${configAxios.taskHubServer}/h5/visit/count`,
        method: "POST",

    });
}
//客户拜访工作收集
export function visitDataSaveAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/visit/data/save`,
        method: "POST",
        data,
    });
}

//客户拜访工作收集列表
export function getVisitDataListAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/visit/data/page`,
        method: "POST",
        data,
    });
}
