<template>
    <div class='mt-16 container_ Contribution'>
        <component :is="item.moudule_" v-for="item in componentOption" :key="item.moudule_name"
            :ref="(el: HTMLDivElement) => item.ref_name && (refsArray[item.ref_name] = el)"
            v-on="generateEventHandlers(item.EventsList || [])"
            @="emitEventsListGenerateEventHandlers(item.emitEventsList || [])"
            v-bind="mergePropsAndVModelBindings(item.props, item.vModels)">
        </component>

        <van-overlay :show="lading" class-name="loadingoverlay" :lock-scroll="false">
            <div class="wrapper" @click.stop>

                <van-loading type="spinner" size="48px" color="#ffa800" vertical><span
                        class="fs20 text-warning">加载中...</span></van-loading>

            </div>
        </van-overlay>
    </div>
</template>

<script setup lang='ts'>
import { showSuccessToast, showFailToast } from 'vant';
import { getContributionDataLeaderAPI } from "@/api/home";
import componentList from "@/views/template/Contribution/config.ts";
import { useUserInfo } from "@/stores/userInfo";
import { ref, reactive, onMounted, inject } from "vue";
import { EventsHooks, ComponentOptionProps } from "@/hooks_modules/Eventhooks";
import { useRoute, useRouter, RouteLocationNormalized } from 'vue-router';
import { cloneDeep } from "lodash-es";
const route = useRoute()
const router = useRouter()
const { generateEventHandlers, emitEventsListGenerateEventHandlers, mergePropsAndVModelBindings } = EventsHooks()
const userInfoStore = useUserInfo();
import { Encrypt } from "@/utils/crypto/index";
const refsArray = ref<{ [key: string]: HTMLDivElement | null }>({});
const componentOption: ComputedRef<ComponentOptionProps[]> = computed(() => {
    let contributionlist = [
        {
            id: 40,
            menuCode: "headerArea",
            parentCode: "ContributionArea",
            menuName: "头部标题",
            type: 2,
            sort: 1
        },
        {
            id: 39,
            menuCode: "overviewCardArea",
            parentCode: "ContributionArea",
            menuName: "总览看板",
            type: 2,
            sort: 2
        },
        {
            id: 38,
            menuCode: "listArea",
            parentCode: "ContributionArea",
            menuName: "列表",
            type: 2,
            sort: 3
        }
    ]

    const listOption = contributionlist.map((item: MenuItem) => getMenuList_(item))



    return listOption
})


type TableData = {
    ContributionMenu: ContributionLeader,
    list: ContributionItem[]
}


const tableData = reactive<TableData>({
    ContributionMenu: {
        type: 1,
        points: 0,
        tianYi: 0,
        kuanDai: 0,
        outCall: 0,
        signCount: 0,
        repairOrder: 0,
        repairOrderTotal: 0,
        businessRecords: 0,
        xinliangZgz: 0,
        ronghe: 0,
        tianyiXuyue: 0,
        kuandaiXuyue: 0
    },
    list: []
})
const { ContributionMenu, list } = toRefs(tableData)


const curSelectType = ref<number | undefined>(4)  //     0  片区       1  门店    2  人员  3分局  4 区域
const curSelectItem = ref<Partial<ContributionItem>>({})   //当前点击的时候 选择的id
const allIsShow = ref(false)  // 是否展开所有

const getMenuList_ = (item: MenuItem): ComponentOptionProps => {
    let obj: ComponentOptionProps = {
        moudule_: componentList[item.menuCode].moudule_,
        moudule_name: item.menuCode,
        ref_name: item.menuCode,
    }

    switch (item.menuCode) {
        case 'headerArea':
            obj = {
                ...obj,
                props: {
                    curSelectType: curSelectType.value,
                },
                emitEventsList: [
                    { eventName: 'gotoHome', handler: gotoHome },
                ],
            }
            break
        case 'overviewCardArea':
            obj = {
                ...obj,
                props: {
                    ContributionMenu: ContributionMenu.value,
                    curSelectType: curSelectType.value,
                    curSelectItem: curSelectItem.value,
                    allIsShow: allIsShow.value

                },
                emitEventsList: [
                    { eventName: 'changeTime', handler: changeTime },
                    { eventName: 'changeAllIsShow', handler: changeAllIsShow },

                ],
            }
            break
        case 'listArea':
            obj = {
                ...obj,
                props: {
                    dataItem: list.value,
                    curSelectType: curSelectType.value,
                },
                emitEventsList: [
                    { eventName: 'viewStores', handler: viewStores },

                ],
            }
            break
    }
    return obj
}


// 定义区域优先级映射，数字越小优先级越高
const AreaPriorityMap: { [area: string]: number } = {
    '鼓楼': 1,
    '秦淮': 2,
    '栖霞': 3,
    '建邺': 4,
    '雨花': 5,
    '玄武': 6,
    '化工园': 7,
    '江宁': 8,
    '浦口': 9,
    '六合': 10,
    '溧水': 11,
    '高淳': 12,
    '省政企': 13,
    '要客中心': 14,
    '工交中心': 15,
    '大客中心': 16,
    '开渠': 17,
    '全区': 18,
};


const lading = ref(false)
function loadContributionData() {
    let params: any = {
        type: curSelectType.value,
        areaId: curSelectItem.value?.areaId,
        startTime: formLine.startTime ? `${formLine.startTime} 00:00:00` : '',
        endTime: formLine.endTime ? `${formLine.endTime} 23:59:59` : ''
    }



    lading.value = true
    getContributionDataLeaderAPI(params).then(res => {
        for (const key in ContributionMenu.value) {
            tableData.ContributionMenu[key] = res.data[key] ?? 0
        }
        for (const item of res.data.develops || []) {
            item.isShow = false
        }
        tableData.list = res.data.develops.filter((item: any) => item.name != '政企')
        // 排序：先按照角色优先级排序，再按照积分排序
        if (curSelectType.value == 4) {
            tableData.list.sort((item1, item2) => {
                const areaPriority1 = AreaPriorityMap[item1.name] || Infinity;
                const areaPriority2 = AreaPriorityMap[item2.name] || Infinity;
                return areaPriority1 - areaPriority2; // 先比较角色优先级
            });
        }


    }).catch(() => {
        showFailToast('请求失败');
    }).finally(() => {
        lading.value = false
    })
}


function gotoHome() {
    curSelectType.value = 4
    curSelectItem.value = {}
    loadContributionData()

}








const routeStack = inject<any>('routeStack')
const setRouteStack = inject<Function>('setRouteStack') as Function
function viewStores(row: ContributionItem) {
    const { VITE_BASE_URL, VITE_BASE_ASSEST } = import.meta.env

    switch (row.type) {
        case 3:
            if (row.staffCode) {
                const backToken = userInfoStore.getToken
                const backAESkey = userInfoStore.getAESkey
                userInfoStore.RecordToken()

                const curRoute = cloneDeep(route)
                // location.href = `${VITE_BASE_URL}${VITE_BASE_ASSEST}?randomParam=${row.staffCode}#/home`
                // location.href = `http://localhost:5173/task-hub/task-hub-web/?randomParam=${row.staffCode}#/home?isHasGoBack=true`
                router.push({
                    path: '/home',
                    query: {
                        randomParam: Encrypt(row.staffCode),
                        isHasGoBack: 'true'
                    }
                }).then(() => {
                    const curRouteStackIndex = routeStack.value.findIndex((item: any) => item.path == curRoute.path)
                    curRoute.query.backToken = backToken
                    curRoute.query.backAESkey = backAESkey
                    routeStack.value.splice(curRouteStackIndex, 1, curRoute)
                    setRouteStack(routeStack.value)
                    userInfoStore.codeInit()
                })

                return
            } else {
                showFailToast('当前分局未设置分局长暂不支持查看！');
                return
            }

        case 4:
            curSelectItem.value = row
            curSelectType.value = 3
            loadContributionData()
            return
    }




}




function changeAllIsShow() {
    allIsShow.value = !allIsShow.value
    for (const item of tableData.list) {
        item.isShow = allIsShow.value
    }

}

const formLine = reactive({
    startTime: '',
    endTime: ''
})
function changeTime(formline: any) {
    formLine.startTime = formline.startTime
    formLine.endTime = formline.endTime

    loadContributionData()
}






// 生命周期钩子
onMounted(() => {
    if (route.query.backToken) {
        userInfoStore.codeInit()
    }

    loadContributionData()
});


</script>

<style lang='scss' scoped>
.Contribution {
    position: relative;
}

.wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}



.loadingoverlay {
    position: absolute;
    border-radius: 5px;
    background: rgba(0, 0, 0, 0.7)
}

.container_body {
    // padding: 15px;
}



.icon-item span {
    font-size: 12px;
    color: #3D3D3D;
    text-align: center;
}



.activity-container {
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    gap: 20px;


}
</style>