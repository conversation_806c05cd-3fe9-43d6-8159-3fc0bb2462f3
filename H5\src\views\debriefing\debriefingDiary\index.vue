<template>
  <div class="card container_">
    <div class="container__header">
      <div>
        <img src="../../template/MyDay/assets/images/fpIcon.png" alt="" srcset="" />
        <span class="ml-6">复盘日志</span>
      </div>
    </div>
    <van-list v-model:loading="loading" :finished="finished" :finished-text="tableData.length > 0 ? '没有更多了' : ''"
      error-text="请求失败，点击重新加载" @load="onLoad">

      <div class="content" v-for="(item, index) in tableData" :key="index">
        <van-row>
          <van-col span="12" class="font font_date">{{ item.date }}</van-col>
          <van-col span="12" class="font font_title" style="text-align: right;">
            <van-tag round type="primary" size="large">{{ item.roleName }}</van-tag>

          </van-col>
        </van-row>
        <div class="font_row">
          <div class="font font_label">提交者：</div>
          <div class="font font_title">
            <span v-getName="{ item: item }">{{ item.name }}</span>
            <span class="ml-5 fs10">{{ item.staffCode }}</span>
          </div>
        </div>
        <div class="font_row">
          <div class="font font_label">复盘时间</div>
          <div class="font font_title">{{ item.time }}</div>
        </div>
        <div class=" imgList" v-if="item.description">
          <div class="font font_label">备注</div>
          <div class="font font_title pl-18 pr-18">
            <van-text-ellipsis rows="3" :content="item.description" expand-text="展开" collapse-text="收起" />

          </div>
        </div>
        <div class="imgList" v-if="item.images.length > 0">
          <div class="font font_label">复盘照片</div>
          <div class="font font_value">
            <van-image width="100" :radius="5" v-imgPreview="[item.images, indexE]" lazy-load fit="cover" height="100"
              :src="el" v-for="el, indexE in item.images" />
          </div>
        </div>
      </div>
    </van-list>
    <van-empty description="暂无数据" v-if="tableData.length == 0" />

  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, toRefs } from "vue";
import { getReviewListAPI } from "@/api/review";
import { useRoute } from "vue-router";
const route = useRoute();
const { VITE_IMAGE_URL } = import.meta.env

type tableData = {
  date: string,
  time: string,
  images: string[],
  [key: string]: any
}

const tableData = ref<tableData[]>([]);

const loading = ref(false)
const finished = ref(true)


const pageSize = ref(10);
const total = ref(0);
const pageNum = ref(1);


function loadList() {
  let params: { [key: string]: any } = {
    pageNum: pageNum.value,
    pageSize: pageSize.value,
  };
  if (route.query.endTime) {
    params.endTime = `${route.query.endTime} 23:59:59`
  }
  if (route.query.startTime) {
    params.startTime = `${route.query.startTime} 00:00:00`
  }
  if (route.query.staffCode) {
    params.staffCode = route.query.staffCode
  }

  loading.value = true
  getReviewListAPI(params).then((res: any) => {
    if (res.code == 200) {

      for (const item of res.data.records || []) {
        item.date = item.createTime.split(' ')[0]
        item.time = item.createTime.split(' ')[1]
        item.images = item.images ? item.images.split(',') : []
        item.images = item.images.map((el: string) => {
          if (el.indexOf(VITE_IMAGE_URL) == -1) {
            return VITE_IMAGE_URL + el
          } else {
            return el
          }

        })
      }
      tableData.value = [...tableData.value, ...res.data.records]
      total.value = res.data.total

      if (total.value <= tableData.value.length) {
        finished.value = true
      } else {
        finished.value = false
      }
    }
  }).finally(() => {
    loading.value = false
  })
}


function onLoad() {
  pageNum.value = pageNum.value + 1
  loadList()
}




onMounted(() => {
  loadList();
});
</script>

<style lang="scss" scoped>
.card {
  min-height: 100%;
  background: #fbfcff;
  // overflow-x: hidden;
  box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
  border-radius: 10px 10px 0px 0px;
}

.content {
  background: #EDF5FF;
  border-radius: 10px 10px 10px 10px;
  margin: 13px 15px;
  border-bottom: 1px solid #EEF5FF;

  .van-row {
    padding: 15px 18px;
    border-bottom: 1px solid #edf5ff;
  }
}



.font_value {
  text-align: right;
}

.font {
  font-family: Source Han Sans, Source Han Sans;
  font-style: normal;
  text-transform: none;
}

.font_date {
  font-size: 18px;
  line-height: 20px;
  color: #1a76ff;
  // text-align: center;
  font-weight: 500;
}

.font_label {
  flex: 0 0 auto;
  padding: 15px 18px;
  font-weight: 500;
  font-size: 14px;
  color: #3D3D3D;
  line-height: 16px;
}

.imgList {
  // border-bottom: 1px solid #EEF5FF;
  padding-bottom: 30px;
}

.font_value {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  place-items: center;
  padding: 15px 18px;
  padding-top: 0px;

  img {
    border-radius: 5px;
  }
}

.font_row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 18px
}

.font_title {
  font-weight: 400;
  font-size: 14px;
  color: #A8A8A8;
}
</style>
