import copy from './copy.ts'
import imgPreview from './imgPreview.ts'
import loading from './loading/loading.ts'
import swipeGesture from './swipe.ts'
import getName from './getNameByStaffCode.ts'
import draggable from './draggable.ts'



// 自定义指令
const directives: { [key: string]: any } = {
  copy,
  imgPreview,
  loading,
  swipeGesture,
  getName,
  draggable,
}

export default {
  install(Vue: any) {
    Object.keys(directives).forEach((key) => {
      Vue.use(directives[key])
    })
  }
}