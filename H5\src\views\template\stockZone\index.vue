<template>
  <div class="box mt-16 container_">
    <div class="container__header">
      <div>
        <img src="./assets/stockZone.svg" alt="" srcset="" />
        <span class="ml-6">我的责任田</span>
      </div>
    </div>
    <div class="container_body">
      <div class="card-box">
        <van-swipe indicator-color="#66C6F2" @change="onChange">
          <van-swipe-item v-for="(list, index) in mydayConfig" :key="index">
            <transition name="fade-transform">
              <van-row :gutter="[10, 10]" v-show="index === current">
                <van-col style="box-sizing: border-box;" span="8" v-for="(item, index) in list" :key="item.menuCode"
                  @click="navTo(item)">
                  <div class="card-menu">
                    <div class="card-image">
                      <van-image class="card-icon" contain :src="item.icon_">
                      </van-image>
                      <div class="card-content-title">
                        {{ item.menuName }}
                      </div>
                    </div>
                  </div>
                </van-col>
              </van-row>
            </transition>
          </van-swipe-item>
        </van-swipe>

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getBusinessCodeAPI } from "@/api/common";
import { useUserInfo } from "@/stores/userInfo";
import { getRemindListByStaffAPI, getRemindStatusAPI } from "@/api/remind";
import { ref, reactive, onMounted, onUnmounted, computed, toRefs } from "vue";
import { useRoute, useRouter } from "vue-router";
import commingSoon from "./assets/commingSoon.svg";


const router = useRouter();
const route = useRoute();
const userInfoStore = useUserInfo();
const props = defineProps({});


const mydayConfig = computed(() => {
  let list = userInfoStore.getMenuList.filter((item: MenuItem) => item.menuCode === 'home')[0]?.children || []
  let MyDaylist = list.filter((item: MenuItem) => item.menuCode === 'stockZone')[0]?.children || []
  MyDaylist.forEach((item, index) => {
    item.icon_ = new URL(`./assets/${item.icon}.svg`, import.meta.url).href
    item.isLink = item.type == 3 ? true : false

  });


  const groupedList = [];
  const LENGTH_ = 6

  for (let i = 0; i < MyDaylist.length; i += LENGTH_) {
    groupedList.push(MyDaylist.slice(i, i + LENGTH_));
  }


  return groupedList;


})







defineExpose({});


const { VITE_SALE_URL_Home, VITE_NINE_CHAPTER_URL } = import.meta.env

const navTo = (item: any) => {
  if (item.isLink) {
    if (item.menuCode == 'servicePoints') {
      window.location.href = 'http://wechat.jlonline.com/qxb/sp-front'
    } else if (item.menuCode == 'nineChapters') {
      window.location.href = VITE_NINE_CHAPTER_URL
      return
    } else {
      getBusinessCodeAPI().then((res: any) => {
        if (res.code == 200) {
          window.location.href = `${VITE_SALE_URL_Home}${res.data}`
        }

      }).catch((error => {
        console.log(error);

      }))
    }

  } else {
    if (item.menuCode == 'customerProfile') {
      showToast({
        message: '待上线\n敬请期待',
        iconSize: 45,
        icon: commingSoon,
      });
      return
    }
    router.push(item.path);

  }
};



const current = ref(0)
function onChange(index: any) {
  current.value = index;
}

onMounted(() => {

});

onBeforeUnmount(() => {

});
</script>

<style lang="scss" scoped>
.box {
  background: #fbfcff;
  box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #ffffff;
  border-radius: 3%;
}


.container__body_header {
  background: #EDF5FF;
  border-radius: 5px;
  padding: 9px 7px;
  display: grid;
  grid-template-columns: 18px auto;
  align-items: center;
  gap: 5px;
  margin-bottom: 8px;

  img {
    width: 15px;
    height: 15px;
  }

  .tips {
    font-weight: 400;
    font-size: 12px;
    color: #FF5D44;

    .notice-swipe {
      height: 25px;
      line-height: 28px;

    }
  }
}


.box::after {
  content: "";
  display: block;
  clear: both;
}

// .card-box {
//   display: grid;
//   grid-template-columns: repeat(3, 1fr);
//   gap: 10px;
// }


// 背景色
.card-menu {
  background: rgba(103, 159, 255, 0.15);
  aspect-ratio: 1/1;
  border-radius: 5%;
}

// 图片与文字区域布局
.card-image {

  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  flex-flow: column nowrap;
  justify-content: center;
  align-items: center;
}

.card-icon {
  width: 51px;
  box-sizing: border-box;
  padding: 5%;
}

.card-content-title {
  font-size: 12px;
  padding: 5%;
  box-sizing: border-box;
  font-weight: 550;
}

::v-deep(.van-swipe__indicator) {
  background-color: #a8a8a8;
}
</style>