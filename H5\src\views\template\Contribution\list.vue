<template>


    <div class=" container_body activity-container">




        <div class="person-section " v-for="item, index in dataItem" :key="item.staffCode || index">
            <div class="header">
                <span @click="handleClick(item)" style="flex: 0 1 auto;">
                    <span class="name" v-getName="{ item: item }">{{ item.name }}</span>
                    <span class="post" v-if="item.type == 2">
                        {{ item.primaryRoleName }}
                    </span>
                </span>

                <van-popover v-if="item.type == 2">

                    <div class="popoverBody" v-if="!isGov">
                        <li>100积分=5活力值</li>
                        <li>宽带和主推融合=2活力值</li>
                        <li>其它=1活力值</li>
                        <li>其中装维工单为竣工工单数/当日预约工单数，按竣工数计算活力值</li>
                        <li>商机录入为商机助手采集的商机数量</li>
                    </div>
                    <div class="popoverBody" v-else>
                        <li>100积分=5活力值</li>
                        <li>1个天翼或5G主卡=1活力值</li>
                        <li>1个FTTR-B=3活力值</li>
                        <li>1条商专=5活力值</li>
                        <li>1条互专=30活力值</li>
                        <li>1条商机=1活力值</li>
                        <li>1次拜访=5活力值</li>
                    </div>
                    <template #reference>

                        <span class="activity-score">
                            <span>{{ item.huoli }}</span>
                            <span>活力</span>
                            <van-icon name="question" color="#1A76FF" />
                        </span>
                    </template>
                </van-popover>
                <div class="playBox">
                    <van-icon name="play" @click="item.isShow = !item.isShow" class="play"
                        :class="item.isShow ? '' : 'playHide'" color="#1A76FF" />
                </div>

            </div>
            <div class="tips" v-if="item.type == 2">
                <span class="ml-5">工号： {{ item.staffCode }} </span>
            </div>
            <collapseTransition>
                <div class="icon-grid" v-show="item.isShow">
                    <div v-for="el in data" :key="item.props" class="icon-item">
                        <van-badge v-if="el.denominator" color="#FF9760">
                            <img :src="el.img" alt="" />
                            <template #content>
                                {{ item[el.props] }}/{{ item[el.denominator] }}
                            </template>
                        </van-badge>
                        <van-badge v-else :content="item[el.props]" color="#FF9760">
                            <img :src="el.img" alt="" />
                        </van-badge>
                        <span>{{ el.name }}</span>
                    </div>
                </div>
            </collapseTransition>
        </div>
        <van-empty description="暂无数据" v-if="dataItem.length == 0" />



    </div>

</template>

<script setup lang='ts'>
import collapseTransition from "@/components/collapse-transition/index.vue";
import { ref, reactive, onMounted, onUnmounted, computed, toRefs, PropType } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const props = defineProps({
    dataItem: {
        type: Array as PropType<ContributionItem[]>,
        default: (): ContributionItem[] => ([])
    },
    isGov: {
        type: Boolean,
        default: false
    }

});
const emits = defineEmits(['selectRow']);
const { dataItem, isGov } = toRefs(props);



const data = computed(() => {


    if (isGov.value) {


        return [
            { name: "政企积分", props: "govQudaoJifen", img: new URL("./assets/govQudaoJifen.svg", import.meta.url).href },
            { name: "政企天翼", props: "govTianyi", img: new URL("./assets/govTianyi.svg", import.meta.url).href },
            { name: "5G主卡", props: "gov5g", img: new URL("./assets/gov5g.svg", import.meta.url).href },
            { name: "FTTR-B", props: "govFttrb", img: new URL("./assets/govFttrb.svg", import.meta.url).href },
            { name: "商专", props: "govShangZhuan", img: new URL("./assets/govShangZhuan.svg", import.meta.url).href },
            { name: "互专", props: "govHuZhuan", img: new URL("./assets/govHuZhuan.svg", import.meta.url).href },
            // { name: "云电脑", props: "govCloudComputer", img: new URL("./assets/govCloudComputer.svg", import.meta.url).href },
            // { name: "天翼视联", props: "govTianyiShilian", img: new URL("./assets/govTianyiShilian.svg", import.meta.url).href },
            // { name: "小微", props: "govXiaowei", img: new URL("./assets/govXiaowei.svg", import.meta.url).href },
            { name: "商机录入", props: "bizRecord", img: new URL("./assets/bizRecord.svg", import.meta.url).href },
            { name: "客户拜访", props: "visitCount", img: new URL("./assets/visitCount.svg", import.meta.url).href },

        ]
    } else {
        return [
            { name: "积分", props: "points", img: new URL("./assets/Points.svg", import.meta.url).href },
            { name: "天翼", props: "tianYi", img: new URL("./assets/Tianyi.svg", import.meta.url).href },
            { name: "宽带", props: "kuanDai", img: new URL("./assets/Broadband.svg", import.meta.url).href },
            { name: "外呼", props: "outCall", img: new URL("./assets/outboundCall.svg", import.meta.url).href },
            { name: "工作写实", props: "signCount", img: new URL("./assets/checkIn.svg", import.meta.url).href },
            { name: "商机录入", props: "businessRecords", img: new URL("./assets/opportunityEntry.svg", import.meta.url).href },
            { name: "主推融合", props: "xinliangZgz", img: new URL("./assets/mainPromotion.svg", import.meta.url).href },
            { name: "天翼续约", props: "tianyiXuyue", img: new URL("./assets/tianyiRenewal.svg", import.meta.url).href },
            { name: "宽带续约", props: "kuandaiXuyue", img: new URL("./assets/broadbandRenewal.svg", import.meta.url).href },
            {
                name: "装维工单",
                props: "repairOrder",
                denominator: "repairOrderTotal",
                img: new URL("./assets/maintenanceOrder.svg", import.meta.url).href
            }
        ]
    }
})






function handleClick(row: ContributionItem) {
    if (row.type == 2) return
    emits('selectRow', row)
}

defineExpose({

});




// 生命周期钩子
onMounted(() => {

});


onUnmounted(() => {

});
</script>

<style lang='scss' scoped>
.person-section {
    background-color: #EDF5FF;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.header {
    // display: grid;
    // grid-template-columns: auto max-content 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    column-gap: 10px;
}

.playBox {
    flex: 0 0 auto;
    display: grid;
    place-items: center;
    width: 25px;
    height: 25px;
    border-radius: 10px;
    margin-left: 10px;
    background: #d4e0ff70;

    .play {
        transform: rotate(90deg);

        transition: transform 0.3s;


    }

    .playHide {
        transform: rotate(-90deg);
    }
}



.name {
    font-size: 18px;
    font-weight: bold;
    color: #3D3D3D;
    line-height: 20px;
}

.post {
    font-weight: 400;
    font-size: 14px;
    color: #3D3D3D;
    line-height: 16px;
    margin-left: 15px;
}

.activity-score {
    &>span:first-child {
        font-family: 'YouSheBiaoTiHei, YouSheBiaoTiHei';
        font-weight: 400;
        font-size: 24px;
        color: #1A76FF;
        line-height: 27px;
        font-weight: bold;
    }

    &>span:last-child {
        font-weight: 500;
        font-size: 10px;
        color: #3D3D3D;
        margin-left: 3px;
    }
}

.icon-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(45px, 1fr));
    gap: 0px 10px;
    margin-top: 15px;
}

.icon-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 0px;
    border-radius: 8px;
    color: #3D3D3D;
    position: relative;


    img {
        width: 34px;
        height: 34px;
        margin-bottom: 4px;
    }
}



.icon-item span {
    font-size: 12px;
    color: #3D3D3D;
    text-align: center;
}

.popoverBody {
    font-size: 13px;
    padding: 5px 10px;
}

:deep(.van-popover__wrapper) {
    flex: 0 0 auto;
}

.tips {
    background: linear-gradient(90deg, #DBEBFF 0%, #D4E0FF 100%);
    border-radius: 5px;
    padding: 3px 5px;
    display: flex;
    justify-content: space-between;
    font-weight: 400;
    font-size: 12px;
    color: #3D3D3D;
    text-align: center;
    font-style: normal;
    text-transform: none;
    margin-top: 5px;


}
</style>