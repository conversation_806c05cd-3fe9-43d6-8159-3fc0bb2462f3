.myEcharts {
    width: 100%;
    height: 300px;
}

.card-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
    border-bottom: 1px solid #EEF5FF;

    .card {
        background-color: #EDF5FF;
        border-radius: 5px;
        padding: 10px;
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            // margin-bottom: 8px;

            .role-name {
                font-size: 14px;
                font-weight: bold;
                color: #3D3D3D;
            }

            .go-button {
                background-color: #1A76FF;
                color: #fff;
                padding: 3px 5px;
                border-radius: 20px;
                cursor: pointer;
                font-weight: 400;
                font-size: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        }

        .card-body {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 15px;

            .status-block {
                width: 100%;
                display: flex;
                align-items: center;
                padding: 3px 5px;

                img {
                    width: 28px;
                }

                .info {
                    flex: 1 0 auto;

                    p {
                        margin: 0;
                        font-size: 12px;
                        color: #3D3D3D;
                        line-height: 18px;
                        margin-left: 5px;
                    }
                }

            }
        }
    }

    .overvirwCard {
        background-color: transparent;
        box-shadow: none;
        padding: 0px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        margin: 10px 0px;
        margin-bottom: 20px;

        gap: 10px;

        .card-body {
            border-radius: 5px 5px 5px 5px;
            padding: 4px 2px;

            .status-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 8px;

                .color0 {
                    font-weight: 500;
                    font-size: 12px;
                    color: #C92528;
                }

                .color2 {
                    font-weight: 500;
                    font-size: 12px;
                    color: #199964;
                }
            }
        }






    }
}