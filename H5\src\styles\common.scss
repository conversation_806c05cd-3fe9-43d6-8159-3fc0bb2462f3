.refreshButton {
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  user-select: none;

  i {
    display: grid;
    place-items: center;
    width: 100%;
    height: 100%;
    border-radius: 5px;
    color: #b5b5c3;
    background-color: transparent;
    border-color: transparent;
    cursor: pointer;
    transition: all 0.1s ease-in;
  }
}

.refreshButton i:hover {
  color: #0bb783;
  background-color: #c8d1da;
  border-color: transparent;
}

.refreshButton i:active {
  transform: translateX(3px) translateY(2px);
}

.homeBox {
  background-color: rgb(242, 246, 255, 0.25);
  border-radius: 20px;
  overflow: hidden;
  padding: 15px 30px;
  padding-bottom: 100px;
}

.extraTip {
  font-size: 12px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #ff6e6e;
  line-height: 22px;
  text-align: justify;
}

.eps {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.font-weight-light {
  font-weight: 300 !important;
}

.font-weight-lighter {
  font-weight: lighter !important;
}

.font-weight-normal {
  font-weight: 400 !important;
}

.font-weight-bold {
  font-weight: 500 !important;
  cursor: pointer;
  text-decoration: underline;
}

.font-weight-bolder {
  font-weight: 600 !important;
}

.font-italic {
  font-style: italic !important;
}

.text-white {
  color: #ffffff !important;
}

.text-primary {
  color: #0bb783 !important;
}

a.text-primary:hover,
a.text-primary:focus {
  color: #076f4f !important;
}

.text-secondary {
  color: #e4e6ef !important;
}

a.text-secondary:hover,
a.text-secondary:focus {
  color: #b4bad3 !important;
}

.text-success {
  color: #1bc5bd !important;
}

a.text-success:hover,
a.text-success:focus {
  color: #12827c !important;
}

.text-info {
  color: #8950fc !important;
}

a.text-info:hover,
a.text-info:focus {
  color: #5605fb !important;
}

.text-warning {
  color: #ffa800 !important;
}

a.text-warning:hover,
a.text-warning:focus {
  color: #b37600 !important;
}

.text-danger {
  color: #f64e60 !important;
}

a.text-danger:hover,
a.text-danger:focus {
  color: #ec0c24 !important;
}

.text-light {
  color: #f3f6f9 !important;
}

a.text-light:hover,
a.text-light:focus {
  color: #c0d0e0 !important;
}

.text-dark {
  color: #181c32 !important;
}

a.text-dark:hover,
a.text-dark:focus {
  color: black !important;
}

.text-white {
  color: #ffffff !important;
}

a.text-white:hover,
a.text-white:focus {
  color: #d9d9d9 !important;
}

.text-body {
  color: #3f4254 !important;
}

.text-muted {
  color: #b5b5c3 !important;
}

.text-black-50 {
  color: rgba(0, 0, 0, 0.5) !important;
}

.text-white-50 {
  color: rgba(255, 255, 255, 0.5) !important;
}

.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-break {
  word-break: break-word !important;
  word-wrap: break-word !important;
}

.text-reset {
  color: inherit !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

.label-dot {
  line-height: 6px;
  min-height: 6px;
  min-width: 6px;
  height: 6px;
  width: 6px;
  border-radius: 50%;
  display: inline-block;
  font-size: 0 !important;
  vertical-align: middle;
  text-align: center;
  background-color: #0bb783;
}



.container_ {
  background: #FBFCFF;
  box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
  border-radius: 10px 10px 10px 10px;

  .container__header {
    padding: 13px 15px;
    border-bottom: 1px solid #EEF5FF;
    display: flex;
    align-items: center;

    justify-content: space-between;

    &>div:first-child {
      display: flex;
      align-items: center;
      font-family: 'Source Han Sans, Source Han Sans';
      font-weight: 700;
      font-size: 16px;
      color: #3D3D3D;
      line-height: 18px;

      img {
        width: 17px;
        height: 17px;
      }
    }
  }

  .container_body {
    padding: 13px 15px;
  }
}


.popoverBody {

  padding: 5px 10px;

  &>li {
    font-size: 13px;
  }
}

.go-button {
  background-color: #1A76FF;
  color: #fff;
  padding: 3px 5px;
  border-radius: 20px;
  cursor: pointer;
  font-weight: 400;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}


.row-item {
  font-weight: 400;
  font-size: 12px;
  color: #2079FF;
  text-align: center;
}

.table-header {
  border-bottom: 1px solid #EDF5FF;
  padding-bottom: 10px;
}

.table-body {
  border-bottom: 1px solid #EDF5FF;
  padding: 10px 0px;
}

.col-item {
  font-weight: 400;
  font-size: 12px;
  color: #3D3D3D;
  text-align: center;
}


// dictList-list
.dictList {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;

  .dictItem {
    background: #F3F4F6;
    border-radius: 5px;
    font-weight: 400;
    font-size: 13px;
    color: #666666;
    padding: 5px;
  }

  .activeItem {
    background: #EDF5FF;
    color: #1A76FF;
  }

}