
import request from "../utils/request";
import configAxios from './config'

//提醒
export function remindSaveAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/remind/save`,
        method: "post",
        data,
    });
}

//查询是否有提醒
export function getRemindListByStaffAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/remind/listByStaff`,
        method: "post",
        data,
    });
}
//方案递单列表
export function getRemindStatusAPI(data: any) {
    return request({
        url: `${configAxios.taskHubServer}/h5/remind/status`,
        method: "post",
        data,
    });
}