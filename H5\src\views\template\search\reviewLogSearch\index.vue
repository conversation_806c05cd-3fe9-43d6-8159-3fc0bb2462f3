<template>
    <div class="box">
        <div class="box_top">
            <div class="Branch_name">
                {{ curTitle }}
            </div>
            <!-- <div class="history" @click="viewDebriefingDiary">
                历史搜索
                <van-icon size="16px" name="clock-o" class="ml-5" color="#1A76FF" />
            </div> -->
        </div>
        <div class="selectOptionList">
            <div @click="changeSelete(item.value)" class="item" v-for="item in selectOptionList" :key="item.value"
                :class="item.value == curSelect ? 'activeItem' : ''">
                {{ item.text }}
            </div>
        </div>
        <div class="selectTime" @click="selectTime">
            <div>
                <div class="startTime">起始日期</div>
                <div class="timeValue" :class="formline.startTime ? 'activeTime' : ''">{{ formline.startTime || '选择日期'
                    }}
                </div>
            </div>
            <div>
                <img src="@/assets/arrows_right.svg" alt="" srcset="">
            </div>
            <div>
                <div class="startTime">截止日期</div>
                <div class="timeValue" :class="formline.endTime ? 'activeTime' : ''">{{ formline.endTime || '选择日期' }}
                </div>
            </div>
        </div>

        <van-search class="search" background="#FBFCFF" v-model="searchName" @click="popoverIsShow = true" readonly
            placeholder="请选择查询对象">
        </van-search>
        <staffList v-model:searchStaffName="searchName" v-model:popoverIsShow="popoverIsShow"
            @confirmStaffCode="confirmStaffCode"></staffList>


        <van-button type="primary" @click="viewDebriefingDiary" size="large" class="searchButtomn"
            color="linear-gradient( 270deg, #73ACFF 0%, #237CFF 100%)" block>查询</van-button>

        <Teleport to="body">
            <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" first-day-of-week="1"
                switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false" :max-date="maxDate"
                @confirm="onConfirm" />
        </Teleport>


    </div>
</template>

<script setup lang="ts">
import staffList from "@/components/staffList/index.vue";
import { useRouter } from "vue-router";
import { searchHooks } from "@/hooks_modules/changeTimeHooks.ts";
import { useUserInfo } from "@/stores/userInfo";
const userInfoStore = useUserInfo()
const router = useRouter()
const curTitle = computed(() => {
    return userInfoStore.userInfo.orgName
})
function viewDebriefingDiary() {

    let staffCode = formline.staffCode[0]
    if (searchName.value == '' || formline.staffCode.length == 0 || formline.staffCode[0] == '全部') {
        staffCode = ''
    } else {
        staffCode =  formline.staffCode[0]
    }


    router.push({
        path: '/debriefingDiary', query: {
            ...formline,
            staffCode: staffCode,
        }
    })
}

const {
    formline,
    defaultDate,
    maxDate,
    calendarIsShow,
    curSelect,
    selectOptionList,
    searchName,
    listData,
    popoverIsShow,
    changeSelete,
    selectTime,
    onConfirm,
    confirmStaffCode,
} = searchHooks()


onMounted(() => {

})

</script>

<style lang="scss" src="../index.scss" scoped></style>