<template>
  <div class='mt-16 container_' v-loading="loadingConfig">
    <div class="container__header">
      <div>
        <img src="./assets/development.svg" alt="" srcset="">
        <span class="ml-6">发展情况</span>
      </div>
      <cusTabs :tabList v-model:curSelectTab="curSelectTab" @changeSelectTab="changeSelectTab"></cusTabs>

    </div>

    <div class=" container_body ">
      <myCharts class="myEcharts" :options="options"></myCharts>
    </div>
  </div>
</template>

<script setup lang='ts'>
import myCharts from "@/components/charts/myCharts.vue";
import { ref, reactive, onMounted, onUnmounted, computed, toRefs } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { getDevelopmentAPI } from "@/api/userData";
import cusTabs from "@/components/cusTabs/index.vue";


type Type = {
  value: number | string,
  name: string,
  props: string
}
const loadingConfig = reactive<LoadingDirectiveOptions>({
  visible: false, // 控制加载状态
  text: '请稍等...', // 自定义加载文本
  iconSize: '36',
  iconColor: '#fff',
  borderRadius: '10px',
});

const tableData = ref<Type[]>([


  {
    value: 25,
    name: '积分',
    props: 'points',
  },
  {
    value: 20,
    name: '天翼',
    props: 'tianYi',
  },
  {
    value: 16,
    name: '宽带',
    props: 'kuanDai',
  },
  {
    value: 13,
    name: '融合',
    props: 'ronghe',
  },
  {
    value: 10,
    name: '新装高套',
    props: 'xinliangZgz',
  },
  {
    value: 0,
    name: '商机',
    props: 'businessRecords',
  },
]
)

const options = computed(() => {


  const color = ['#6282FD', '#00AFA3', '#0083FD', '#000DFF', '#4DE7FF','#3FB4EF']
  const option = {
    color: color,
    title: {
      show: true,
      text: '今日发展情况',
      x: '8%',
      y: '85%',
      textStyle: { //主标题文本样式{"fontSize": 18,"fontWeight": "bolder","color": "#333"}
        fontSize: 14,
        color: '#3D3D3D'
      },

    },
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: 'vertical',
      itemWidth: 16,
      itemHeight: 16,
      icon: 'rect',
      right: 10,
      bottom: '10%',
      data: tableData.value,
      textStyle: {
        fontSize: 14,
        padding: [10, 0, 10, 15],
        rich: {
          color0: { color: color[0], fontSize: 14 }, // For first label
          color1: { color: color[1], fontSize: 14 }, // For second label
          color2: { color: color[2], fontSize: 14 }, // For third label
          color3: { color: color[3], fontSize: 14 }, // For fourth label
          color4: { color: color[4], fontSize: 14 }, // For fourth label
          color5: { color: color[5], fontSize: 14 }, // For fourth label

        },
      },
      formatter: function (params: any) {
        const index = tableData.value.findIndex(item => item.name === params)
        const value = tableData.value[index].value
        return `{color${index}|${params}: ${value}}`;
      },


    },
    series: [
      // 展示层
      {
        type: 'pie',
        center: ['20%', '50%'],

        radius: ['0%', '100%'],
        roseType: 'area',
        label: {
          show: false,
        },

        data: tableData.value
      },
    ],
  }

  return option
})





//当前 默认选择 当日
const curSelectTab = ref(1)
// tab 列表
const tabList = ref([
  { text: '当日', value: 0 },
  { text: '当月', value: 1 },

])

function changeSelectTab() {
  loadDevelopmentMiner()
}

function loadDevelopmentMiner() {
  loadingConfig.visible = true
  getDevelopmentAPI({ type: curSelectTab.value }).then(res => {
    for (const item of tableData.value) {
      item.value = res.data[0]?.[item.props] ?? 0

    }
  }).finally(() => {
    loadingConfig.visible = false

  })

}






defineExpose({

});


// 生命周期钩子
onMounted(() => {
  loadDevelopmentMiner()
});

onUnmounted(() => {
  console.log('Component unmounted');
});
</script>

<style lang='scss' scoped>
.container_body {
  padding: 15px;
}

.myEcharts {

  width: 100%;
  height: 206px;
  padding: 10px 20px;
}
</style>