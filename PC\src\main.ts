import { createApp } from "vue";
import pinia from "./stores";
import router from "./router";
import App from "./App.vue";
import "@/styles/index.scss";
import 'normalize.css/normalize.css'
import directive from './directive'
// import ElementPlus from "element-plus";
import "./router/permission";
import "element-plus/dist/index.css";
import iconfontSvg from "@/components/iconfont/iconfont.vue";

import * as ElementPlusIconsVue from "@element-plus/icons-vue";

const app = createApp(App);

app.component("svg-icon", iconfontSvg)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
}
app.use(pinia);
app.use(directive)
// app.use(router).use(ElementPlus).mount("#app");
app.use(router).mount("#app");

