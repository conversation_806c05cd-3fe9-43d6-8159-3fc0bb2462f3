{"name": "net-portal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"serve": "vite", "dev": "vite --mode test", "start": "vite --mode production", "build": "vite build  --mode production && node ./obfuscator.js", "build:test": "vite build --mode test && node ./obfuscator.js", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@types/lodash-es": "^4.17.12", "@vueuse/core": "^13.1.0", "axios": "^1.6.2", "crypto-js": "^4.2.0", "echarts": "^5.4.3", "element-plus": "^2.5.1", "fs": "^0.0.1-security", "javascript-obfuscator": "^4.1.1", "js-cookie": "^3.0.5", "json-stable-stringify": "^1.2.1", "lodash-es": "^4.17.21", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^7.1.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "qs": "^6.11.2", "rollup-plugin-obfuscator": "^1.1.0", "terser": "^5.40.0", "vite-plugin-commonjs": "^0.10.1", "vite-plugin-require-transform": "^1.0.21", "vue": "^3.4.27", "vue-router": "^4.2.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^20.10.4", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.2", "@vitejs/plugin-vue": "^4.5.0", "sass": "^1.86.3", "typescript": "^5.2.2", "unplugin-auto-import": "^0.19.0", "unplugin-vue-components": "^0.28.0", "vite": "^5.0.0", "vue-tsc": "^1.8.22"}}