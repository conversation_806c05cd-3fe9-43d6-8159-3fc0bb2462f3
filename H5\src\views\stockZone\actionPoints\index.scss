.card-header {
    height: 46px;
    padding: 2px 5px;
    background: #FBFCFF;
    border-radius: 10px 10px 10px 10px;
    box-shadow: 0px 4px 10px 0px rgba(103, 159, 255, 0.15), inset 0px 2px 0px 0px #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &>div:first-child {
        display: flex;
        align-items: center;
        font-weight: 400;
        font-size: 14px;
        color: #3D3D3D;
        line-height: 18px;

        img {
            margin-right: 8px;
        }

    }

    .selectOption-item {
        flex: 1 0 auto;
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
            position: absolute;
            right: 10px;
        }
    }

    .selectOption-item:active {
        background: #d8dee6;
    }
}

.table-container {
    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 20px;
        padding: 0px 15px;
        .title {
            font-weight: 600;
            font-size: 16px;
            color: #3D3D3D;
        }
    }
}

.myEcharts {
    width: 100%;
    height: 300px;
}

