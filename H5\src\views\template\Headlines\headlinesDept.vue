<template>
    <div class='Headlines container_'>
        <div class="container__header">
            <div>
                <img src="./assets/headline.svg" alt="" srcset="">
                <span class="ml-6">今日头条</span>
            </div>
            <div style="display: flex;align-items: center;justify-content: space-between;">
                <van-popover :actions="actionsList" @select="changeSelete" placement="bottom">
                    <template #reference>
                        <div class="selectOption-item">
                            <span>{{ curSelectTime.text }}</span>
                            <van-icon name="play" class="play" color="#1A76FF" />
                        </div>
                    </template>
                </van-popover>
                <div class="ml-15 timeSection" @click="calendarIsShow = true">

                    <span>{{ timeSection }}</span>
                    <van-icon name="play" class="play" color="#1A76FF" />
                </div>
            </div>
        </div>

        <div class="container_body">
            <div style="display: grid;grid-template-columns: 1fr 1fr;">
                <div class="popover">
                    <van-popover placement="bottom-end">
                        <div class="popoverBody">
                            <li>派单总量：所有框架下的所有单量</li>
                            <li>已执行量：外呼时长大于0的数量</li>
                            <li>外呼量：外呼时长大于30秒的数量</li>
                            <li>待处理量：派单量-已执行量</li>
                            <li>以上数据均来自智慧营销，最终口径以业务为准</li>

                        </div>
                        <template #reference>
                            <span>积分完成率</span>
                            <van-icon class="ml-3" name="question" color="#1A76FF" />
                        </template>
                    </van-popover>
                </div>


                <myCharts class="myEcharts" :options="options"></myCharts>

                <div class="tabs">
                    <div class="tab-item" v-for="item in tableData" :key="item.name" @click="legendselectchanged(item)">
                        <div class="circle mr-5" :style="{
                            backgroundColor: item.color
                        }"></div>
                        <div class="name">{{ item.name }}:</div>
                        <div class="value">{{ item.rate }}% <i :style="{
                            color: item.color
                        }" class=" icon-jiantou_xiangyouliangci"></i>
                        </div>
                    </div>
                </div>

            </div>

        </div>


        <van-calendar v-model:show="calendarIsShow" :default-date="defaultDate" :min-date="minDate"
            first-day-of-week="1" switch-mode="year-month" :allow-same-day="true" type="range" :show-confirm="false"
            :max-date="maxDate" @confirm="onConfirm" />

    </div>
</template>

<script setup lang='ts'>
import myCharts from "@/components/charts/myCharts.vue";
import { useRouter } from "vue-router";
import { areaAndDepthooks } from "./hooks/areaAndDepthooks.ts";

const router = useRouter();
const { options, tableData,
    formline,
    defaultDate,
    maxDate,
    minDate,
    calendarIsShow,
    actionsList,
    curSelectTime,
    timeSection,
    changeSelete,
    onConfirm,

} = areaAndDepthooks();



function legendselectchanged(params: any) {
    let type: number
    switch (params.name) {
        case '预警':
            type = 0
            break;
        case '良好':
            type = 1
            break;
        case '优秀':
            type = 2
            break;
        default:
            type = 3
            break;
    }

    router.push({
        path: '/headlinesDetailDept',
        query: {
            type: type,
            startTime: formline.startTime ? `${formline.startTime} 00:00:00` : '',
            endTime: formline.endTime ? `${formline.endTime} 23:59:59` : '',
        }
    })
}




</script>

<style lang="scss" scoped src="./deptAndArea.scss"></style>