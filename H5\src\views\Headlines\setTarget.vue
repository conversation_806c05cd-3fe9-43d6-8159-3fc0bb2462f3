<template>
    <div class="card container_">

        <van-sticky>
            <div class="container__header" ref="container__header">
                <div>
                    <van-icon name="arrow-left" size="4.5vw" class="mr-5" color="#428eff" @click="router.go(-1)" />
                    <img src="@/assets/resultOptimization.svg" alt="" srcset="">
                    <span class="ml-6" style="font-weight: 500;">月指标分配</span>
                </div>
                <div class="selectOption">
                    <van-popover v-model:show="showPopover" :actions="actions" @select="onSelect"
                        placement="bottom-end">
                        <template #reference>
                            <div class="selectOption-item">
                                <span>{{ curSelectText }}</span>
                                <van-icon name="play" class="play" color="#1A76FF" />
                            </div>
                        </template>
                    </van-popover>
                </div>
            </div>
        </van-sticky>

        <div class="container_body">
            <van-sticky :offset-top="height">
                <div style="background-color: #fbfcff;" class="pb-5">
                    <van-popover :actions="roleList" @select="changeRole" placement="bottom-end">
                        <template #reference>
                            <van-search v-model="searchName" class="searchInput" placeholder="请选择查询对象/条线/工号">
                                <template #left-icon>
                                    <img class="mt-5" src="@/assets/search.svg" alt="" srcset="">
                                </template>
                            </van-search>
                        </template>
                    </van-popover>


                    <div class="surplusTarget" v-if="!isGov">
                        <span>当前剩余指标：</span>
                        <span>{{ residualNum }}</span>
                    </div>

                </div>




            </van-sticky>

            <div class="optionList">
                <div class="list-item" v-for="item in staffList_" :key="item.id">
                    <img src="@/assets/defaultAvatar.png" alt="">
                    <div class="ml-6">
                        <div class="name">
                            <span v-getName="{ item: item }">{{ item.staffName }}</span>
                            <span class="fs10 ml-3">{{ item.staffCode }} </span>
                        </div>
                        <div class="staffRole">{{ item.staffRole || item.primaryRoleName }}</div>
                    </div>
                    <van-stepper min="0" v-model="item[curSelectValue]" />
                </div>

            </div>

            <van-button @click="confirm" :loading="loading" type="primary" size="large" class="searchButtomn"
                color="linear-gradient( 270deg, #73ACFF 0%, #237CFF 100%)" block>提交</van-button>
        </div>





    </div>
</template>

<script setup lang="ts">
import { PopoverAction } from "vant";
import { debounce } from "@/utils/debounce";
import { showToast, showSuccessToast, showFailToast } from "vant";
import { setTargetStaffAPI, getsetTargetStaffListAPI, getsetTargetStaffListGovAPI, setTargetStaffGovAPI } from "@/api/home";
import { useRouter } from "vue-router";
import { useElementBounding } from '@vueuse/core'
import { mapSort } from "@/utils/mapSort";
import { getlistStaffRole } from "@/hooks_modules/getlistStaffRole";


interface List {
    id: string,
    staffName: string,
    staffCode: string,
    points: number,
    ronghe: number,
    xinliangZgz: number

    gov5g: number;
    govCloudComputer: number;
    govFttrb: number;
    govHuZhuan: number;
    govShangZhuan: number;
    govTianyi: number;
    govTianyiShilian: number;
    govXiaowei: number;


    [key: string]: any
}
type Residual = {
    areaPoints: number,
    areaRonghe: number,
    areaXinliangZgz: number,



    staffList: List[]
    [key: string]: any
}
const { roleList } = getlistStaffRole()

const container__header = ref()
const { height } = useElementBounding(container__header)
const router = useRouter()
const route = useRoute()


//是否是政企部门
const isGov = computed(() => {
    return route.query.departmentType ? route.query.departmentType == 'gov' ? true : false : false;
})




const showPopover = ref(false);
const actions = computed(() => {
    if (isGov.value) {
        return [
            { text: '传统天翼', value: 'govTianyi', props: 'govTianyi', type: 0 },
            { text: 'FTTR-B', value: 'govFttrb', props: 'govFttrb', type: 1 },
            { text: '互专', value: 'govHuZhuan', props: 'govHuZhuan', type: 2 },
            { text: '商专', value: 'govShangZhuan', props: 'govShangZhuan', type: 3 },
            { text: '政企积分', value: 'govQudaoJifen', props: 'govQudaoJifen', type: 4 },
            { text: '天翼视联', value: 'govTianyiShilian', props: 'govTianyiShilian', type: 5 },
            // { text: '小微', value: 'govXiaowei', props: 'areaPoints', type: 6 },
            { text: '5G主卡', value: 'gov5g', props: 'gov5g', type: 7 },
        ]
    } else {
        return [
            { text: '积分', value: 'points', props: 'areaPoints', type: 0 },
            { text: '融合', value: 'ronghe', props: 'areaRonghe', type: 1 },
            { text: '新装高套', value: 'xinliangZgz', props: 'areaXinliangZgz', type: 2 },
        ]
    }
})



const curSelectText = ref<string>('积分')
const curSelectValue = ref<string>('points')
const curSelectProps = ref<string>('areaPoints')
const curSelectType = ref(0)


watch(isGov, (newValue) => {
    if (newValue) {
        curSelectText.value = '传统天翼'
        curSelectValue.value = 'govTianyi'
    } else {
        curSelectText.value = '积分'
        curSelectValue.value = 'points'

    }

}, { immediate: true })


const searchName = ref('')

function changeRole(item: any) {
    searchName.value = item.text == '全部' ? '' : item.text
}

const residual = reactive<Residual>({
    areaPoints: 0,
    areaRonghe: 0,
    areaXinliangZgz: 0,
    staffList: []

})

const staffList_: ComputedRef<List[]> = computed(() => {
    const input = searchName.value.toLowerCase();

    return residual.staffList.filter(item => {
        const staffName = item.staffName ? item.staffName.toLowerCase() : '';
        const staffRoleName = item.staffRole ? item.staffRole.toLowerCase() : '';
        const staffCode = item.staffCode ? item.staffCode.toLowerCase() : '';
        return (
            staffName.includes(input) ||
            staffRoleName.includes(input) ||
            staffCode.includes(input)
        );
    });
})
// 剩余指标
const residualNum = computed(() => {
    if (isGov.value) {
        return 0
    } else {
        let number = residual[curSelectProps.value]
        let listNumber = residual.staffList.reduce((pre: number, cur: List) => {
            return pre + Number(cur[curSelectValue.value])
        }, 0)
        return number - listNumber
    }

})


function onSelect(action: PopoverAction) {
    if (curSelectText.value == action.text) return
    if (isGov.value) {
        // 政企不计算剩余数量 不需要curSelectProps
    } else {
        curSelectProps.value = action.props
    }
    curSelectValue.value = action.value
    curSelectText.value = action.text
    curSelectType.value = action.type

}
function loadsetTargetStaffList() {
    if (isGov.value) {
        getsetTargetStaffListGovAPI().then((res: any) => {
            if (res.code == 200) {
                residual.staffList = mapSort(res.data, 'primaryRoleName')
            }
        })
    } else {
        getsetTargetStaffListAPI({}).then((res: any) => {
            if (res.code == 200) {
                residual.areaPoints = res.data.areaPoints
                residual.areaRonghe = res.data.areaRonghe
                residual.areaXinliangZgz = res.data.areaXinliangZgz
                residual.staffList = mapSort(res.data.nspDevelopGoalList, 'staffRole')
            }
        })
    }



}

const loading = ref(false)
//提交
const confirm = debounce(() => {
    let params = {
        type: curSelectType.value,
        detail: loadDetail().map((item: List) => {
            if (isGov.value) {
                return {
                    staffCode: item.staffCode,
                    gov5g: item.gov5g,
                    govQudaoJifen: item.govQudaoJifen,
                    govFttrb: item.govFttrb,
                    govHuZhuan: item.govHuZhuan,
                    govShangZhuan: item.govShangZhuan,
                    govTianyi: item.govTianyi,
                    govTianyiShilian: item.govTianyiShilian,
                    // govXiaowei: item.govXiaowei,
                }
            } else {
                return {
                    staffCode: item.staffCode,
                    points: item.points,
                    ronghe: item.ronghe,
                    xinliangZgz: item.xinliangZgz
                }
            }
        })
    }
    loading.value = true
    const API = isGov.value ? setTargetStaffGovAPI : setTargetStaffAPI

    API(params).then((res: any) => {
        if (res.code == 200) {
            showSuccessToast("提交成功");
            loadsetTargetStaffList()
        } else {
            showFailToast(res.msg);
        }
    }).finally(() => {
        loading.value = false
    })
}, 300, true)

function loadDetail(): List[] {
    for (const item of staffList_.value) {
        const index = residual.staffList.findIndex(el => el.staffCode == item.staffCode)

        residual.staffList[index] = item
    }

    return residual.staffList
}
onMounted(() => {
    loadsetTargetStaffList()
})
</script>



<style lang="scss" scoped>
.card {
    font-family: 'Source Han Sans, Source Han Sans';
    height: 100%;
    background: #fbfcff;
    overflow-y: scroll;
    overflow-x: hidden;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px 10px 0px 0px;


}

.container__header {
    background-color: #fbfcff;
}

.container_body {
    // position: relative;
    padding-bottom: 70px;
}

.surplusTarget {
    display: flex;
    justify-content: space-between;
    margin: 10px 0px;
    font-weight: 500;
    font-size: 12px;
    color: #3D3D3D;
}




.selectOption {
    display: flex;
    align-items: center;

    & .selectOption-item {
        background: #EDF5FF;
        border-radius: 5px 5px 5px 5px;
        font-weight: 400;
        font-size: 14px;
        color: #1A76FF;
        padding: 8px;
        display: flex;
        align-items: center;

        .play {
            transform: rotate(90deg);
            margin-left: 3px;
        }
    }

    & .selectOption-item:active {
        background: #d8dee6;
    }



}


.optionList {

    .list-item {
        display: grid;
        grid-template-columns: 43px 1fr max-content;
        align-items: center;
        border-bottom: 1px solid #EEF5FF;
        padding: 16px 0px;

        img {
            width: 43px;
            height: 43px;

        }

        .name {
            font-weight: 500;
            font-size: 14px;
            color: #3D3D3D;
        }

        .staffRole {
            font-weight: 400;
            margin-top: 3px;
            font-size: 12px;
            color: #999999;
        }
    }

}

.searchButtomn {
    border-radius: 10px;
    width: 90%;
    position: fixed;
    left: 50%;
    transform: translateX(-50%);
    bottom: 80px;
    font-weight: 500;
    font-size: 18px;
    color: #FFFFFF;

}

.searchInput {
    // width: 70vw;
}

:deep(.van-search) {
    padding: 0px;
}

:deep(.van-search__action) {
    line-height: 0;
}

:deep(.van-search__content) {
    background-color: #EDF5FF;
    border-radius: 5px;
}

:deep(.van-field__control::placeholder) {
    color: #72ABFF;
}

:deep(.van-field__control) {
    color: #1a76ff;
}

:deep(.van-search__field) {
    height: 44px;
}

:deep(.van-icon-search) {
    color: #1a76ff;
}

:deep(.van-stepper__minus, .van-stepper__minus, ) {
    // background-color: #ffffff;
    border-radius: 50%;
}

:deep(.van-stepper__minus) {
    border-radius: 50%;
    border: 1px solid #FF6C1D;
    color: #FF6C1D;
    background-color: #ffffff;
}

:deep(.van-stepper__plus) {
    border-radius: 50%;
    border: 1px solid #1D78FF;
    color: #1D78FF;
    background-color: #ffffff;
}

:deep(.van-stepper__input) {
    margin: 0px 10px;
    background-color: #fff;
    border: 1px solid #D8D8D8;
    border-radius: 5px;
    width: 60px;
    height: 32px;
}

:deep(.van-popover__wrapper) {
    display: block;
}

:deep(.van-popover__content) {
    overflow-y: auto;
    max-height: calc(5.2 * var(--van-popover-action-height));
}

:deep(.van-popover__action) {
    width: auto;
}
</style>