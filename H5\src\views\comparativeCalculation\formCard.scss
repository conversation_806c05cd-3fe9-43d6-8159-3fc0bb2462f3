.card {
  min-height: 100%;
  background: #fff;
  box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
  border-radius: 10px 10px 0px 0px;
  position: relative;
}

.form_card {
  height: 80%;
  overflow: scroll;

  ::v-deep .van-field {
    border-bottom: 1px solid #edf5ff;
  }
}

.form_card_camera {
  width: 89px;
  height: 89px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(103, 159, 255, 0.15);
}

.sure_btn {
  // position: relative;
  // left: 50%;
  // transform: translateX(-50%);
  // bottom: 100px;
  margin: 0px auto;
  margin-top: 30px;
  width: calc(100% - 18px - 18px);
}

.sign_list {
  font-family: Source <PERSON>, Source <PERSON>;
  font-weight: 400;
  font-size: 12px;
  color: #1a76ff;
  line-height: 13px;
  font-style: normal;
  text-transform: none;
}

.right_align {
  text-align: right;

  ::v-deep .van-uploader__wrapper {
    justify-content: flex-end !important;
  }
}

.card_icon {
  object-fit: contain;
}