$menuText: #bfcbd9;
$menuActiveText: #e9f4ff;
$subMenuActiveText: #f4f4f5; //https://github.com/ElemeFE/element/issues/12951

$menuBg: #304156;

$menuHover: #263445;
$subMenuBg: #1f2d3d;
$subMenuHover: #001528;

$sideBarWidth: 210px;

// $menuBg: #fff;
// $menuHover: #e9f4ff;
// $subMenuBg: #fff;
// $subMenuHover: #e9f4ff;
// $menuText: #606266;
// $menuActiveText: #409eff;
// $subMenuActiveText: #409eff;
// $menuShadow: rgba(50, 50, 93, 0.25) 0px 50px 100px -20px,
//   rgba(0, 0, 0, 0.3) 0px 30px 60px -30px,
//   rgba(10, 37, 64, 0.35) 0px -2px 6px 0px inset;

$menuBg: #2e7060;
$menuHover: #e9f4ff59;
$subMenuBg: #2e7060;
$subMenuHover: #e9f4ff59;

$menuText: #606266;

$menuActiveText: #13ede6;
$subMenuActiveText: #13ede6;
$menuShadow: rgba(50, 50, 93, 0.25) 0px 50px 100px -20px,
  rgba(0, 0, 0, 0.3) 0px 30px 60px -30px,
  rgba(10, 37, 64, 0.35) 0px -2px 6px 0px inset;


:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
