import { getNameByStaffCodeAPI } from "@/api/common";
import { Decrypt } from "@/utils/crypto/index";
import { debounce } from "@/utils/debounce";
const directives = {
    install: ((app: any) => {
        app.directive('getName', (el: any, binding: any) => {
            el.onclick = debounce(() => {
                const { item, props } = binding.value
                if(!item.staffCode) return  //staffCode存在才继续下一步
                
                if (props) {
                    //为了解决循环出来的列表点击那一行都会触发的问题
                    if (props == 'staffName') {
                        changeInnerText(el, item)
                    } else {
                        return
                    }
                } else {
                    changeInnerText(el, item)
                }
            }, 300, true)
        });
    })
}


function changeInnerText(el: HTMLElement, item: any) {

    getNameByStaffCodeAPI({ staffCode: item.staffCode }).then((res: any) => {
        if (res.code == 200) {
            el.innerText = Decrypt(res.data.staffName)
        }

    })


}

export default directives;
