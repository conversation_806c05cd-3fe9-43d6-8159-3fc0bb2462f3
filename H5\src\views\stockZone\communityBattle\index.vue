<template>
    <div class='card container_'>
        <div class="container__header" style="padding-top: 5px;padding-bottom: 0px;">
            <div>
                <img src="./assets/communityBattle.png" alt="" srcset="">
                <span class="ml-6">小区攻防</span>
            </div>
            <cusTabs :tabList="tabList" v-model:curSelectTab="curSelectType" @changeSelectTab="changeSelectTab">
            </cusTabs>
        </div>




        <div class=" container_body ">
            <div class="box_content">
                <div class="box_content_header">
                    <span class="title">{{ orgName }}</span>
                </div>
                <div class="imgList">
                    <router-link to="/promoShots">
                        <img src="./assets/lr.png" alt="" srcset="">
                    </router-link>
                    <router-link to="/inspectionLogGridList">
                        <img src="./assets/jl.png" alt="" srcset="">
                    </router-link>

                </div>

            </div>




        </div>

        <div class="container_body " style="padding-top: 0px;padding-bottom: 0px; " v-if="isShow">
            <div class="box_content">
                <div class="body_box">
                    <div class="box-item">
                        <div style="color: #3A83FC;">{{ gridManagementDetails.totalGrids }}</div>
                        <div>督办网格数</div>
                    </div>
                    <div class="box-item">
                        <div style="color: #34C759;">{{ gridManagementDetails.feedbackCount }}</div>
                        <div>已检查网格</div>
                    </div>
                    <div class="box-item">
                        <div style="color: #FF9760;">{{ gridManagementDetails.noFeedbackCount }}</div>
                        <div>未检查网格</div>
                    </div>
                </div>
                <div class="mt-18">
                    <div class=" mb-2 fs12">反馈进度:</div>
                    <div style="display: flex;align-items: center;">
                        <van-progress style="flex: 1 0 auto;" track-color="#F4F4F4" :show-pivot="false" stroke-width="6"
                            color="#1A76FF" :percentage="gridManagementDetails.feedbackProgress" />
                        <div style="color: #3A83FC;" class="ml-10 fs12">{{ gridManagementDetails.feedbackProgress }}%
                        </div>
                    </div>
                </div>
            </div>





        </div>


        <div class=" container_body">
            <div class="box_content pb-35">
                <div style="display: flex;align-items: center;justify-content: space-between;">
                    <div class="font-weight-bolder">网格类型分布</div>
                    <div class="fs12" style="color:#757581 ;letter-spacing: 2px;">共{{ list.length }}种</div>
                </div>

                <div class="row" v-for="item in list" :key="item.gridTypeName" @click="gotoDetail(item)">
                    <span>
                        <i :class="[item.icon, `fs${item.fs}`]" style="color: #3566E3;"></i>
                        <span>{{ item.gridTypeName }}</span>
                    </span>
                    <span>
                        <span>{{ item.quantity }}个</span>
                        <van-icon name="arrow" color="#9DAAB4" />
                    </span>
                </div>
                <van-empty description="暂无数据" v-if="list.length == 0" />
            </div>


        </div>









    </div>
</template>

<script setup lang="ts">

interface GridManagementDetails {
    totalGrids: number; // 暂办网格总数
    feedbackCount: number; // 有反馈数
    noFeedbackCount: number; // 无反馈数
    feedbackProgress: number; // 反馈进度，百分比
}

import cusTabs from "@/components/cusTabs/index.vue";
import { getGridTypeCountAPI, getAreaL2DetailAPI } from "@/api/promoShots";
import { useRouter } from "vue-router";
import { useUserInfo } from "@/stores/userInfo";

const userInfoStore = useUserInfo()
const orgName = userInfoStore.userInfo.orgName
const router = useRouter()







const tabList = ref([
    { text: '检查宣传', value: 0 },
    // { text: '未交付小区', value: 1 },
])


const curSelectType = ref<number>(0)   //1  检查宣传  //     0  未交付小区    
function changeSelectTab(params: any) {
    console.log(params);
}





const list = ref([
    { gridTypeName: "MALL", icon: "icon-localmall", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "专业市场", icon: "icon-surface_calender_text", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "中小幼", icon: "icon-zhongxiaoyoujiaoyujigou", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "产业园区", icon: "icon-chanyeyuanqu", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "党政军", icon: "icon-dangzhengjiguan", quantity: 0, fs: 18, isShow: false },
    { gridTypeName: "公共设施", icon: "icon-gonggongsheshi", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "公园", icon: "icon-gongyuan_gongyuan", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "其他", icon: "icon-qita", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "医院", icon: "icon-yiyuan", quantity: 0, fs: 18, isShow: false },
    { gridTypeName: "单位", icon: "icon-danwei", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "厂区", icon: "icon-changqu", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "商业区", icon: "icon-shangyequ", quantity: 0, fs: 18, isShow: false },
    { gridTypeName: "商业街区", icon: "icon-buildings", quantity: 0, fs: 18, isShow: false },
    { gridTypeName: "商住两用", icon: "icon-shangzhuliangyong", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "商务楼宇", icon: "icon-BusinessBuilding", quantity: 0, fs: 21, isShow: false },
    { gridTypeName: "城中村", icon: "icon-chengzhongcun", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "城市公寓楼", icon: "icon-chengshigongyu", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "城市别墅小区", icon: "icon-fangchan", quantity: 0, fs: 19, isShow: false },
    { gridTypeName: "城市居民小区", icon: "icon-jumin", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "大中专院校", icon: "icon-xuexiqiangguo", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "已拆迁小区", icon: "icon-dizhen", quantity: 0, fs: 21, isShow: false },
    { gridTypeName: "承包区域默认网格", icon: "icon-kongdi", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "政企单位地块", icon: "icon-qiweiguanli-01", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "新农村小区", icon: "icon-xinnongcun", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "新建小区", icon: "icon-Dxinjianxiaoqudaiweihu", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "未交付新小区", icon: "icon-shou", quantity: 0, fs: 22, isShow: false },
    { gridTypeName: "汽车服务", icon: "icon-fullCarLacquer-b", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "空地", icon: "icon-kongdi", quantity: 0, fs: 17, isShow: false },
    { gridTypeName: "自然村", icon: "icon-zirancun", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "行政村", icon: "icon-hangzhengcun", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "超市", icon: "icon-fuwuchaoshi", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "逻辑网格", icon: "icon-lianluzhuizong-shuangse", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "逻辑网格（待认领）", icon: "icon-dairenling", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "酒店", icon: "icon-guojijiudian", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "集宿区", icon: "icon-jisuqu", quantity: 0, fs: 20, isShow: false },
    { gridTypeName: "集镇居民小区", icon: "icon-chengjizhenjumin", quantity: 0, fs: 17, isShow: false },
    { gridTypeName: "集镇散户区", icon: "icon-sanhu", quantity: 0, fs: 20, isShow: false }
]
)




function loadGridTypeCount() {
    getGridTypeCountAPI().then((res: any) => {
        if (res.code == 200) {
            const obj = res.data.reduce((arr: any, item: any) => {
                arr[item.gridTypeName] = item.qty
                return arr
            }, {})

            let objKeyList = Object.keys(obj)
            for (const item of list.value) {
                item.quantity = obj[item.gridTypeName] ?? 0
                item.isShow = objKeyList.includes(item.gridTypeName) ? true : false
            }
            list.value.sort((a: any, b: any) => {
                return b.quantity - a.quantity
            })
            list.value = list.value.filter(item => item.isShow && item.quantity != 0)


        }
    })
}

const isShow = ref(false)

const gridManagementDetails = reactive<GridManagementDetails>({
    totalGrids: 0,
    feedbackCount: 0,
    noFeedbackCount: 0,
    feedbackProgress: 0, // 进度以百分比表示
});


function loadAreaL2Detail() {
    getAreaL2DetailAPI().then((res: any) => {
        if (res.code == 200) {
            isShow.value = true
            gridManagementDetails.totalGrids = res.data.gridTotal
            gridManagementDetails.feedbackCount = res.data.reactionNum
            gridManagementDetails.noFeedbackCount = res.data.unReactionNum
            gridManagementDetails.feedbackProgress = res.data.reactionRate
        }
    }).catch(error => {
        isShow.value = false
    })
}


function gotoDetail(row: any) {
    router.push({
        path: '/communityDetailList',
        query: {
            gridTypeName: row.gridTypeName
        }

    })
}



onMounted(() => {
    loadGridTypeCount()
    loadAreaL2Detail()
})

</script>

<style lang="scss" scoped>
.card {
    min-height: 100%;
    background: #fff;
    box-shadow: inset 0px 2px 0px 0px #ffffff, 0px 4px 10px 0px rgba(103, 159, 255, 0.15);
    border-radius: 10px 10px 0px 0px;
}




.body_box {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .box-item {
        text-align: center;

        &>div:last-child {
            font-weight: 400;
            font-size: 12px;
        }

        &>div:first-child {
            font-weight: 500;
            font-size: 24px;
            line-height: 27px;
        }
    }
}





.box_content {
    background: #EDF5FF;
    border-radius: 10px 10px 10px 10px;
    padding: 10px;
    position: relative;

    .box_content_header {
        font-weight: 400;
        font-size: 13px;
        color: #3D3D3D;
        display: flex;
        justify-content: space-between;

        align-items: center;

        .title {
            font-weight: 500;
            font-size: 16px;
            color: #3D3D3D;
        }
    }

    .imgList {
        display: flex;
        align-items: center;
        justify-content: space-between;

        &>a {
            width: 45%;
        }

        img {
            width: 100%;
            cursor: pointer;
        }
    }



    .row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 15px 15px;
        border-radius: 10px;
        background-color: #ffffff99;
        margin: 10px 0px;

        &>span {
            display: flex;
            align-items: center;
        }

        &>span:first-child {
            :last-child {
                font-weight: 500;
                font-size: 14px;
                margin-left: 5px;
            }
        }

        &>span:last-child {

            :first-child {
                font-weight: 500;
                font-size: 14px;
                margin-right: 5px;
                color: #3566E3;
            }
        }
    }

    .row:active {
        background-color: #7e82992e;
    }

}
</style>