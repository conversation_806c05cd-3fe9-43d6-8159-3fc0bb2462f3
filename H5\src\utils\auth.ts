import Cookies from "js-cookie";
const CryptoJS = require("crypto-js");
let hostname = location.hostname;
const TokenKey = "DMC_ENDToken" + hostname;
// const AccessKey = "access_token";
// const RefreshKey = "refresh_token";
// const firstKey = "is_verified";
// const orderKey = "order_data";
// const errorKey = "error";

export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token:string) {
  /* 10秒过期 */
  // let seconds = 10;
  // let expires = new Date(new Date() * 1 + seconds * 1000);
  // localStorage.setItem(TokenKey, token);
  // return Cookies.set(Token<PERSON>ey, token, { expires: expires });

  /* 7天过期 */
  // localStorage.setItem(TokenKey, token);
  return Cookies.set(TokenKey, token, { expires: 1, path: "" });
}





/**
 * CryptoJS 加密
 *
 * @param {String} encryptData  需要加密数据
 * @returns 加密后的数据
 * @memberof Utils
 */
export const encrypt = (encryptData:string) => {
  var key = CryptoJS.enc.Utf8.parse('as-Crypto-js')
  var srcs = CryptoJS.enc.Utf8.parse(encryptData)
  var encrypted = CryptoJS.AES.encrypt(srcs, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  })
  return encrypted.toString()
}


/**
 * CryptoJS 解密
 *
 * @param {String} encryptData  需要加密数据
 * @returns 解密后的数据
 * @memberof Utils
 */
export const decrypt = (encryptData:string) => {
  var key = CryptoJS.enc.Utf8.parse('as-Crypto-js')
  var decrypt = CryptoJS.AES.decrypt(encryptData, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  })
  return CryptoJS.enc.Utf8.stringify(decrypt).toString()
}

export function setRememberPasswords(value:string) {
  return Cookies.set('remember', encrypt(value), { expires: 7, path: "" });
}
export function getRememberPasswords() {
  return Cookies.get('remember')
}
export function removeRememberPasswords() {
  return Cookies.remove('remember');
}





export function removeToken() {
  // localStorage.removeItem(TokenKey);
  return Cookies.remove(TokenKey);
}

