import type { App, Directive, DirectiveBinding } from 'vue'

// 扩展 HTMLElement 类型
interface CustomHTMLElement extends HTMLElement {
    _observer?: MutationObserver
}


const loadmore = {
    install: (app: App) => {
        app.directive('loadmore', {
            mounted(el: CustomHTMLElement, binding: DirectiveBinding) {
                let isObserving = false

                // 创建一个观察器实例
                const observer = new MutationObserver(
                    debounce((mutations: MutationRecord[]) => {
                        if (isObserving) return
                        isObserving = true

                        mutations.forEach((mutation) => {
                            if (mutation.addedNodes.length) {
                                // 查找新添加的下拉框
                                const dropdown = document.querySelector('.el-select-dropdown')
                                if (dropdown) {
                                    const wrap = dropdown.querySelector('.el-select-dropdown__wrap')
                                    if (wrap) {
                                        // 移除可能存在的旧监听器
                                        wrap.removeEventListener('scroll', scrollHandler)
                                        // 添加新的监听器
                                        wrap.addEventListener('scroll', scrollHandler)
                                    }
                                }
                            }
                        })

                        isObserving = false
                    }, 200)
                )

                // 滚动处理函数
                const scrollHandler = function (this: HTMLElement) {
                    const condition = this.scrollHeight - this.scrollTop <= this.clientHeight
                    if (condition) {
                        binding.value()
                    }
                }

                // 开始观察body的变化
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                })

                // 在组件卸载时断开观察器
                el._observer = observer
            },
            unmounted(el: CustomHTMLElement) {
                if (el._observer) {
                    el._observer.disconnect()
                }
            }
        })
    }
}

export default loadmore






// 防抖函数
function debounce(fn: Function, delay: number) {
    let timer: number | null = null
    return function (this: any, ...args: any[]) {
        if (timer) clearTimeout(timer)
        timer = setTimeout(() => {
            fn.apply(this, args)
        }, delay) as unknown as number
    }
}
